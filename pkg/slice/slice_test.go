package slice

import (
	"reflect"
	"testing"
)

func TestIntersection(t *testing.T) {
	type args struct {
		slice1 []string
		slice2 []string
	}
	tests := []struct {
		name string
		args args
		want []string
	}{
		{
			name: "",
			args: args{
				slice1: []string{"a", "b", "c", "d"},
				slice2: []string{"a"},
			},
			want: []string{"a"},
		},
		{
			name: "",
			args: args{
				slice1: []string{"a", "b", "c", "d"},
				slice2: []string{""},
			},
			want: []string{},
		},
	}
	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			if got := Intersection(tt.args.slice1, tt.args.slice2); !reflect.DeepEqual(got, tt.want) {
				t.<PERSON>("Intersection() = %v, want %v", got, tt.want)
			}
		})
	}
}

func TestCustomSort(t *testing.T) {
	type args struct {
		arr   []string
		order []string
	}
	tests := []struct {
		name string
		args args
		want []string
	}{
		{
			name: "test1",
			args: args{
				arr: []string{"impressions", "clicks", "impressions_ctr ", "impressions_per_page_view"},
				order: []string{
					"estimated_earnings",
					"page_views",
					"page_views_rpm",
					"impressions",
					"impressions_rpm",
					"ad_requests_coverage",
					"clicks",
					"ad_requests",
					"impressions_ctr",
					"cost_per_click",
					"matched_ad_requests",
					"impressions_per_page_view",
				},
			},
			want: []string{"impressions", "clicks", "impressions_ctr ", "impressions_per_page_view"},
		},
	}
	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			if got := CustomSort(tt.args.arr, tt.args.order); !reflect.DeepEqual(got, tt.want) {
				t.Errorf("CustomSort() = %v, want %v", got, tt.want)
			}
		})
	}
}

func TestRemoveDuplicates(t *testing.T) {
	type args struct {
		arr []string
	}
	tests := []struct {
		name string
		args args
		want []string
	}{
		{
			name: "empty slice",
			args: args{
				arr: []string{},
			},
			want: []string{},
		},
		{
			name: "no duplicates",
			args: args{
				arr: []string{"a", "b", "c"},
			},
			want: []string{"a", "b", "c"},
		},
		{
			name: "with duplicates",
			args: args{
				arr: []string{"a", "b", "a", "c", "b", "a"},
			},
			want: []string{"a", "b", "c"},
		},
		{
			name: "all same values",
			args: args{
				arr: []string{"a", "a", "a", "a"},
			},
			want: []string{"a"},
		},
		{
			name: "with empty strings",
			args: args{
				arr: []string{"", "a", "", "b", ""},
			},
			want: []string{"", "a", "b"},
		},
		{
			name: "with special characters",
			args: args{
				arr: []string{"@", "#", "@", "$", "#", "%"},
			},
			want: []string{"@", "#", "$", "%"},
		},
	}
	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			if got := RemoveDuplicates(tt.args.arr); !reflect.DeepEqual(got, tt.want) {
				t.Errorf("RemoveDuplicates() = %v, want %v", got, tt.want)
			}
		})
	}
}
