package slice

import (
	"sort"
	"strings"
)

func RemoveDuplicates(arr []string) []string {
	uniqueMap := make(map[string]bool)
	for _, str := range arr {
		uniqueMap[str] = true
	}

	uniqueArr := make([]string, 0, len(uniqueMap))
	for str := range uniqueMap {
		uniqueArr = append(uniqueArr, str)
	}

	return uniqueArr
}

func Intersection(slice1, slice2 []string) []string {
	// 创建一个空的map，用于存储slice1中的元素
	set := make(map[string]bool)

	// 遍历slice1，将每个元素作为键插入到map中
	for _, val := range slice1 {
		set[val] = true
	}

	// 创建一个空的切片，用于存储交集结果
	var intersection []string
	intersection = make([]string, 0)

	// 遍历slice2，对于每个元素，检查它是否在map中存在
	// 如果存在，则将该元素添加到交集切片中
	for _, val := range slice2 {
		if set[val] {
			intersection = append(intersection, val)
		}
	}

	// 返回交集切片作为结果
	return intersection
}

// CustomSort 将 arr 的顺序与 order 中定义的顺序对应上
func CustomSort(arr []string, order []string) []string {
	// 创建映射，将预定义顺序中的元素映射为它们的索引
	orderMap := make(map[string]int)
	for index, value := range order {
		orderMap[strings.ReplaceAll(value, " ", "")] = index
	}

	// 自定义排序函数，根据预定义顺序的索引对数组进行排序
	sort.Slice(arr, func(i, j int) bool {
		index1, exists1 := orderMap[strings.ReplaceAll(arr[i], " ", "")]
		index2, exists2 := orderMap[strings.ReplaceAll(arr[j], " ", "")]

		// 如果元素不存在于预定义顺序中，则将其索引设置为 len(order)
		if !exists1 {
			index1 = len(order)
		}
		if !exists2 {
			index2 = len(order)
		}

		return index1 < index2
	})

	return arr
}
