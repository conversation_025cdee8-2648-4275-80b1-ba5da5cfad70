package cast

import "testing"

func TestStringToFloat(t *testing.T) {
	type args struct {
		s string
	}
	tests := []struct {
		name string
		args args
		want float64
	}{
		{
			name: "string to float",
			args: args{
				s: "1.1",
			},
			want: 1.1,
		}, {
			name: "string to float2",
			args: args{
				s: "1.1zdsa",
			},
			want: 0,
		},
	}
	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			defer func() { _ = recover() }()

			if got := StringToFloat(tt.args.s); got != tt.want {
				t.Errorf("StringToFloat() = %v, want %v", got, tt.want)
			}
		})
	}
}

func TestStringToInt(t *testing.T) {
	type args struct {
		s string
	}
	tests := []struct {
		name string
		args args
		want int
	}{
		{
			name: "string to int",
			args: args{
				s: "1",
			},
			want: 1,
		},
		{
			name: "string to int2",
			args: args{
				s: "1zdsa",
			},
			want: 0,
		},
	}
	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			defer func() { _ = recover() }()

			if got := StringToInt(tt.args.s); got != tt.want {
				t.Errorf("StringToInt() = %v, want %v", got, tt.want)
			}
		})
	}
}
