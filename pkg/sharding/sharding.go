/**
* <AUTHOR>
* @Date 2024/2/4
**/

package sharding

import (
	"fmt"
	"time"

	_ "github.com/lib/pq"
)

// GetMonthlyTable 按月份计算分表
func GetMonthlyTable(tableName string, currentTime time.Time) string {
	return fmt.Sprintf("%s_%d%02d", tableName, currentTime.Year(), currentTime.Month())
}

// GetYearlyTables 按年获取月份分表
func GetYearlyTables(table string, year int) []string {
	tables := make([]string, 0)
	// 按月迭代计算分表
	for month := 1; month <= 12; month++ {
		tableName := fmt.Sprintf("%s_%d%02d", table, year, month)
		tables = append(tables, tableName)
	}
	return tables
}

type MonthDate struct {
	Start string
	End   string
}

// 按年份获取每个月的起止UTC(0时区)时间字符串
// Input: year=2023
// Output:map[January:{2022-12-31 16:00:00+00 2023-01-31 16:00:00+00}
//February:{2023-01-31 16:00:00+00 2023-03-03 16:00:00+00}
//March:{2023-02-28 16:00:00+00 2023-03-28 16:00:00+00}
//April:{2023-03-31 16:00:00+00 2023-05-01 16:00:00+00}
//May:{2023-04-30 16:00:00+00 2023-05-30 16:00:00+00}
//June:{2023-05-31 16:00:00+00 2023-07-01 16:00:00+00}
//July:{2023-06-30 16:00:00+00 2023-07-30 16:00:00+00}
//August:{2023-07-31 16:00:00+00 2023-08-31 16:00:00+00}
//September:{2023-08-31 16:00:00+00 2023-10-01 16:00:00+00}
//October:{2023-09-30 16:00:00+00 2023-10-30 16:00:00+00}
//November:{2023-10-31 16:00:00+00 2023-12-01 16:00:00+00}
//December:{2023-11-30 16:00:00+00 2023-12-30 16:00:00+00}]

func GetYearlyMonthDate(year int) map[time.Month]MonthDate {
	months := make(map[time.Month]MonthDate)
	// 设置东八区时区
	utc8 := time.FixedZone("UTC+8", 8*60*60)
	// 遍历每个月
	for month := time.January; month <= time.December; month++ {
		// 获取当前月份的起始时间（东八区）
		start := time.Date(year, month, 1, 0, 0, 0, 0, utc8)
		// 获取下个月的起始时间（东八区）
		end := start.AddDate(0, 1, 0)
		// 将时间格式化为字符串，用于构建 SQL 查询条件
		startStr := start.UTC().Format("2006-01-02 15:04:05-07")
		endStr := end.UTC().Format("2006-01-02 15:04:05-07")
		months[month] = MonthDate{
			Start: startStr,
			End:   endStr,
		}
	}
	return months
}

// GetMonthlyTables 按月迭代计算分表
func GetMonthlyTables(tableName string, startTime time.Time, endTime time.Time) []string {
	tables := make([]string, 0)
	currentTime := startTime
	for currentTime.Before(endTime) || currentTime.Equal(endTime) {
		tabName := fmt.Sprintf("%s_%d%02d", tableName, currentTime.Year(), currentTime.Month())
		tables = append(tables, tabName)
		currentTime = currentTime.AddDate(0, 1, 0) // 增加一个月
	}
	return tables
}
