/**
* <AUTHOR>
* @Date 2024/2/19
**/

package sharding

import (
	"fmt"
	"testing"
	"time"
)

func TestGetYearlyMonthsFirst(t *testing.T) {
	t.Log(GetYearlyMonthDate(2023))
	//sharding_test.go:15: map[January:{2022-12-31 16:00:00+00 2023-01-31 16:00:00+00} February:{2023-01-31 16:00:00+00 2023-03-03 16:00:00+00} March:{2023-02-28 16:00:00+00 2023-03-28 16:00:00+00} April:{2023-03-31 16:00:00+00 2023-05-01 16:00:00+00} May:{2023-04-30 16:00:00+00 2023-05-30 16:00:00+00} June:{2023-05-31 16:00:00+00 2023-07-01 16:00:00+00} July:{2023-06-30 16:00:00+00 2023-07-30 16:00:00+00} August:{2023-07-31 16:00:00+00 2023-08-31 16:00:00+00} September:{2023-08-31 16:00:00+00 2023-10-01 16:00:00+00} October:{2023-09-30 16:00:00+00 2023-10-30 16:00:00+00} November:{2023-10-31 16:00:00+00 2023-12-01 16:00:00+00} December:{2023-11-30 16:00:00+00 2023-12-30 16:00:00+00}]

}

func TestGetYearlyMonthsFirst2(t *testing.T) {
	t.Log(GetYearlyMonthDate(2023))
	//sharding_test.go:19: map[January:{2022-12-31 16:00:00+00 2023-01-31 16:00:00+00} February:{2023-01-31 16:00:00+00 2023-02-28 16:00:00+00} March:{2023-02-28 16:00:00+00 2023-03-31 16:00:00+00} April:{2023-03-31 16:00:00+00 2023-04-30 16:00:00+00} May:{2023-04-30 16:00:00+00 2023-05-31 16:00:00+00} June:{2023-05-31 16:00:00+00 2023-06-30 16:00:00+00} July:{2023-06-30 16:00:00+00 2023-07-31 16:00:00+00} August:{2023-07-31 16:00:00+00 2023-08-31 16:00:00+00} September:{2023-08-31 16:00:00+00 2023-09-30 16:00:00+00} October:{2023-09-30 16:00:00+00 2023-10-31 16:00:00+00} November:{2023-10-31 16:00:00+00 2023-11-30 16:00:00+00} December:{2023-11-30 16:00:00+00 2023-12-31 16:00:00+00}]

}

func TestTime(t *testing.T) {
	//cstSh, _ := time.LoadLocation("Asia/Shanghai")
	//start := time.Date(2023, 1, 1, 17, 0, 0, 0, cstSh)
	//// 获取下个月的起始时间
	//end := start.AddDate(0, 1, 0)
	//// 将时间格式化为字符串，用于构建 SQL 查询条件
	//startStr := start.Format("2006-01-02")
	//endStr := end.Format("2006-01-02")
	//t.Log(startStr, endStr)
	//t.Log(start.String(), end.String())

	//start1 := time.Date(2023, 1, 1, 17, 0, 0, 0, time.UTC)
	//// 获取下个月的起始时间
	//end1 := start.AddDate(0, 1, 0)
	//// 将时间格式化为字符串，用于构建 SQL 查询条件
	//startStr1 := start1.Format("2006-01-02")
	//endStr1 := end1.Format("2006-01-02")
	//t.Log(startStr1, endStr1)
	//t.Log(start1.Location().String(), end1.String())

	cstSh, _ := time.LoadLocation("Asia/Shanghai")
	start := time.Date(2023, 1, 1, 17, 0, 0, 0, cstSh)
	// 获取下个月的起始时间
	end := start.AddDate(0, 1, 0)
	// 将时间格式化为字符串，用于构建 SQL 查询条件
	startStr := start.Format("2006-01-02 15:04:05-07")
	endStr := end.Format("2006-01-02 15:04:05-07")

	t.Log(startStr, endStr)
	t.Log(start.String(), end.String())
}

func TestTimeLocation(t *testing.T) {
	// 设置CST时区
	//cst1 := time.FixedZone("CST", 8*60*60) // 中国标准时间为UTC+8
	cst := time.FixedZone("CST", 0) // 中国标准时间为UTC+8

	// 获取当前时间
	currentTime := time.Now()

	// 将时间转换为CST时区
	cstTime := currentTime.In(cst)

	// 格式化为SQL字符串
	sqlTimeStr := cstTime.Format("2006-01-02 15:04:05-07")

	// 构建SQL语句
	sql := fmt.Sprintf("INSERT INTO table_name (date_column) VALUES ('%s');", sqlTimeStr)

	// 打印SQL语句
	fmt.Println(sql)
}

func TestCST2UTCLocation(t *testing.T) {
	// 设置东八区时区
	utc8 := time.FixedZone("UTC+8", 8*60*60)

	// 获取当前日期（东八区的零点）
	today := time.Date(2023, time.May, 31, 0, 0, 0, 0, utc8)

	// 转换为UTC时区时间
	utcTime := today.UTC()

	// 格式化为指定格式的字符串
	utcTimeString := utcTime.Format("2006-01-02 15:04:05-07")

	// 打印结果
	fmt.Println(utcTimeString)
}

func TestGetYearlyTables(t *testing.T) {
	tests := []struct {
		name     string
		table    string
		year     int
		expected []string
	}{
		{
			name:  "normal case 2024",
			table: "orders",
			year:  2024,
			expected: []string{
				"orders_202401", "orders_202402", "orders_202403",
				"orders_202404", "orders_202405", "orders_202406",
				"orders_202407", "orders_202408", "orders_202409",
				"orders_202410", "orders_202411", "orders_202412",
			},
		},
		{
			name:  "empty table name",
			table: "",
			year:  2024,
			expected: []string{
				"_202401", "_202402", "_202403",
				"_202404", "_202405", "_202406",
				"_202407", "_202408", "_202409",
				"_202410", "_202411", "_202412",
			},
		},
		{
			name:  "year 2000",
			table: "logs",
			year:  2000,
			expected: []string{
				"logs_200001", "logs_200002", "logs_200003",
				"logs_200004", "logs_200005", "logs_200006",
				"logs_200007", "logs_200008", "logs_200009",
				"logs_200010", "logs_200011", "logs_200012",
			},
		},
		{
			name:  "special characters in table name",
			table: "test_table$",
			year:  2025,
			expected: []string{
				"test_table$_202501", "test_table$_202502", "test_table$_202503",
				"test_table$_202504", "test_table$_202505", "test_table$_202506",
				"test_table$_202507", "test_table$_202508", "test_table$_202509",
				"test_table$_202510", "test_table$_202511", "test_table$_202512",
			},
		},
	}

	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			result := GetYearlyTables(tt.table, tt.year)
			if len(result) != len(tt.expected) {
				t.Errorf("GetYearlyTables() length = %v, want %v", len(result), len(tt.expected))
				return
			}
			for i := range result {
				if result[i] != tt.expected[i] {
					t.Errorf("GetYearlyTables() index %d = %v, want %v", i, result[i], tt.expected[i])
				}
			}
		})
	}
}
