package partition

import (
	"reflect"
	"testing"
	"time"
)

func Test_yearMonthTable(t *testing.T) {
	type args struct {
		tableName string
		year      int
		month     int
	}
	tests := []struct {
		name string
		args args
		want string
	}{
		{
			name: "test1",
			args: args{
				tableName: "test",
				year:      2024,
				month:     11,
			},
			want: "test_y2024m11",
		},
	}
	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			if got := yearMonthTable(tt.args.tableName, tt.args.year, tt.args.month); got != tt.want {
				t.Errorf("yearMonthTable() = %v, want %v", got, tt.want)
			}
		})
	}
}

func Test_GetYearlyMonthDate(t *testing.T) {
	tests := []struct {
		name string
		year int
		want map[time.Month]MonthDate
	}{
		{
			name: "year_2024",
			year: 2024,
			want: map[time.Month]MonthDate{
				time.January:   {Start: "2024-01-01", End: "2024-02-01"},
				time.February:  {Start: "2024-02-01", End: "2024-03-01"},
				time.March:     {Start: "2024-03-01", End: "2024-04-01"},
				time.April:     {Start: "2024-04-01", End: "2024-05-01"},
				time.May:       {Start: "2024-05-01", End: "2024-06-01"},
				time.June:      {Start: "2024-06-01", End: "2024-07-01"},
				time.July:      {Start: "2024-07-01", End: "2024-08-01"},
				time.August:    {Start: "2024-08-01", End: "2024-09-01"},
				time.September: {Start: "2024-09-01", End: "2024-10-01"},
				time.October:   {Start: "2024-10-01", End: "2024-11-01"},
				time.November:  {Start: "2024-11-01", End: "2024-12-01"},
				time.December:  {Start: "2024-12-01", End: "2025-01-01"},
			},
		},
		{
			name: "leap_year_2020",
			year: 2020,
			want: map[time.Month]MonthDate{
				time.January:   {Start: "2020-01-01", End: "2020-02-01"},
				time.February:  {Start: "2020-02-01", End: "2020-03-01"},
				time.March:     {Start: "2020-03-01", End: "2020-04-01"},
				time.April:     {Start: "2020-04-01", End: "2020-05-01"},
				time.May:       {Start: "2020-05-01", End: "2020-06-01"},
				time.June:      {Start: "2020-06-01", End: "2020-07-01"},
				time.July:      {Start: "2020-07-01", End: "2020-08-01"},
				time.August:    {Start: "2020-08-01", End: "2020-09-01"},
				time.September: {Start: "2020-09-01", End: "2020-10-01"},
				time.October:   {Start: "2020-10-01", End: "2020-11-01"},
				time.November:  {Start: "2020-11-01", End: "2020-12-01"},
				time.December:  {Start: "2020-12-01", End: "2021-01-01"},
			},
		},
		{
			name: "year_1999",
			year: 1999,
			want: map[time.Month]MonthDate{
				time.January:   {Start: "1999-01-01", End: "1999-02-01"},
				time.February:  {Start: "1999-02-01", End: "1999-03-01"},
				time.March:     {Start: "1999-03-01", End: "1999-04-01"},
				time.April:     {Start: "1999-04-01", End: "1999-05-01"},
				time.May:       {Start: "1999-05-01", End: "1999-06-01"},
				time.June:      {Start: "1999-06-01", End: "1999-07-01"},
				time.July:      {Start: "1999-07-01", End: "1999-08-01"},
				time.August:    {Start: "1999-08-01", End: "1999-09-01"},
				time.September: {Start: "1999-09-01", End: "1999-10-01"},
				time.October:   {Start: "1999-10-01", End: "1999-11-01"},
				time.November:  {Start: "1999-11-01", End: "1999-12-01"},
				time.December:  {Start: "1999-12-01", End: "2000-01-01"},
			},
		},
	}
	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			got := GetYearlyMonthDate(tt.year)
			if !reflect.DeepEqual(got, tt.want) {
				t.Errorf("GetYearlyMonthDate() = %v, want %v", got, tt.want)
			}
		})
	}
}

func Test_LastMonth(t *testing.T) {
	tests := []struct {
		name      string
		tableName string
		saveMonth int
		want      string
	}{
		{
			name:      "save_1_month",
			tableName: "test_table",
			saveMonth: 1,
			want:      yearMonthTable("test_table", time.Now().AddDate(0, -1, 0).Year(), int(time.Now().AddDate(0, -1, 0).Month())),
		},
		{
			name:      "save_12_months",
			tableName: "test_table",
			saveMonth: 12,
			want:      yearMonthTable("test_table", time.Now().AddDate(0, -12, 0).Year(), int(time.Now().AddDate(0, -12, 0).Month())),
		},
		{
			name:      "save_0_months",
			tableName: "test_table",
			saveMonth: 0,
			want:      yearMonthTable("test_table", time.Now().Year(), int(time.Now().Month())),
		},
		{
			name:      "save_negative_months",
			tableName: "test_table",
			saveMonth: -3,
			want:      yearMonthTable("test_table", time.Now().AddDate(0, 3, 0).Year(), int(time.Now().AddDate(0, 3, 0).Month())),
		},
		{
			name:      "empty_table_name",
			tableName: "",
			saveMonth: 1,
			want:      yearMonthTable("", time.Now().AddDate(0, -1, 0).Year(), int(time.Now().AddDate(0, -1, 0).Month())),
		},
	}
	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			if got := LastMonth(tt.tableName, tt.saveMonth); got != tt.want {
				t.Errorf("LastMonth() = %v, want %v", got, tt.want)
			}
		})
	}
}
