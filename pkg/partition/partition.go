/**
* <AUTHOR>
* @Date 2024/2/27
**/

package partition

import (
	"fmt"
	"time"
)

type MonthDate struct {
	Start string
	End   string
}

func GetYearlyMonthDate(year int) map[time.Month]MonthDate {
	months := make(map[time.Month]MonthDate)
	// 设置UTC/东八区时区 UTC+8 ,8*60*60
	utc8 := time.FixedZone("UTC", 0*60*60)
	// 遍历每个月
	for month := time.January; month <= time.December; month++ {
		// 获取当前月份的起始时间（UTC）
		start := time.Date(year, month, 1, 0, 0, 0, 0, utc8)
		// 获取下个月的起始时间（UTC）
		end := start.AddDate(0, 1, 0)
		startStr := start.UTC().Format("2006-01-02")
		endStr := end.UTC().Format("2006-01-02")
		months[month] = MonthDate{
			Start: startStr,
			End:   endStr,
		}
	}
	return months
}

func LastMonth(tableName string, saveMonth int) string {
	currentTime := time.Now().AddDate(0, -saveMonth, 0) // 上一个月
	return yearMonthTable(tableName, currentTime.Year(), int(currentTime.Month()))
}

func yearMonthTable(tableName string, year, month int) string {
	return fmt.Sprintf("%s_y%dm%02d", tableName, year, month)
}
