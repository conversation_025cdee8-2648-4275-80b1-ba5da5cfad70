package date

import (
	"testing"
	"time"
)

func TestYesterday(t *testing.T) {
	now := time.Date(2024, 1, 15, 14, 30, 0, 0, time.UTC)
	start, end := Yesterday(now)

	expectedStart := time.Date(2024, 1, 14, 0, 0, 0, 0, time.UTC)
	expectedEnd := time.Date(2024, 1, 14, 23, 59, 59, 59, time.UTC)

	if !start.Equal(expectedStart) {
		t.<PERSON><PERSON>("Expected start time %v, got %v", expectedStart, start)
	}
	if !end.Equal(expectedEnd) {
		t.<PERSON><PERSON><PERSON>("Expected end time %v, got %v", expectedEnd, end)
	}
}

func TestLast7Days(t *testing.T) {
	now := time.Date(2024, 1, 15, 14, 30, 0, 0, time.UTC)
	start, end := Last7Days(now)

	expectedStart := time.Date(2024, 1, 8, 0, 0, 0, 0, time.UTC)
	expectedEnd := time.Date(2024, 1, 14, 23, 59, 59, 59, time.UTC)

	if !start.Equal(expectedStart) {
		t.<PERSON><PERSON><PERSON>("Expected start time %v, got %v", expectedStart, start)
	}
	if !end.Equal(expectedEnd) {
		t.<PERSON><PERSON><PERSON>("Expected end time %v, got %v", expectedEnd, end)
	}
}

func TestGetDateRange(t *testing.T) {
	now := time.Date(2024, 1, 15, 14, 30, 0, 0, time.UTC)

	tests := []struct {
		name          string
		dateRange     string
		expectedStart time.Time
		expectedEnd   time.Time
	}{
		{
			name:          "YESTERDAY",
			dateRange:     "YESTERDAY",
			expectedStart: time.Date(2024, 1, 14, 0, 0, 0, 0, time.UTC),
			expectedEnd:   time.Date(2024, 1, 14, 23, 59, 59, 59, time.UTC),
		},
		{
			name:          "LAST_15_DAYS",
			dateRange:     "LAST_15_DAYS",
			expectedStart: time.Date(2024, 1, 0, 0, 0, 0, 0, time.UTC),
			expectedEnd:   time.Date(2024, 1, 14, 23, 59, 59, 59, time.UTC),
		},
		{
			name:          "TODAY",
			dateRange:     "TODAY",
			expectedStart: now,
			expectedEnd:   now,
		},
		{
			name:          "INVALID",
			dateRange:     "INVALID",
			expectedStart: time.Now(),
			expectedEnd:   time.Now(),
		},
	}

	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			start, end := GetDateRange(tt.dateRange, now)
			if tt.dateRange != "INVALID" {
				if !start.Equal(tt.expectedStart) {
					t.Errorf("Expected start time %v, got %v", tt.expectedStart, start)
				}
				if !end.Equal(tt.expectedEnd) {
					t.Errorf("Expected end time %v, got %v", tt.expectedEnd, end)
				}
			}
		})
	}
}

func TestLastMonth(t *testing.T) {
	now := time.Date(2024, 2, 15, 14, 30, 0, 0, time.UTC)
	start, end := LastMonth(now)

	expectedStart := time.Date(2024, 1, 1, 0, 0, 0, 0, time.UTC)
	expectedEnd := time.Date(2024, 1, 31, 23, 59, 59, 0, time.UTC)

	if !start.Equal(expectedStart) {
		t.Errorf("Expected start time %v, got %v", expectedStart, start)
	}
	if !end.Equal(expectedEnd) {
		t.Errorf("Expected end time %v, got %v", expectedEnd, end)
	}
}

func TestYearToDate(t *testing.T) {
	now := time.Date(2024, 1, 15, 14, 30, 0, 0, time.UTC)
	start, end := YearToDate(now)

	expectedStart := time.Date(2021, 1, 15, 14, 30, 0, 0, time.UTC)
	expectedEnd := time.Date(2024, 1, 14, 23, 59, 59, 59, time.UTC)

	if !start.Equal(expectedStart) {
		t.Errorf("Expected start time %v, got %v", expectedStart, start)
	}
	if !end.Equal(expectedEnd) {
		t.Errorf("Expected end time %v, got %v", expectedEnd, end)
	}
}

func TestAdjustTimeToPreviousDay(t *testing.T) {
	tests := []struct {
		name           string
		startTime      string
		endTime        string
		expectedStart  string
		expectedEnd    string
		needsFiltering bool
	}{
		{
			name:           "不同天的时间应该调整到前一天16:00:00",
			startTime:      "2025-07-22T10:05:39Z",
			endTime:        "2025-07-23T15:30:00Z",
			expectedStart:  "2025-07-21T16:00:00Z",
			expectedEnd:    "2025-07-22T16:00:00Z",
			needsFiltering: true,
		},
		{
			name:           "同一天的时间不需要过滤",
			startTime:      "2025-07-22T10:05:39Z",
			endTime:        "2025-07-22T15:30:00Z",
			expectedStart:  "2025-07-22T10:05:39Z",
			expectedEnd:    "2025-07-22T15:30:00Z",
			needsFiltering: false,
		},
		{
			name:           "跨月的时间调整",
			startTime:      "2025-08-01T08:00:00Z",
			endTime:        "2025-08-02T12:00:00Z",
			expectedStart:  "2025-07-31T16:00:00Z",
			expectedEnd:    "2025-08-01T16:00:00Z",
			needsFiltering: true,
		},
		{
			name:           "跨年的时间调整",
			startTime:      "2025-01-01T00:00:00Z",
			endTime:        "2025-01-02T23:59:59Z",
			expectedStart:  "2024-12-31T16:00:00Z",
			expectedEnd:    "2025-01-01T16:00:00Z",
			needsFiltering: true,
		},
	}

	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			// 解析输入时间
			startTime, err := time.Parse(time.RFC3339, tt.startTime)
			if err != nil {
				t.Fatalf("解析开始时间失败: %v", err)
			}

			endTime, err := time.Parse(time.RFC3339, tt.endTime)
			if err != nil {
				t.Fatalf("解析结束时间失败: %v", err)
			}

			// 调用函数
			result := AdjustTimeToPreviousDay(startTime, endTime)

			// 验证是否需要过滤
			if result.NeedsFiltering != tt.needsFiltering {
				t.Errorf("NeedsFiltering = %v, 期望 %v", result.NeedsFiltering, tt.needsFiltering)
			}

			// 验证调整后的开始时间
			expectedStart, err := time.Parse(time.RFC3339, tt.expectedStart)
			if err != nil {
				t.Fatalf("解析期望开始时间失败: %v", err)
			}

			if !result.AdjustedStartTime.Equal(expectedStart) {
				t.Errorf("AdjustedStartTime = %v, 期望 %v",
					result.AdjustedStartTime.Format(time.RFC3339),
					expectedStart.Format(time.RFC3339))
			}

			// 验证调整后的结束时间
			expectedEnd, err := time.Parse(time.RFC3339, tt.expectedEnd)
			if err != nil {
				t.Fatalf("解析期望结束时间失败: %v", err)
			}

			if !result.AdjustedEndTime.Equal(expectedEnd) {
				t.Errorf("AdjustedEndTime = %v, 期望 %v",
					result.AdjustedEndTime.Format(time.RFC3339),
					expectedEnd.Format(time.RFC3339))
			}
		})
	}
}

func TestAdjustTimeToPreviousDay_EdgeCases(t *testing.T) {
	// 测试边界情况：同一天但不同时区
	loc, _ := time.LoadLocation("Asia/Shanghai")
	startTime := time.Date(2025, 7, 22, 10, 5, 39, 0, loc)
	endTime := time.Date(2025, 7, 22, 15, 30, 0, 0, loc)

	result := AdjustTimeToPreviousDay(startTime, endTime)

	// 应该不需要过滤，因为是同一天
	if result.NeedsFiltering {
		t.Errorf("同一天的不同时区时间应该不需要过滤")
	}

	// 验证时间被正确转换为UTC
	expectedStart := startTime.UTC()
	expectedEnd := endTime.UTC()

	if !result.AdjustedStartTime.Equal(expectedStart) {
		t.Errorf("开始时间UTC转换不正确")
	}

	if !result.AdjustedEndTime.Equal(expectedEnd) {
		t.Errorf("结束时间UTC转换不正确")
	}
}
