package email

import (
	"bytes"
	"context"
	"crypto/tls"
	"fmt"
	"io"
	"time"

	"github.com/google/wire"
	"gopkg.in/gomail.v2"
	"k8s.io/kubectl/pkg/util/i18n"

	"git.minigame.vip/minicloud/service/adsense-bot/internal/conf"
)

// ProviderSet is email notify providers.
var ProviderSet = wire.NewSet(NewNotifyEmail)

type NotifyEmail struct {
	Host     string
	Port     int
	UserName string
	PassWord string
}

func NewNotifyEmail(email *conf.Email) *NotifyEmail {
	return &NotifyEmail{Host: email.Host, Port: int(email.Port), UserName: email.Username, PassWord: email.Password}
}

func (n *NotifyEmail) SendEmailAddAttach(ctx context.Context, emails, ccEmails []string, reportName, dateRange, sendTime string, data *bytes.Buffer) error {
	m := gomail.NewMessage()
	cstSh, _ := time.LoadLocation("Asia/Shanghai")

	m.SetHeader("From", "<EMAIL>")
	m.SetHeader("To", emails...)
	for _, cc := range ccEmails {
		m.SetAddressHeader("Cc", cc, cc)
	}
	nd := time.Now().In(cstSh).Format(time.DateTime)
	m.SetAddressHeader("From", "<EMAIL>", "微游互娱数据中心")
	m.SetHeader("Subject", fmt.Sprintf("%s#%s by 微游互娱", reportName, nd))
	m.SetBody("text/html; charset=UTF-8", fmt.Sprintf("报表： %s<br /> 数据时间跨度:  %s<br /> 数据周期:  %s<br /> 报表制作时间:  %s<br /> 报表制作：微游互娱数据中心<br /><br /> <br />  特别申明：<br /> 本报告数据来源于谷歌AdSense等变现平台，但是不代表最终结算金额，仅做运营参考，不做对账依据。<br /> 微游互娱对此数据报表数据有最终解释权。<br /> 有任何疑问，请联系微游互娱相关人员。", reportName, i18n.T(dateRange), i18n.T(sendTime), nd))
	all, err := io.ReadAll(data)
	if err != nil {
		return err
	}
	m.Attach("Report.zip", gomail.SetCopyFunc(func(w io.Writer) error {
		_, err := w.Write(all)
		return err
	}))
	d := gomail.NewDialer(n.Host, n.Port, n.UserName, n.PassWord)
	d.TLSConfig = &tls.Config{InsecureSkipVerify: true}
	// Send the email to Bob, Cora and Dan.
	if err := d.DialAndSend(m); err != nil {
		return err
	}
	return nil
}
func (n *NotifyEmail) SendEmail(ctx context.Context, emails []string, reportName, dateRange, sendTime string, data *bytes.Buffer) error {
	m := gomail.NewMessage()
	m.SetHeader("From", "<EMAIL>")
	m.SetHeader("To", emails...)
	cstSh, _ := time.LoadLocation("Asia/Shanghai")

	nd := time.Now().In(cstSh).Format(time.DateTime)
	m.SetAddressHeader("From", "<EMAIL>", "微游互娱数据中心")
	m.SetHeader("Subject", fmt.Sprintf("%s#%s by 微游互娱", reportName, nd))
	m.SetBody("text/html; charset=UTF-8", fmt.Sprintf("报表： %s<br /> 数据时间跨度:  %s<br /> 数据周期:  %s<br /> 报表制作时间:  %s<br /> 报表制作：微游互娱数据中心<br /><br /> <br />  特别申明：<br /> 本报告数据来源于谷歌AdSense等变现平台，但是不代表最终结算金额，仅做运营参考，不做对账依据。<br /> 微游互娱对此数据报表数据有最终解释权。<br /> 有任何疑问，请联系微游互娱相关人员。", reportName, i18n.T(dateRange), i18n.T(sendTime), nd))
	all, err := io.ReadAll(data)
	if err != nil {
		return err
	}
	m.Attach("report.xlsx", gomail.SetCopyFunc(func(w io.Writer) error {
		_, err := w.Write(all)
		return err
	}))
	d := gomail.NewDialer(n.Host, n.Port, n.UserName, n.PassWord)
	d.TLSConfig = &tls.Config{InsecureSkipVerify: true}
	if err := d.DialAndSend(m); err != nil {
		return err
	}
	return nil
}
func (n *NotifyEmail) SendChannelGaEmail(emails []string, reportName string, data *bytes.Buffer) error {
	m := gomail.NewMessage()
	m.SetHeader("From", "<EMAIL>")
	m.SetHeader("To", emails...)

	m.SetAddressHeader("From", "<EMAIL>", "微游互娱数据中心")
	m.SetHeader("Subject", reportName)
	all, err := io.ReadAll(data)
	if err != nil {
		return err
	}
	m.Attach(reportName+".xlsx", gomail.SetCopyFunc(func(w io.Writer) error {
		_, err := w.Write(all)
		return err
	}))
	d := gomail.NewDialer(n.Host, n.Port, n.UserName, n.PassWord)
	d.TLSConfig = &tls.Config{InsecureSkipVerify: true}
	if err := d.DialAndSend(m); err != nil {
		return err
	}
	return nil
}
