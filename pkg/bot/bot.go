package bot

import (
	"github.com/google/wire"
	"github.com/xen0n/go-workwx"

	"git.minigame.vip/minicloud/service/adsense-bot/internal/conf"
)

// ProviderSet is data providers.
var ProviderSet = wire.NewSet(NewBotClient)

type Client struct {
	client *workwx.WebhookClient
}

func NewBotClient(bot *conf.Bot) (*Client, error) {
	client := workwx.NewWebhookClient(bot.Key)
	return &Client{client: client}, nil
}

func (c *Client) SendText(text string) (err error) {
	err = c.client.SendTextMessage(text, &workwx.Mentions{})
	if err != nil {
		return err
	}
	return nil
}

func (c *Client) SendMarkdown(markdown string) (err error) {
	err = c.client.SendMarkdownMessage(markdown)
	if err != nil {
		return err
	}
	return nil
}
