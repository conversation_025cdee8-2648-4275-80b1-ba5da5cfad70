package i18n

import (
	"github.com/chai2010/gettext-go"
	"k8s.io/kubectl/pkg/util/i18n"
)

func init() {
	if err := i18n.SetLoadTranslationsFunc(loadCustomTranslations); err != nil {
		panic(err)
	}
}

func loadCustomTranslations() error {
	//gettext.OS("D:\\adsense-bot\\pkg\\i18n\\locales")
	//gettext.OS("./locales")
	gettext.BindLocale(gettext.New("countries", "locales", gettext.OS("./locales")))
	//gettext.SetDomain("countries")
	gettext.SetLanguage("zh_CN")
	return nil
}
