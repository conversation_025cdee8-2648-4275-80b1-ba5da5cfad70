package i18n

import (
	"context"
	"github.com/go-kratos/kratos/v2/errors"
	"github.com/go-kratos/kratos/v2/middleware"
	"github.com/go-kratos/kratos/v2/transport"
)

type i18nKey struct{}

const (
	i18n string = "i18n"
)

var (
	ErrWrongContext = errors.Unauthorized("Bearer", "Wrong context for middleware")
)

// Server 以下中间件废弃使用，因为将i18n放在header中，后续无法针对多语言接口做cdn。
func Server() middleware.Middleware {
	return func(handler middleware.Handler) middleware.Handler {
		return func(ctx context.Context, req interface{}) (interface{}, error) {
			if header, ok := transport.FromServerContext(ctx); ok {
				language := header.RequestHeader().Get(i18n)

				ctx = NewContext(ctx, language)
				return handler(ctx, req)
			}
			return nil, ErrWrongContext
		}
	}
}

// NewContext put auth info into context
func NewContext(ctx context.Context, language string) context.Context {
	return context.WithValue(ctx, i18nKey{}, language)
}

// FromContext extract auth info from context
func FromContext(ctx context.Context) (language string, ok bool) {
	language, ok = ctx.Value(i18nKey{}).(string)
	return
}
