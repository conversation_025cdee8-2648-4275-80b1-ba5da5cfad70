package cron

import (
	"context"
	"github.com/robfig/cron/v3"
	"reflect"
	"time"
)

type Job struct {
	Name     string
	Schedule string
	Input    string
	Output   string
}

type JobMethod func(ctx context.Context) error

func (w *Scheduler) getMethod(methodName string, s any) (f JobMethod) {
	val := reflect.ValueOf(s)
	typ := reflect.TypeOf(s)
	for i := 0; i < val.NumMethod(); i++ {
		method := val.Method(i)
		methodType := typ.Method(i)
		if methodType.Name == methodName {
			f = method.Interface().(func(ctx context.Context) error)
			break
		}
	}
	return
}

type Scheduler struct {
	cron *cron.Cron
}

func NewCronScheduler(jobs []Job, s any) (w *Scheduler) {
	loc, err := time.LoadLocation("Asia/Shanghai")
	if err != nil {
		panic(err)
	}
	w = &Scheduler{
		cron: cron.New(cron.WithLocation(loc)),
	}

	for _, j := range jobs {
		if f := w.getMethod(j.Name, s); f != nil {
			_, err := w.cron.AddFunc(j.Schedule, func() {
				err := f(context.TODO())
				if err != nil {
					return
				}
			})
			if err != nil {
				panic(err)
			}
		} else {
			panic("can not find method : " + j.Name)
		}
	}
	return
}

func (w *Scheduler) Start(c context.Context) error {
	w.cron.Start()
	return nil
}

func (w *Scheduler) Stop(c context.Context) error {
	w.cron.Stop()
	return nil
}
