syntax = "proto3";

package api.deploy.v1;

option go_package = "minigame.vip/minicloud/channel-service/api/deploy/v1;v1";
option java_multiple_files = true;
option java_package = "api.tool.v1";
import "google/api/annotations.proto";
import "google/protobuf/empty.proto";
service DeployService {
    rpc DeployV1Center (DeployV1CenterRequest) returns (.google.protobuf.Empty) {
        option (google.api.http) = {
            post: "/v1/deploy/version/v1"
            body: "*"
        };
    }
    rpc DownV1Center (DownV1CenterRequest) returns (.google.protobuf.Empty) {
        option (google.api.http) = {
            post: "/v1/deploy/down_game"
            body: "*"
        };
    }
    rpc UpdateDeploy(UpdateDeployRequest) returns (.google.protobuf.Empty) {
        option (google.api.http) = {
            post: "/v1/deploy/update"
            body: "*"
        };
    }
}

message DownV1CenterRequest {
    // 商户名称
    string name = 1;

    string host = 2;
}


message DeployV1CenterRequest {
    // 域名
    string host = 1;
    // 商户名称
    string name = 2;
    // 关联的模板id 为0则不关联模板
    uint32 template_id = 3;
    // HomePage 为空九宫格 game_box推荐页
    string home_page = 4;
    // cdn类型
    uint32 cdn_type = 5;
    // 是否创建渠道
    bool is_create_channel = 6;
}

message UpdateDeployRequest {
    // 域名
    string host = 1;
    // 商户名称
    string name = 2;
    // 关联的模板id 为0则不关联模板
    uint32 template_id = 3;
    // HomePage 为空九宫格 game_box推荐页
    string home_page = 4;
    // cdn类型
    uint32 cdn_type = 5;
    // 是否创建渠道
    bool is_create_channel = 6;
    bool is_delete_channel = 7;
}