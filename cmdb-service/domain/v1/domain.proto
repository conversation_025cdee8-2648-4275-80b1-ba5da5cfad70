syntax = "proto3";

package api.domain.v1;

option go_package = "git.minigame.vip/minicloud/minigame-cmdb-service/api/domain/v1;v1";
option java_multiple_files = true;
option java_package = "api.domain.v1";

import "google/api/annotations.proto";
import "google/protobuf/struct.proto";
import "open-proto/cmdb-service/domain/v1/domain_inter.proto";

service Domain {
  // 搜索域名
  rpc ListDomain(ListDomainRequest) returns (ListDomainReply) {
    option (google.api.http) = {
      get : "/v1/domain/list"
    };
  }

  // 添加域名
  rpc AddDomain(AddDomainRequest) returns (AddDomainReply) {
    option (google.api.http) = {
      post : "/v1/domain/{domain}"
      body : "*"
    };
  }

  // 删除域名
  rpc DeleteDomain(DeleteDomainRequest) returns (DeleteDomainReply) {
    option (google.api.http) = {
      delete : "/v1/domain/{domain}"
    };
  }

  // 更新域名信息
  rpc UpdateDomain(UpdateDomainRequest) returns (UpdateDomainReply) {
    option (google.api.http) = {
      patch : "/v1/domain/{domain}",
      body : "*"
    };
  }

  // 部署域名
  rpc DeployDomain(DeployDomainRequest) returns (DeployDomainReply) {
    option (google.api.http) = {
      post : "/v1/domain/{domain}/deploy"
    };
  }

  // 获取所有域名的证书过期时间（deprecated)
  rpc GetAllDomainsNotAfter(GetAllDomainsNotAfterRequest)
      returns (GetAllDomainsNotAfterReply) {
    option (google.api.http) = {
      get : "/v1/domains/not_after"
    };
  }

  // 获取域名的证书
  rpc GetDomainCert(GetDomainCertRequest) returns (GetDomainCertReply) {
    option (google.api.http) = {
      get : "/v1/domain/{domain}/cert",
    };
  }

  // 部署域名的证书
  rpc DeployDomainCert(DeployDomainCertRequest)
      returns (DeployDomainCertReply) {
    option (google.api.http) = {
      post : "/v1/domain/{domain}/cert",
    };
  }

  // 刷新阿里云cdn
  rpc RefreshAliCloudCdnCache(RefreshAliCloudCdnCacheRequest)
      returns (RefreshAliCloudCdnCacheReply) {
    option (google.api.http) = {
      post : "/v1/alicloud/cdn/refresh",
      body : "*"
    };
  }

  // 获取阿里云cdn状态
  rpc GetAllAliCloudCdnStatus(GetAllAliCloudCdnStatusRequest)
      returns (GetAllAliCloudCdnStatusReply) {
    option (google.api.http) = {
      get : "/v1/domains/alicloud_cdn_status",
    };
  }

  // 添加阿里云cdn
  rpc AddAliCloudCdnDomain(AddAliCloudCdnDomainRequest)
      returns (AddAliCloudCdnDomainReply) {
    option (google.api.http) = {
      post : "/v1/domain/{domain}/alicloud/cdn"
    };
  }

  // 获取cdn刷新配额
  rpc GetAliCloudCdnRefreshQuota(GetAliCloudCdnRefreshQuotaRequest)
      returns (GetAliCloudCdnRefreshQuotaReply) {
    option (google.api.http) = {
      get : "/v1/alicloud/cdn/refresh/quota",
    };
  }

  // 获取阿里云cdn刷新任务列表
  rpc GetAliCloudCdnRefreshTasks(GetAliCloudCdnRefreshTasksRequest)
      returns (GetAliCloudCdnRefreshTasksReply) {
    option (google.api.http) = {
      get : "/v1/alicloud/cdn/refresh/tasks",
    };
  }

  // 添加apisix服务映射
  rpc AddApiSixServiceMap(AddApiSixServiceMapRequest)
      returns (AddApiSixServiceMapReply) {
    option (google.api.http) = {
      post : "/v1/apisix/service_map",
      body : "*",
    };
  }

  // 删除apisix服务映射
  rpc DeleteApisixServiceMap(DeleteApisixServiceMapRequest)
      returns (DeleteApisixServiceMapReply) {
    option (google.api.http) = {
      delete : "/v1/apisix/service_map/{service_map_id}",
    };
  }

  // 更新apisix服务映射
  rpc UpdateApisixServiceMap(UpdateApisixServiceMapRequest)
      returns (UpdateApisixServiceMapReply) {
    option (google.api.http) = {
      patch : "/v1/apisix/service_map/{service_map_id}",
      body : "*",
    };
  }

  // 列出apisix服务映射列表
  rpc ListApisixServiceMaps(ListApisixServiceMapsRequest)
      returns (ListApisixServiceMapsReply) {
    option (google.api.http) = {
      get : "/v1/apisix/service_maps",
    };
  }

  // 推送apisix服务映射
  rpc PushApisixServiceMap(PushApisixServiceMapRequest)
      returns (PushApisixServiceMapReply) {
    option (google.api.http) = {
      post : "/v1/apisix/service_map/{service_map_id}/push",
    };
  }

  // 拉取apisix服务映射
  rpc PullApisixServiceMap(PullApisixServiceMapRequest)
      returns (PullApisixServiceMapReply) {
    option (google.api.http) = {
      post : "/v1/apisix/service_map/{service_map_id}/pull",
    };
  }

  // 从apisix中快速添加服务映射
  rpc AddServiceMapFromApisix(AddServiceMapFromApisixRequest)
      returns (AddServiceMapFromApisixReply) {
    option (google.api.http) = {
      post : "/v1/apisix/route/{route_id}/pull",
    };
  }

  // 获取服务映射的日志
  rpc GetServiceMapLogs(GetServiceMapLogsRequest)
      returns (GetServiceMapLogsReply) {
    option (google.api.http) = {
      get : "/v1/apisix/service_map/logs",
    };
  }

  // 刷新域名的证书过期时间
  rpc RefreshDomainCertNotAfter(RefreshDomainCertNotAfterRequest)
      returns (RefreshDomainCertNotAfterReply) {
    option (google.api.http) = {
      post : "/v1/domain/{domain}/cert/refresh",
    };
  }

  // 快速部署域名 DNS+CDN+TLS
  rpc QuickDeployDomain(QuickDeployDomainRequest) 
      returns (QuickDeployDomainReply) {
    option (google.api.http) = {
      post : "/v1/domain/{domain}/quick-deploy",
    };
  }
  
  // 绑定host到路由id
  rpc BindHostToRoute(BindHostToRouteRequest)
      returns (BindHostToRouteReply) {
    option (google.api.http) = {
      post : "/v1/domain/{domain}/route/{route}/bind",
    };
  }
  
  // 根据路由id解绑host
  rpc UnbindHostFromRoute(UnbindHostFromRouteRequest)
      returns (UnbindHostFromRouteReply) {
    option (google.api.http) = {
      post : "/v1/domain/{domain}/route/{route}/unbind",
    };
  }
  // 查询cdn域名详情
  rpc DescribeCdnDomainDetail(DescribeCdnDomainDetailRequest)
      returns (DescribeCdnDomainDetailReply) {
    option (google.api.http) = {
      post : "/v1/domain/{domain}/cdn/detail",
    };
  }

  rpc ListAllExactDnsRecords(ListAllExactDnsRecordsRequest)
      returns (ListAllExactDnsRecordsReply) {
    option (google.api.http) = {
      post : "/v1/domain/{domain}/dns/records",
    };
  }
  
  rpc ExistHostInRoute(ExistHostInRouteRequest)
      returns (ExistHostInRouteReply) {
    option (google.api.http) = {
      post : "/v1/domain/{domain}/exist/route/{route}",
    };
  }

}

message ExistHostInRouteRequest{
  // 域名
  string domain = 1;
  // 路由id
  string route = 2;
}

message ExistHostInRouteReply{
  // 是否存在
  bool exist = 1;
}


message UnbindHostFromRouteRequest {
  // 域名
  string domain = 1;
  // 路由id
  string route = 2;
}

message UnbindHostFromRouteReply {}
message BindHostToRouteRequest {
  // 域名
  string domain = 1;
  // 路由id
  string route = 2;
}

message BindHostToRouteReply {}

message QuickDeployDomainRequest{
  // 域名
  string domain = 1;
  // 域名的相关描述
  string desc = 2;
}

message QuickDeployDomainReply{}

message GetServiceMapLogsRequest {}

message GetServiceMapLogsReply { repeated google.protobuf.Struct apisix = 1; }

message DeleteApisixRouteRequest {
  // apisix路由id
  string route_id = 1;
}

message DeleteApisixRouteRequestReply {}

message AddServiceMapFromApisixRequest {
  // apisix路由id
  string route_id = 1;
}

message AddServiceMapFromApisixReply {}

message PullApisixServiceMapRequest {
  // 服务映射id
  int32 service_map_id = 1;
}

message PullApisixServiceMapReply {}

message PushApisixServiceMapRequest {
  // 服务映射id
  int32 service_map_id = 1;
}

message PushApisixServiceMapReply {}

message ListApisixServiceMapsRequest {}

message ListApisixServiceMapsReply {
  message ServiceMap {
    // 服务映射id
    int32 id = 1;
    // apisix路由id
    string apisix_route_id = 2;
    // 名称
    string name = 3;
    // 描述
    string desc = 4;
    // uris
    repeated string uris = 5;
    // hosts
    repeated string hosts = 6;
    // 上游host
    string upstream_host = 7;
    // 上游端口
    int32 upstream_port = 8;
  }
  // 服务映射列表
  repeated ServiceMap results = 1;
}

message UpdateApisixServiceMapRequest {
  // uris
  repeated string uris = 1;
  // 名称
  string name = 2;
  // 描述
  string desc = 3;
  // hosts
  repeated string hosts = 4;
  // 上游host
  string upstream_host = 5;
  // 上游端口
  int32 upstream_port = 6;
  // 服务映射id
  int32 service_map_id = 7;
}

message UpdateApisixServiceMapReply {}

message DeleteApisixServiceMapRequest {
  // 服务映射id
  int32 service_map_id = 1;
}

message DeleteApisixServiceMapReply {}

message AddApiSixServiceMapRequest {
  // uris
  repeated string uris = 1;
  // 名称
  string name = 2;
  // 描述
  string desc = 3;
  // hosts
  repeated string hosts = 4;
  // 上游host
  string upstream_host = 5;
  // 上游端口
  int32 upstream_port = 6;
}

message AddApiSixServiceMapReply {}

message AddAliCloudCdnDomainRequest {
  // 域名
  string domain = 1;
}

message AddAliCloudCdnDomainReply {}

message ListDomainRequest {
  // 页码
  optional uint32 page_number = 1;
  // 大小
  optional uint32 page_size = 2;
  // 名称
  optional string name = 3;
  // 域名
  optional string domain_name = 4;
  // 后端类型 aliyun_cdn apisix
  optional string backend = 5;
  // 描述
  optional string desc = 6;
}

message ListDomainReply {
  message Domain {
    // 名称
    string name = 1;
    // 域名
    string domain = 2;
    // 后端服务 apisix aliyun_cdn
    string backend = 3;
    // 描述
    string desc = 4;
    // 过期时间
    string not_after = 5;
  }
  // 域名列表
  repeated Domain domains = 1;
  // 总数
  uint32 total_count = 2;
}

message AddDomainRequest {
  // 域名
  string domain = 1;
  // 后端服务 apisix aliyun_cdn
  string backend = 2;
  // 名称
  string name = 3;
  // 描述
  string desc = 4;
  // 证书签发机构
  // 0、未知 1、Let's Encrypt 2、ZeroSSL
  optional uint32 issuer = 5;
}

message AddDomainReply {}

message DeployDomainRequest {
  // 域名
  string domain = 1;
}

message DeployDomainReply {}

message DeleteDomainRequest {
  // 域名
  string domain = 1;
}

message DeleteDomainReply {}

message UpdateDomainRequest {
  // 域名
  string domain = 1;
    // 后端服务 apisix aliyun_cdn
  string backend = 2;
  // 名称
  string name = 3;
  // 描述
  string desc = 4;
  // 证书签发机构
  // 0、未知 1、Let's Encrypt 2、ZeroSSL
  optional uint32 issuer = 5;
}

message UpdateDomainReply {}
