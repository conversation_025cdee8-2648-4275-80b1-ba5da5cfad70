syntax = "proto3";

package api.domain.v1;

option go_package = "git.minigame.vip/minicloud/minigame-cmdb-service/api/domain/v1;v1";
option java_multiple_files = true;
option java_package = "api.domain.v1";

import "google/api/annotations.proto";

service DomainInter {
  // 部署域名
  // 设置cdn、dns
  // 签发证书
  // 上传证书
  rpc DeployDomainCert(DeployDomainCertRequest)
      returns (DeployDomainCertReply) {
    option (google.api.http) = {
      post : "/v1/domain/{domain}/cert",
    };
  }

  // 获取域名证书
  rpc GetDomainCert(GetDomainCertRequest) returns (GetDomainCertReply) {
    option (google.api.http) = {
      get : "/v1/domain/{domain}/cert",
    };
  }

  // 获取所有域名的过期日期
  rpc GetAllDomainsNotAfter(GetAllDomainsNotAfterRequest)
      returns (GetAllDomainsNotAfterReply) {
    option (google.api.http) = {
      get : "/v1/domains/not_after",
    };
  }

  // 立即执行每日检查
  rpc AdminCheckNow(AdminCheckNowRequest) returns (AdminCheckNowReply) {
    option (google.api.http) = {
      get : "/v1/domain/admin/check_now",
    };
  }

  // 创建阿里云cdn
  rpc CreateAliCloudCdn(CreateAliCloudCdnRequest)
      returns (CreateAliCloudCdnReply) {
    option (google.api.http) = {
      post : "/v1/domain/{domain}/alicloud/cdn",
      body : "*",
    };
  }

  // 创建阿里云dns记录
  rpc CreateAliCloudDns(CreateAliCloudDnsRequest)
      returns (CreateAliCloudDnsReply) {
    option (google.api.http) = {
      post : "/v1/domain/{domain}/alicloud/dns",
      body : "*",
    };
  }

  // 获取阿里云cdn的所有状态
  rpc GetAllAliCloudCdnStatus(GetAllAliCloudCdnStatusRequest)
      returns (GetAllAliCloudCdnStatusReply) {
    option (google.api.http) = {
      get : "/v1/domains/alicloud_cdn_status",
    };
  }

  // 刷新阿里云cdn缓存
  rpc RefreshAliCloudCdnCache(RefreshAliCloudCdnCacheRequest)
      returns (RefreshAliCloudCdnCacheReply) {
    option (google.api.http) = {
      post : "/v1/alicloud/cdn/refresh",
      body : "*"
    };
  }

  // 获取cdn每日刷新用量
  rpc GetAliCloudCdnRefreshQuota(GetAliCloudCdnRefreshQuotaRequest)
      returns (GetAliCloudCdnRefreshQuotaReply) {
    option (google.api.http) = {
      get : "/v1/alicloud/cdn/refresh/quota",
    };
  }

  // 获取阿里云cdn刷新的任务记录
  rpc GetAliCloudCdnRefreshTasks(GetAliCloudCdnRefreshTasksRequest)
      returns (GetAliCloudCdnRefreshTasksReply) {
    option (google.api.http) = {
      get : "/v1/alicloud/cdn/refresh/tasks",
    };
  }

  rpc RefreshDomainCertNotAfter(RefreshDomainCertNotAfterRequest)
      returns (RefreshDomainCertNotAfterReply) {
    option (google.api.http) = {
      post : "/v1/domain/{domain}/cert/refresh",
    };
  }
  
  rpc QuickDeployCDNDomain(QuickDeployCDNDomainRequest)
      returns (QuickDeployCDNDomainReply) {
    option (google.api.http) = {
      post : "/v1/domain/{domain}/quick-deploy",
    };
  }

  rpc DescribeCdnDomainDetail(DescribeCdnDomainDetailRequest)
      returns (DescribeCdnDomainDetailReply) {
    option (google.api.http) = {
      post : "/v1/domain/{domain}/cdn/detail",
    };
  }

  rpc ListAllExactDnsRecords(ListAllExactDnsRecordsRequest)
      returns (ListAllExactDnsRecordsReply) {
    option (google.api.http) = {
      post : "/v1/domain/{domain}/dns/records",
    };
  }
}

message ListAllExactDnsRecordsRequest {
  string domain = 1;
  string keyword = 2;
}

message ListAllExactDnsRecordsReply {
  repeated DnsRecord records = 1;
}

message DnsRecord {
  // 状态
  string status = 1;
  // 类型 A/CNAME等
  string type = 2;
  // TTL
  int64 ttl = 3;
  // 描述
  string record_id = 4;
  string rr = 5;
  string domain_name =6;
  string value =7;
}


message DescribeCdnDomainDetailRequest {
  // 域名
  string domain = 1;
}

message DescribeCdnDomainDetailReply {
  CdnDomainDetail detail = 1;
}

message CdnDomainDetail{
  // 域名
  string DomainName = 1;
  // 域名状态
  string DomainStatus =2;
  // 描述
  string Description =3;
  // HTTPS
  string ServerCertificateStatus = 4;
  // CNAME
  string Cname = 5;
  // 创建时间
  string GmtCreated = 6;
  // 修改时间
  string GmtModified = 7;
}

message QuickDeployCDNDomainRequest {
  // 域名
  string domain = 1;
  // 源站ip
  string source_ip = 2;
  // 源站端口
  int32 port = 3;
}

message QuickDeployCDNDomainReply {}

message RefreshDomainCertNotAfterRequest { string domain = 1; }

message RefreshDomainCertNotAfterReply { string not_after = 1; }

message GetAliCloudCdnRefreshTasksRequest {
  // 需要完整路径，如 https://agw.minigame.vip/v2/api/cid/3182/games/stick-hero
  optional string object_path = 1;
  // 2017-12-21T08:00:00Z
  optional string start_time = 2;
  // 2017-12-22T08:00:00Z
  optional string end_time = 6;
  // 页码
  optional uint32 page_number = 3;
  // 每页大小
  optional uint32 page_size = 4;
  // 仅支持单个查询，默认放空为查询所有域名。
  optional string domain_name = 5;
  // 当指定 DomainName时，ObjectType 参数为必传参数。 类型：file directory regex
  // preload
  optional string object_type = 7;
}

message GetAliCloudCdnRefreshTasksReply {
  message CdnRefreshTask {
    // 路径
    string path = 1;
    // 类型
    string type = 2;
    // 状态
    string status = 3;
    // 任务发起时间
    string create_time = 4;
    // 进度
    string process = 5;
  }
  repeated CdnRefreshTask tasks = 1;
  uint64 total = 2;
}

message GetAliCloudCdnRefreshQuotaRequest {}

message GetAliCloudCdnRefreshQuotaReply {
  // 剩余url刷新数
  int32 url_remain = 1;
  // 剩余目录刷新数
  int32 dir_remain = 2;
}

message RefreshAliCloudCdnCacheRequest {
  // 路径
  string path = 1;
  // 类型
  string type = 2;
  // 是否强制刷新
  bool force = 3;
}

message RefreshAliCloudCdnCacheReply {}

message GetDomainCertRequest { string domain = 1; }

message GetDomainCertReply {
  // 证书
  string crt = 1;
  // 密钥
  string key = 2;
  // 域名
  string domain = 3;
}

message GetAllAliCloudCdnStatusRequest {
  // 域名
  string domain = 1;
  int32 page = 2;
  int32 page_size=3;
}

message GetAllAliCloudCdnStatusReply {
  message CdnStatus {
    // 域名
    string domain = 1;
    // 状态
    string status = 2;
  }

  repeated CdnStatus domains = 1;
  int32 total = 2;
}
message CreateAliCloudCdnRequest {
  // 域名
  string domain = 1;
  // 源站ip
  string source_ip = 2;
  // 源站端口
  int32 port = 3;
}

message CreateAliCloudCdnReply {}

message CreateAliCloudDnsRequest {
  // 域名
  string domain = 1;
  // 类型 A/CNAME
  string type = 2;
  // 值
  string value = 3;
}

message CreateAliCloudDnsReply {}

message DeployDomainCertRequest {
  // 域名
  string domain = 1;
}

message DeployDomainCertReply {}

message GetAllDomainsNotAfterRequest {}

message GetAllDomainsNotAfterReply {
  message DomainNotAfterResult {
    // 域名
    string domain = 1;
    // 过期时间
    string not_after = 2;
    // 是否成功
    bool success = 3;
  }

  repeated DomainNotAfterResult results = 1;
}

message AdminCheckNowRequest {}
message AdminCheckNowReply {}
