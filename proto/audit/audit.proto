syntax = "proto3";

package audit.v1;

option go_package = "/;audit";

import "google/api/annotations.proto";
import "google/protobuf/timestamp.proto";

// 操作审计
service Audit {
  rpc Create(CreateRequest) returns (CreateReply) {
    option (google.api.http) = {
      post : "/v1/audit",
      body : "*"
    };
  }
  rpc Search(SearchRequest) returns (SearchReply) {
    option (google.api.http) = {
      get : "/v1/audit/search"
    };
  }
}

message CreateRequest {
  string method = 1;
  string path = 2;
  string body = 3;
  string operator_id = 4;
}

message CreateReply {}

message SearchRequest {
  int32 page = 1;
  int32 page_size = 2;
  optional string path = 3;
  optional string name = 4;
}

message SearchReply {
  message AuditItem {
    string method = 1;
    string path = 2;
    string body = 3;
    string name = 4;
    google.protobuf.Timestamp create_at = 5;
  }
  repeated AuditItem items = 1;
}