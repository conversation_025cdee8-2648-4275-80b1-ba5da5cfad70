syntax = "proto3";

package api.minigame.cas.service.v1.user;

import "google/api/annotations.proto";
import "validate/validate.proto";
import "google/protobuf/empty.proto";
import "google/protobuf/timestamp.proto";
option go_package = "git.minigame.vip/service/minigame-cas-service/api/minigame-cas-service/v1;v1";

service UserService {
  rpc GetUserInfo(.google.protobuf.Empty) returns (User) {
    option (google.api.http) = {
      get: "/v1/cas/user"
    };
  }
  rpc ListUser (ListUserRequest) returns (ListUserReply) {
    option (google.api.http) = {
      get: "/v1/cas/users"
    };
  };
  rpc GetUserInfoInter(GetUserInfoInterRequest) returns (User) {}
  rpc ListUserInter (ListUserInterRequest) returns (ListUserReply) {};
  rpc BatchGetUserInfoInter(BatchGetUserInfoInterRequest) returns (BatchGetUserInfoInterReply) {}
  rpc LogoutInter(LogoutInterRequest) returns (.google.protobuf.Empty){};
}

message BatchGetUserInfoInterRequest {
  repeated uint64 uids = 1;
  string sign = 2;
}

message BatchGetUserInfoInterReply {
  repeated User users = 1;
}


message LogoutInterRequest {
  int64 uid = 1;
  string sign = 2;
}
message ListUserInterRequest {
  int32 page = 1;
  int32 page_size = 2;
  string sign = 3;
}

message GetUserInfoInterRequest {
  int64 uid = 1;
  string sign = 2;
}

message User {
  int64 id = 1;
  string email = 2[(validate.rules).string = {
    pattern:  "(?m)^(([^<>()[\\]\\\\.,;:\\s@\"]+(\\.[^<>()[\\]\\\\.,;:\\s@\"]+)*)|(\".+\"))@(minigame\\.vip)$"
    min_bytes: 6,
    max_bytes: 256,
  }];
  string real_name = 3;
  string name = 4;
  int32 state = 5;
  // 上次登录时间
  .google.protobuf.Timestamp last_login_time = 6;
  // 上次登录ip
  string last_login_ip = 7;
  .google.protobuf.Timestamp created_at = 8;
  .google.protobuf.Timestamp updated_at = 9;
  .google.protobuf.Timestamp last_logout_time = 10;
}


message ListUserRequest {
  int32 page = 1;
  int32 page_size = 2;
}
message ListUserReply {
  repeated User data = 1;
  int32 page = 2;
  int32 page_size = 3;
  int32 total = 4;
}

message GetUserInfoRequest {
  int64 uid = 1;
}