syntax = "proto3";

package api.minigame.cas.service.v1.cas;

import "google/api/annotations.proto";
import "validate/validate.proto";
import "google/protobuf/empty.proto";
option go_package = "git.minigame.vip/service/minigame-cas-service/api/minigame-cas-service/v1;v1";


// The Cas service definition.
service Cas {
  rpc Signup(SignupRequest) returns (.google.protobuf.Empty) {
    option (google.api.http) = {
      post: "/v1/cas/signup"
      body: "*"
    };
  }
  rpc Login(LoginRequest) returns (LoginReply) {
    option (google.api.http) = {
      post: "/v1/cas/login"
      body: "*"
    };
  }
  rpc RefreshToken(.google.protobuf.Empty) returns (RefreshTokenReply) {
    option (google.api.http) = {
      post: "/v1/cas/refresh_token"
      body: "*"
    };
  }
  rpc GenerateServiceTicket(GenerateServiceTicketRequest)returns (GenerateServiceTicketReply) {
    option (google.api.http) = {
      post: "/v1/cas/st"
      body: "*"
    };
  }
  rpc ValidateServiceTicket(ValidateServiceTicketRequest)returns (ValidateServiceTicketReply) {
    option (google.api.http) = {
      get: "/v1/cas/validate"
    };
  }
  rpc Logout(.google.protobuf.Empty) returns (.google.protobuf.Empty) {
    option (google.api.http) = {
      post: "/v1/cas/logout"
      body: "*"
    };
  }
  rpc ResetPassWord(ResetPassWordRequest)returns (LoginReply) {
    option (google.api.http) = {
      post: "/v1/cas/reset_password"
      body: "*"
    };
  }
}

message ResetPassWordRequest{
  string password = 1;
  string new_password = 2;
  string name = 3;
  string app_id = 4;
}

message RefreshTokenReply {
  string access_token = 1;
  string refresh_token = 2;
}

message ValidateServiceTicketRequest {
  string st = 1;
  string app_id = 2;
}

message ValidateServiceTicketReply {
  int64 uid = 1;
}


message GenerateServiceTicketRequest {
  string app_id = 1;
}
message GenerateServiceTicketReply {
  string st = 1;
}
message LoginRequest {
  string name = 1;
  string password = 2 ;
  string type = 3;
  string app_id = 4;
}
message LoginReply {
  string  access_token = 1;
  string  refresh_token = 2;
  string  st = 3 ;
}




message SignupRequest {
  string mail = 1 [(validate.rules).string = {
    pattern:  "(?m)^(([^<>()[\\]\\\\.,;:\\s@\"]+(\\.[^<>()[\\]\\\\.,;:\\s@\"]+)*)|(\".+\"))@(minigame\\.vip)$"
    min_bytes: 14,
    max_bytes: 256,
  }];
  string password = 2;
  string real_name = 3;
}
