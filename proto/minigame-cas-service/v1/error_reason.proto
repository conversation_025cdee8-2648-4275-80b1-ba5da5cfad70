syntax = "proto3";

package api.minigame.cas.service.v1;

option go_package = "git.minigame.vip/service/minigame-cas-service/api/minigame-cas-service/v1;v1";
option java_multiple_files = true;
option java_package = "minigame-cas-service.v1";
import "errors/errors.proto";


enum ErrorReason {
  option (errors.default_code) = 500;

  UNKNOWN_ERROR = 0;


  USER_PASSWORD_NOT_MATCH = 1[(errors.code) = 400];  //密码不匹配
  USER_LOCKED = 2[(errors.code) = 403];  //
  USER_NOT_EXIST = 3[(errors.code) = 404];
  SERVICE_TICKET_NOT_FOUND = 4[(errors.code) = 404];
  TOKEN_INVALID = 5[(errors.code) = 401];
  USER_ALREADY_EXIST = 6[(errors.code) = 409];
  APP_NOT_EXIST = 7[(errors.code) = 404];
  APP_INVALID = 8[(errors.code) = 400];
  APPID_IS_EMPTY = 9[(errors.code) = 400];
  GENERATE_SERVICE_TICKET_TOO_MANY = 10[(errors.code) = 429];
}
