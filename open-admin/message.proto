syntax = "proto3";


option go_package = "open-proto/open-admin/api/extern;extern";

import "google/protobuf/timestamp.proto";
import "google/protobuf/empty.proto";
import "google/protobuf/field_mask.proto";

//Message
service Message {
  // 创建消息
  rpc CreateMessage(CreateMessageRequest) returns (CreateMessageReply) {}
  // 编辑消息
  rpc UpdateMessage(UpdateMessageRequest) returns (.google.protobuf.Empty) {}
  // 删除消息
  rpc DeleteMessage(DeleteMessageRequest) returns (.google.protobuf.Empty) {}
  // 获取消息
  rpc GetMessage(GetMessageRequest) returns (GetMessageReply) {}
  // 消息列表
  rpc ListMessage(ListMessageRequest) returns (ListMessageReply) {}
  // 发布消息
  rpc PublishMessage(PublishMessageRequest) returns (.google.protobuf.Empty) {}
  // 取消发布消息
  rpc CancelPublishMessage(CancelPublishMessageRequest) returns (.google.protobuf.Empty) {}
  // 创建通知
  rpc CreateNotice(CreateNoticeRequest) returns (CreateNoticeReply){}
  // 创建并发布
  rpc SaveAndPublishMessage(SaveAndPublishMessageRequest) returns (SaveAndPublishMessageReply) {}
  // 启用或禁用消息
  rpc SetMessageEnable(SetMessageEnableRequest) returns (.google.protobuf.Empty) {}
}

message SetMessageEnableRequest {
  uint64 id = 1;
  // 状态 true 启用、false 禁用
  bool  enable = 2;
}

message SaveAndPublishMessageRequest {
  // 标题
  optional string title = 1;
  // 消息类型 1公告 2通知 4报表 5工单 3活动
  optional  uint32 msg_type = 2;
  // 接收者
  repeated uint64 receiver = 3;
  // 消息内容
  optional string content = 4;
  // 封面
  optional string cover = 5;
  // 是否开启轮播推荐 true 是
  optional bool rotate = 6;
  // 接收者是否为全部开发者
  optional bool  is_all = 7;
  // 内容描述
  optional string desc = 8;
  // id
  uint64 id = 9;
  // 状态 true 启用、false 禁用
  optional bool  enable = 10;
  // 更新了哪些字段
  google.protobuf.FieldMask update_fields = 11;
  // 创建人
  optional  uint64 founder = 12;
}

message SaveAndPublishMessageReply{
  uint64 id = 1;
}

message CreateNoticeRequest {
  Notice notice = 1;
}


message CreateNoticeReply {
  Notice notice = 1;
}

message Notice {
  // 标题
  string title = 1;
  // 创建时间
  optional .google.protobuf.Timestamp created_time = 2;
  // 接收者
  uint64 receiver = 3;
  // 消息内容
  string content = 4;
  // id
  uint64 id = 5;
  // 内容描述
  string desc = 6;
}

message MessageInfo {
  // 发布人
  optional string publisher = 1;
  // 创建人
  optional  string founder = 2;
  // 标题
  string title = 3;
  // 消息类型 1公告 2通知 4报表 5工单 3活动
  uint32 msg_type = 4;
  // 状态 true 启用、false 禁用
  optional bool  enable = 5;
  // 创建时间
  optional .google.protobuf.Timestamp created_time = 6;
  // 发布状态 0 未知， 1 未发布，2 已发布，3 取消发布
  optional uint32 state = 7;
  // 接收者
  repeated uint64 receiver = 8;
  // 发布时间
  optional .google.protobuf.Timestamp publish_time = 9;
  // 消息内容
  string content = 10;
  // 封面
  optional string cover = 11;
  // 是否开启轮播推荐 true 是
  optional bool rotate = 12;
  // 接收者是否为全部开发者
  optional bool  is_all = 13;
  // id
  uint64 id = 14;
  // 内容描述
  string desc = 15;
}

message PublishMessageRequest {
  uint64 id = 1;
  // 发布人
  optional uint64 publisher = 2;
}

message CancelPublishMessageRequest {
  uint64 id = 1;
}

message ListMessageReply {
  //页码
  int32 page = 1[json_name = "page"];
  //每页条数
  int32 page_size = 2[json_name = "page_size"];
  //总条数
  int32 total = 3;
  //Cp
  repeated MessageInfo data = 4;
}

message ListMessageRequest {
  //搜索条件
  MessageFilter filter = 1;
  //页码
  int32 page = 2[json_name = "page"];
  //每页条数
  int32 page_size = 3[json_name = "page_size"];
}

message CreateMessageReply {
  MessageInfo message = 1;
}

message UpdateMessageRequest {
  // 标题
  optional string title = 1;
  optional  uint32 msg_type = 2[json_name="msg_type"];
  // 接收者
  repeated uint64 receiver = 3;
  // 消息内容
  optional string content = 4;
  // 封面
  optional string cover = 5;
  // 是否开启轮播推荐 true 是
  optional bool rotate = 6;
  // 接收者是否为全部开发者
  optional bool  is_all = 7;
  // 内容描述
  optional string desc = 8;
  // id
  uint64 id = 9;
  // 状态 true 启用、false 禁用
  optional bool  enable = 10;
  // 更新了哪些字段
  google.protobuf.FieldMask update_fields = 11;
}

message CreateMessageRequest {
  // 创建人
  optional  uint64 founder = 1;
  // 标题
  string title = 2;
  // 消息类型 1公告 2通知 4报表 5工单 3活动
  uint32 msg_type = 3;
  // 接收者
  repeated uint64 receiver = 4;
  // 消息内容
  string content = 5;
  // 是否开启轮播推荐 true 是
  optional bool rotate = 6;
  // 接收者是否为全部开发者
  optional bool  is_all = 7;
  // 内容描述
  string desc = 8;
  string cover = 9;
}

message GetMessageReply {
  MessageInfo message = 1;
}

message DeleteMessageRequest {
  uint64 id = 1;
}

message GetMessageRequest {
  uint64 id = 1;
}

message MessageFilter {
  optional string Title = 1;
  optional uint32 MsgType = 2;
  optional uint32 State = 3;
  optional bool   Enable = 4;
}