syntax = "proto3";

package admin.v1;

option go_package = "open-proto/open-admin/api/extern;extern";

import "open-proto/open-common/carousel.proto";

service Carousel {
  // 列出轮播图配置列表
  rpc ListCarousels(ListCarouselsRequest) returns (ListCarouselsReply) {}
  // 获取轮播图配置
  rpc GetCarousel(GetCarouselRequest) returns (GetCarouselReply) {}
  // 删除轮播图配置
  rpc DeleteCarousel(DeleteCarouselRequest) returns (DeleteCarouselReply) {}
  // 创建轮播图配置
  rpc CreateCarousel(CreateCarouselRequest) returns (CreateCarouselReply) {}
  // 更新轮播图配置
  rpc UpdateCarousel(UpdateCarouselRequest) returns (UpdateCarouselReply) {}
  // 启用/禁用轮播图
  rpc SetCarouselEnable(SetCarouselEnableRequest)
      returns (SetCarouselEnableReply) {}
  // 同步轮播图到从节点
  rpc SyncCarousels(SyncCarouselsRequest) returns (SyncCarouselsReply) {}
}

message SyncCarouselsRequest { repeated carousel.v1.Carousel carousels = 1; }

message SyncCarouselsReply {}

message ListCarouselsRequest {
  // 页码
  int32 page = 1;
  // 每页条数
  int32 page_size = 2;
  // 轮播图名称
  optional string name = 4;
  // 状态
  optional uint32 enable = 5;
  // 顺序 1、asc 2、desc
  optional uint32 order = 6;
}

message ListCarouselsReply {
  // 页码
  uint32 page = 1;
  // 每页条数
  uint32 page_size = 2;
  // 总数
  uint32 total = 3;
  // 游戏信息
  repeated carousel.v1.Carousel data = 4;
}

message GetCarouselRequest {
  // 轮播图配置id
  uint64 carousel_id = 2;
}

message GetCarouselReply {
  // 轮播图配置
  carousel.v1.Carousel carousel = 1;
}

message DeleteCarouselRequest {
  // 轮播图配置id
  uint64 carousel_id = 2;
}

message DeleteCarouselReply {}

message CreateCarouselRequest {
  // 关联的游戏id
  optional uint64 game_id = 2;
  // 轮播图名称
  optional string name = 3;
  // 排序
  optional uint32 order = 4;
  // 素材
  optional string banner = 5;
}

message CreateCarouselReply {}

message UpdateCarouselRequest {
  // 轮播图配置id
  uint64 carousel_id = 2;
  // 轮播图配置
  carousel.v1.Carousel carousel = 3;
}

message UpdateCarouselReply {}

message SetCarouselEnableRequest {
  // 轮播图配置id
  uint64 carousel_id = 2;
  // 是否启用：1 启用; 2 禁用
  uint32 enable = 3;
}

message SetCarouselEnableReply {}
