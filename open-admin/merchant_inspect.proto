syntax = "proto3";

package admin.v1;

option go_package = "open-proto/open-admin/api/extern;extern";

import "google/api/annotations.proto";
import "validate/validate.proto";
import "google/protobuf/field_mask.proto";
import "google/protobuf/empty.proto";
import "google/protobuf/timestamp.proto";
import "open-proto/open-channel-user/user.proto";

// 商户审核服务
service MerchantInspect {
  rpc List(MerchantInspectListRequest) returns   (MerchantInspectListReply) {}
  rpc Detail(MerchantInspectDetailRequest) returns (MerchantInspectDetailReply) {}
  rpc Confirm(MerchantInspectConfirmRequest) returns (google.protobuf.Empty) {
  };
}

//筛选条件
message MerchantInspectFilter {
  // 名称（名称、英文名称、全称）
  optional string name = 1;
  // 认证状态：(0:未知 ,1:待提交, 2:待审核, 3:审核失败, 4:结束,)
  optional uint32 auth_state = 2;
  // 认证类型: 1认证资质、2修改信息
  optional uint32 auth_type = 3;
  // 来源（1.注册 2.后台 3.迁移）
  optional uint32 from = 4;
  // 商户id
  optional uint64 id = 5;
  // 组织类型
  optional uint32 org_type = 6;
}

message MerchantInspectListRequest {
  MerchantInspectFilter filter = 1;
  int32 page = 2;
  int32 page_size = 3;
}

message MerchantInspectListReply{
  int32 page = 1;
  int32 page_size = 2;
  int32 total_page = 3;
  int32 total = 4;
  repeated channeluser.v1.MerchantInspectInfo data = 5;
}

message MerchantInspectDetailRequest{
  uint64 id = 1;
}
message MerchantInspectDetailReply{
    channeluser.v1.MerchantInspectInfo data = 1;
}

message MerchantInspectConfirmRequest{
  //uid
  uint64 uid = 6;
  //认证ID
  uint64 id = 1;
  // 不合规的参数名
  repeated string illegal_list = 2;
  // 合规的参数名
  repeated string compliance_list = 3;
  // 审核反馈
  string feedback = 4;
  // 是否通过: true: 通过; false:不通过
  bool is_pass = 5;
  // 更新时间
  optional google.protobuf.Timestamp update_time = 7;
}
