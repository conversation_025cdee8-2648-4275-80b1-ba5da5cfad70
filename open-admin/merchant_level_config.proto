syntax = "proto3";

package admin.v1;

import "validate/validate.proto";
import "google/protobuf/timestamp.proto";
import "google/protobuf/empty.proto";
import "google/protobuf/field_mask.proto";
import "open-proto/open-common/merchant_level_config.proto";

option go_package = "open-proto/open-admin/api/extern;extern";

// 商户等级配置相关
service MerchantLevelConfig {
  // 新增
  rpc Create (level.v1.MerchantLevelConfigCreateRequest) returns (level.v1.MerchantLevelConfigCreateReply) {
  }

  // 查询列表
  rpc Search (level.v1.MerchantLevelConfigSearchRequest) returns (level.v1.MerchantLevelConfigSearchReply) {
  }

  // 查询指定等级信息
  rpc Detail (level.v1.MerchantLevelConfigDetailRequest) returns (level.v1.MerchantLevelConfigDetailReply) {
  }
}
