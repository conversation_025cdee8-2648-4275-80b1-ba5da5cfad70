syntax = "proto3";

package admin.v1;

import "validate/validate.proto";
import "google/protobuf/timestamp.proto";
import "google/protobuf/empty.proto";
import "google/protobuf/field_mask.proto";
import "open-proto/open-common/merchant_channel.proto";

option go_package = "open-proto/open-admin/api/extern;extern";

// 商户渠道相关
service MerchantChannel {
  // 根据域名和名称设置渠道链接状态
  rpc SetStateByDomainName (MerchantChannelSetStateByDomainNameRequest) returns (google.protobuf.Empty) {
  }
  // 新增渠道链接
  rpc Create (MerchantChannelCreateRequest) returns (MerchantChannelCreateReply) {
  }
  // 新增渠道链接，根据用户uid创建
  rpc CreateByUid (MerchantChannelCreateByUidRequest) returns (MerchantChannelCreateReply) {
  }

  // 查询渠道链接列表
  rpc Search (MerchantChannelSearchRequest) returns (MerchantChannelSearchReply) {
  }
  // 查询指定渠道链接信息
  rpc Detail (MerchantChannelDetailRequest) returns (MerchantChannelDetailReply) {
  }
  // 删除渠道链接
  rpc Delete (MerchantChannelDeleteRequest) returns (google.protobuf.Empty) {
  }
  // 更新渠道链接
  rpc Update (MerchantChannelUpdateRequest) returns (MerchantChannelUpdateReply) {
  }
  // 更新渠道链接状态
  rpc SetState (MerchantChannelSetStateRequest) returns (google.protobuf.Empty) {
  }
  // 域名记录是否存在
  rpc DomainExists(DomainExistsRequest) returns (DomainExistsReply) {}

  // 设置渠道链接选中的游戏列表
  rpc SetGames (MerchantChannelSetGamesRequest) returns (google.protobuf.Empty) {}
  // 生成随机域名占位符
  rpc generateUrl (MerchantChannelGenerateUrlRequest) returns (MerchantChannelGenerateUrlReply) {}
  // 操作日志
  rpc OperationLog (MerchantChannelOperationLogRequest) returns (MerchantChannelOperationLogReply) {}
  // 获取渠道链接的游戏列表
  rpc Games (MerchantChannelGamesRequest) returns (MerchantChannelGamesReply) {}
  // 商户的渠道列表
  rpc SearchByMerchantId (MerchantChannelSearchByMerchantIdRequest) returns (MerchantChannelSearchByMerchantIdReply) {}
  // 提交上架审核
  rpc Up(MerchantChannelUpRequest) returns (MerchantChannelUpReply) {}
  // 提交下架审核
  rpc Down(MerchantChannelDownRequest) returns (MerchantChannelDownReply) {}
  // 提交违规下架审核
  rpc ViolationDown(MerchantChannelViolationDownRequest) returns (MerchantChannelViolationDownReply) {}
  // 提交违规下架申诉
  rpc ViolationDownAppeal(MerchantChannelViolationDownAppealRequest) returns (MerchantChannelViolationDownAppealReply) {}
  // 提交违规下架重新上架审核
  rpc ViolationDownRepublish(ViolationDownRepublishRequest) returns (ViolationDownRepublishReply) {}
}

message ViolationDownRepublishRequest{
  // 渠道链接id
  uint64 id = 1;
  // 操作人id(前端不传)
  uint64 uid = 2; 
}

message ViolationDownRepublishReply{

}


message MerchantChannelSearchByMerchantIdRequest {
  // 商户id
  uint64 merchant_id = 1;
  // 页码
  uint32 page = 2;
  // 每页条数
  uint32 page_size = 3;
}

message MerchantChannelDomainInfo {
  //  渠道二级域名
  string sub_domain = 1;
  // URL
  string domain = 2;
  // 发布时间
  google.protobuf.Timestamp create_time = 3;
}

message MerchantChannelSearchByMerchantIdReply {
  // 渠道链接列表
  repeated MerchantChannelDomainInfo channels = 1;
  // 页码
  uint32 page = 2;
  // 每页条数
  uint32 page_size = 3;
  // 总条数
  uint32 total = 4;
}

message MerchantChannelGamesRequest {
  // 渠道链接域名
  string domain = 1;
}

message MerchantChannelGamesReply {
  // 游戏列表
  repeated MerchantChannelGameLink games = 1;
}

message MerchantChannelGameLink{
  string app_id = 1;
  string link = 2;
}

message MerchantChannelOperationLogRequest {
  // 渠道链接id
  uint64 id = 1;
}

message MerchantChannelOperationLog {
  string operation = 1; // 操作内容
  string operator = 2; // 操作人
  google.protobuf.Timestamp operation_time = 3; // 操作时间
}

message MerchantChannelOperationLogReply {
  repeated MerchantChannelOperationLog logs = 1; // 操作日志列表
}


message MerchantChannelUpdateStrategyRequest {
  // 渠道链接id
  uint64 id = 1;
  // 变现策略
  uint32 strategy = 2;
  // 操作人id(前端不传)
  uint64 uid = 3;
}

message MerchantChannelGenerateUrlRequest {
  string domain = 1;
  // 商户id
  optional uint64 merchant_id =2;
}

message MerchantChannelGenerateUrlReply {
  string url = 1;
}

message MerchantChannelSetGamesRequest {
  // 渠道链接id
  uint64 id = 1;
  // 游戏列表
  repeated string games = 2;
}

message DomainExistsRequest {
  // 域名
  string record = 1;
}

message DomainExistsReply {
  // 是否存在
  bool is_exist = 1;
}

message MerchantChannelSetStateRequest {
  // 渠道链接id
  uint64 id = 1;
  // 渠道链接状态
  uint32 state = 2;
}

message MerchantChannelUpdateRequest {
  uint64 id = 1;
  // 1 MiniGame1.0九宫格，2 MiniGame1.0精品推荐，3 2.0，4 无模板
  optional uint32 template = 2;
  repeated string games = 3;
  // 更新了哪些字段
  google.protobuf.FieldMask update_fields = 4;
  // 变现策略，1 纯净版 2 普通版 3 增强版
  optional uint32 strategy = 5;
  // uid
  uint64 uid = 7; // 操作人id(前端不传)
  // 来源
  uint32 from = 8; // 提交来源 1、商户 2、后台
}

// 使用uid进行渠道链接的创建，用于open-channel-user发起的创建
message MerchantChannelCreateByUidRequest {
  string domain = 1;
  repeated string games = 2;
  uint32 template = 3;
  uint64  uid = 4;
  // 变现策略，1 纯净版 2 普通版 3 增强版
  uint32 strategy = 5;
}

message MerchantChannelCreateRequest {
  // 商户id
  uint64  merchant_id = 1;
  // uid
  uint64 uid = 2; // 操作人id(前端不传)
  // 渠道域名
  string domain = 3;
  // 流量来源，0 未知 1 Google 2 Meta 3 Tiktok 4 Applovin 5 Snapchat 6 Mintegral 7 现有用户 8 其他广告平台
  repeated uint32 traffic_source=5;
  // 行业类型
  optional uint32 industry_type = 6;
  // 用户分布，为cldr国家/地区编码列表
  repeated string user_area = 7;
  // 用户量级（DAU），0 未知 1 [0, 10_000) 2 [10_000, 50_000) 3 [50_000,200_000 4 [200_000, 500_000) 5 [500_000, 1_000_000) 6 [1_000_000, 2_000_000) 7 [2_000_000, +∞)
  optional uint32 user_scale=8;
  // 媒体类型，0 未知 1 网站 2 google商店 3 苹果商店 4 其他应用市场
  optional uint32 media_type = 9;
  // 产品链接
  optional string product_link =10;
  // 是否合作过h5游戏，false：否，true：是
  optional bool use_to_cooperate_with_h5 = 11;
  // 用户入口示意图列表
  repeated string user_entry_pics = 12;
  // 模板
  uint32 template = 13;
  // 变现策略，1 纯净版 2 普通版 3 增强版
  uint32 strategy = 14;
  // 提交来源 1、商户 2、后台
  optional uint32 from = 15; 
}

message MerchantChannelCreateReply {
  MerchantChannelInfo detail = 1;
}

message MerchantChannelUpdateReply {
  MerchantChannelInfo detail = 1;
}


message MerchantChannelFilter {
  // 商户名称
  optional string name = 1;
  // 渠道链接
  optional string domain = 2;
  // 模板
  optional uint32 template = 3;
  // 部署状态 1、游戏待部署 2、部署成功 3、部署失败
  optional uint32 state = 4;
  // 变现策略
  optional uint32 strategy = 6;
  // 提交来源 // 1、商户 2、后台
  optional uint32 from = 7; 
  // 发布状态
  optional uint32 publish_state = 8; // 0 未知 1 未发布 2 审核通过 3 已发布 4 已下架 5 违规下架
}

message MerchantChannelSearchRequest {
  MerchantChannelFilter filter = 1;
  // 页码
  uint32 page = 2;
  // 每页条数
  uint32 page_size = 3;
}

message MerchantChannelSearchReply {
  repeated  MerchantChannelInfo data = 1;
  // 页码
  uint32 page = 2;
  // 每页条数
  uint32 page_size = 3;
  // 总条数
  uint32 total = 4;
}

message MerchantChannelDetailRequest {
  // 渠道链接id
  uint64 id = 1;
}

message MerchantChannelDetailReply {
  MerchantChannelInfo detail = 1;
}

message MerchantChannelDeleteRequest {
  // 渠道链接id
  uint64 id = 1;
}

message MerchantStateDetailItem {
  // 1 提交成功 2 部署成功
  uint32 state = 1;
  // 状态达成时间
  google.protobuf.Timestamp at = 2;
}

message MerchantChannelInfo {
  // id
  uint64 id = 1;
  // 渠道链接名称
  string name = 2;
  // 商户id
  uint64 merchant_id = 3;
  //  商户名称
  string merchant_name = 4;
  //  渠道链接
  string domain = 5;
  // 流量来源，0 未知 1 Google 2 Meta 3 Tiktok 4 Applovin 5 Snapchat 6 Mintegral 7 现有用户 8 其他广告平台
  repeated uint32 traffic_source=6;
  // 行业类型 参见配置
  optional uint32 industry_type = 7;
  // 用户分布，为cldr国家/地区编码列表
  repeated string user_area = 8;
  // 用户量级（DAU），0 未知 1 [0, 10_000) 2 [10_000, 50_000) 3 [50_000,200_000 4 [200_000, 500_000) 5 [500_000, 1_000_000) 6 [1_000_000, 2_000_000) 7 [2_000_000, +∞)
  optional uint32 user_scale=9;
  // 媒体类型，0 未知 1 网站 2 google商店 3 苹果商店 4 其他应用市场
  optional uint32 media_type =10;
  // 产品链接
  optional string product_link = 11;
  // 是否合作过h5游戏，false：否，true：是
  optional bool use_to_cooperate_with_h5 = 12;
  // 用户入口示意图列表
  repeated string user_entry_pics = 13;
  // 变现策略，1 纯净版 2 普通版 3 增强版
  optional uint32 strategy = 14;
  // 参见配置
  optional uint32 template = 15;
  // 游戏列表
  repeated string games = 16;
  // 发布状态，0 未知 1 待部署 2 部署完成
  optional uint32 state = 17;
  // 发布状态，0 未知 1 未发布 2 审核通过 3 已发布 4 已下架 5 违规下架 6 已删除
  uint32 publish_state = 18;
  // 提交来源 1 商户 2 后台 3 迁移
  uint32 from = 19;
  // 最近审核id
  optional uint64 last_inspect_id = 20;
  // 最近审核类型
  optional uint32 last_inspect_type = 21;
  // 最近审核状态
  optional uint32 last_inspect_state = 22;
  // 创建时间
  google.protobuf.Timestamp create_time = 23;
  // 更新时间
  google.protobuf.Timestamp update_time = 24;
  // 最新审核的更新时间
  google.protobuf.Timestamp last_inspect_update_time = 25;
  // 最近的审核反馈
  string last_inspect_feedback = 26;
}

message MerchantChannelSetStateByDomainNameRequest {
  string domain = 1; // 渠道链接
  uint32 state = 2; // 渠道链接状态
  string name = 3; // 渠道链接名称
}


message MerchantChannelUpRequest{
  // 渠道链接id
  uint64 id = 1;
  // 操作人id(前端不传)
  uint64 uid = 2; 
  // 提交来源 1 商户 2 后台(前端不传)
  uint32 from = 3;
}

message MerchantChannelUpReply{

}

message MerchantChannelDownRequest{
  // 渠道链接id
  uint64 id = 1;
  // 操作人id(前端不传)
  uint64 uid = 2; 
  // 提交来源 1 商户 2 后台(前端不传)
  uint32 from = 3;
}

message MerchantChannelDownReply{
  
}

message MerchantChannelViolationDownRequest{
  // 渠道链接id
  uint64 id = 1;
  // 操作人id(前端不传)
  uint64 uid = 2; 
}

message MerchantChannelViolationDownReply{

}


message MerchantChannelViolationDownAppealRequest{
  // 渠道链接id
  uint64 id = 1;
  // 操作人id(前端不传)
  uint64 uid = 2; 
  // 申述理由
  string appeal = 3;
}

message MerchantChannelViolationDownAppealReply{

}
