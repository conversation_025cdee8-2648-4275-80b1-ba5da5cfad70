syntax = "proto3";

package game.v1;

import "open-proto/open-common/metadata.proto";
import "google/protobuf/empty.proto";

option go_package = "open-proto/open-admin/api/extern;extern";


service MetadataService {
  // 查询游戏引擎列表
  rpc GameEngineList(metadata.v1.MetadataGameEngineListRequest) returns (metadata.v1.MetadataGameEngineListReply) {}

  // 查询游戏类型列表
  rpc GameTypeList(metadata.v1.MetadataGameTypeListRequest) returns (metadata.v1.MetadataGameTypeListReply) {}

  // 查询游戏Tag列表
  rpc GameTagList(metadata.v1.MetadataGameTagListRequest) returns (metadata.v1.MetadataGameTagListReply) {}

  // 查询游戏语言列表
  rpc GameLanguageList(metadata.v1.MetadataGameLanguageListRequest) returns (metadata.v1.MetadataGameLanguageListReply) {}
  rpc GameLanguageGroupI18nList(GameLanguageGroupI18nListRequest) returns (metadata.v1.MetadataGameLanguageListReply) {}

  // 查询有相应游戏的游戏类型列表
  rpc GameTypeGameUsedList(.google.protobuf.Empty) returns (metadata.v1.MetadataGameTypeListReply) {}

  // 查询有游戏Tag列表
  rpc GameTagUsedList(.google.protobuf.Empty) returns (metadata.v1.MetadataGameTagListReply) {}

  // 重载缓存
  rpc Reload(.google.protobuf.Empty) returns (.google.protobuf.Empty) {}
  // 同步到从节点
  rpc SyncToSlaveNode(SyncToSlaveNodeRequest)  returns (.google.protobuf.Empty) {}
  // 根据语言获取游戏类型列表
  rpc GameTypeI18nList(GameTypeI18nListRequest) returns (metadata.v1.MetadataGameTypeListReply) {}
  // 根据语言获取游戏的标签列表
  rpc GameTagI18nList(GameTagI18nListRequest) returns (metadata.v1.MetadataGameTagListReply) {}

  rpc GameTypeGameUsedI18nList(GameTypeGameUsedListI18nRequest) returns (metadata.v1.MetadataGameTypeListReply) {}
  rpc GameTagUsedI18nList(GameTagUsedListI18nRequest) returns (metadata.v1.MetadataGameTagListReply) {}

  // 根据语言获取语言列表
  rpc GameLanguageI18nList(GameLanguageI18nListRequest) returns (metadata.v1.MetadataLanguageListReply) {}
  // 获取模板列表
  rpc MerchantChannelTemplateList(metadata.v1.MetadataMerchantChannelTemplateListRequest) returns (metadata.v1.MetadataMerchantChannelTemplateListReply) {}
  // 获取模板分类列表
  rpc MerchantChannelTemplateTypeList(metadata.v1.MetadataMerchantChannelTemplateTypeListRequest) returns (metadata.v1.MetadataMerchantChannelTemplateTypeListReply) {}
  // 变现策略对应的可选模板列表
  rpc MerchantChannelTemplateByStrategy(metadata.v1.MetadataMerchantChannelTemplateByStrategyRequest) returns (metadata.v1.MetadataMerchantChannelTemplateByStrategyReply) {}
  // 行业分类
  rpc MerchantIndustryList(metadata.v1.MetadataMerchantIndustryListRequest) returns (metadata.v1.MetadataMerchantIndustryListReply) {}
}

message GameLanguageI18nListRequest{
  string lang = 1;
}


message GameTypeGameUsedListI18nRequest{
  string lang =1;
}

message GameTagUsedListI18nRequest{
  string lang =1;
}


message SyncToSlaveNodeRequest{
  repeated metadata.v1.MetadataRawData datas = 1;
}

message GameTypeI18nListRequest{
  string lang = 1;
}

message GameTagI18nListRequest{
  string lang = 1;
}

message GameLanguageGroupI18nListRequest{
  string lang = 1;
}