syntax = "proto3";

package admin.v1;

option go_package = "open-proto/open-admin/api/extern;extern";

import "open-proto/open-channel-user/user.proto";
import "google/protobuf/empty.proto";

// 商户管理
service Merchant {
  // 创建
  rpc Create(MerchantCreateRequest) returns (MerchantCreateReply) {}
  // 查询
  rpc Search(MerchantSearchRequest) returns (MerchantSearchReply) {}
  // 查询详情
  rpc Detail(MerchantDetailRequest) returns (MerchantDetailReply) {}
  // 更新
  rpc Update(MerchantUpdateRequest) returns (.google.protobuf.Empty) {}
  // 删除
  rpc Delete(MerchantDeleteRequest) returns (.google.protobuf.Empty) {}
  // 启用/禁用
  rpc Enable(MerchantEnableRequest) returns (.google.protobuf.Empty) {}
  // 启用/禁用 白名单
  rpc Whitelist(MerchantEnableRequest) returns (.google.protobuf.Empty) {}
  // 所有发行商的概要信息
  rpc AllSummary(MerchantAllSummaryRequest) returns (MerchantAllSummaryReply) {}
  // 生成唯一标识
  rpc GenUid(MerchantGenUidRequest) returns (MerchantGenUidReply) {}
  // 更新帐号等级
  rpc UpdateLevel(MerchantUpdateLevelRequest) returns (.google.protobuf.Empty) {}
}

message MerchantSearchRequest {
  // 中文名或英文名或全称
  optional string name = 1;
  // 商户ID，全匹配
  optional uint64 id = 2;
  // 认证状态
  optional uint32 auth_state = 3;
  // 启用状态
  optional bool enable = 4;
  // 页码
  optional int32 page = 5;
  // 每页条数
  optional int32 page_size = 6;
  // 组织类型
  optional uint32 org_type = 7;
  // 商户等级
  optional string level_name = 8;
}

message MerchantSearchReply{
  // 页码
  int32 page = 1;
  // 每页条数
  int32 page_size = 2;
  // 总页数
  int32 total_page = 3;
  // 总条数
  int32 total = 4;
  // 商户列表
  repeated channeluser.v1.MerchantInfo data = 5;
}

message MerchantCreateRequest {
  // 创建者uid，服务端微服务间使用，前端不指定
  uint64 uid = 1;
  // 认证信息
  channeluser.v1.MerchantInspectInfo inspect=2;
  // 帐号信息
  channeluser.v1.UserRelationChangeInfo user =3;
}

message MerchantCreateReply {
  // 商户id
  uint64 id = 1;
}

message MerchantDetailRequest {
  // 操作者uid，服务端微服务间使用，前端不指定
  uint64 uid = 1;
  // 商户id
  uint64 id = 2;
}

message MerchantDetailReply {
  // 商户信息
  channeluser.v1.MerchantInfo merchant=1;
  // 帐号信息
  channeluser.v1.UserRelationInfo user =2;
}

message MerchantUpdateRequest {
  // 操作者uid，服务端微服务间使用，前端不指定
  uint64 uid = 4;
  // 商户id
  uint64 id = 1;
  // 认证信息
  channeluser.v1.MerchantInspectInfo inspect=2;
  // 帐号信息
  channeluser.v1.UserRelationChangeInfo user =3;
}

message MerchantDeleteRequest {
  // 操作者uid，服务端微服务间使用，前端不指定
  uint64 uid = 1;
  // 商户id
  uint64 id = 2;
}

message MerchantEnableRequest {
  // 操作者uid，服务端微服务间使用，前端不指定
  uint64 uid = 1;
  // 商户id
  uint64 id = 2;
  //状态: true 启用、false 禁用
  optional bool enable = 3;
}

message MerchantAllSummaryRequest {
  // 操作者uid，服务端微服务间使用，前端不指定
  uint64 uid = 1;
}

message MerchantAllSummaryReply {
  repeated channeluser.v1.MerchantSummary data = 1;
}

message  MerchantGenUidRequest{
}
message  MerchantGenUidReply{
  uint64 uid = 1;
}

message MerchantUpdateLevelRequest{
  // 操作者uid，服务端微服务间使用，前端不指定
  uint64 uid = 1;
  // 商户id
  uint64 id = 2;
  // 等级
  string level_name = 3;
}