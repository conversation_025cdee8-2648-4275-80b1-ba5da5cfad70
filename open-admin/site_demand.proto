syntax = "proto3";

package admin.v1;

import "open-proto/open-common/site_demand.proto";
import "google/protobuf/timestamp.proto";

option go_package = "open-proto/open-admin/api/extern;extern";

service SiteDemand {
  // 搜索网站需求
  rpc Search(SearchRequest) returns (SearchReply) {}
  // 获取网站需求
  rpc Get(GetRequest) returns (sitedemand.v1.SiteDemand) {}
  // 更新网站需求
  rpc Update(UpdateRequest) returns (UpdateReply) {}
  // 删除网站需求
  rpc Delete(DeleteRequest) returns (DeleteReply) {}
  // 域名记录是否存在
  rpc RecordExists(RecordExistsRequest) returns (RecordExistsReply) {}
}

message RecordExistsRequest {
  // 域名记录
  string record = 1;
}

message RecordExistsReply {
  // 是否存在
  bool is_exist = 1;
}

message SearchRequest {
  // 页码
  uint32 page = 1;
  // 每页条数
  uint32 page_size = 2;
  // 发行商id
  optional uint64 publisher_id = 3;
  // 模板枚举:  1、minigame2.0模板 2、微游中心模板
  optional uint32 template = 4;
  // 网站域名
  optional string domain = 5;
  // 联系状态 1、待联系 2、已联系
  optional uint32 contact_state = 6;
}

message SearchReply {
  // 网站需求列表
  repeated sitedemand.v1.SiteDemand data = 1;
  // 页码
  int32 page = 2;
  // 每页条数
  int32 page_size = 3;
  // 总条数
  int32 total = 4;
}

message GetRequest {
  // 网站需求id
  uint64 demand_id = 1;
}

message UpdateRequest {
  uint64 demand_id = 1;
  // 联系状态 1、待联系 2、已联系
  uint32 contact_state = 2;
  // 联系情况
  string contact_status = 3;
  // 更新时间，做校验用
  google.protobuf.Timestamp update_at = 4;
}

message UpdateReply {}

message DeleteRequest {
  // 网站需求id
  uint64 demand_id = 1;
}

message DeleteReply {}

message ContactRequest {
  // 网站需求id
  uint64 demand_id = 1;
  // 联系状态 1、待联系 2、已联系
  uint32 contact_state = 2;
  // 更新时间，做校验用
  google.protobuf.Timestamp update_at = 3;
}

message ContactReply {}
