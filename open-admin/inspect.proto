syntax = "proto3";

package admin.v1;

option go_package = "open-proto/open-admin/api/extern;extern";

import "open-proto/open-common/cp.proto";
import "google/protobuf/timestamp.proto";


//认证管理
service Inspect {
  //获取认证列表
  rpc ListInspects(ListInspectsRequest) returns (ListInspectsReply) {
  }
  //获取认证详情
  rpc GetInspect(GetInspectRequest) returns (GetInspectReply) {
  }
  //审核认证
  rpc ConfirmInspect (ConfirmInspectRequest) returns (ConfirmInspectReply) {
  };
}

message ListInspectsRequest {
  //搜索条件
  Filter filter = 1;
  //页码
  int32 page = 2;
  //每页条数
  int32 page_size = 3;
}
message ListInspectsReply {
  //页码
  int32 page = 1;
  //每页条数
  int32 page_size = 2;
  //总页数
  int32 total_page = 3;
  //总条数
  int32 total = 4;
  //认证列表
  repeated user.v1.InspectInfo data = 5;
}

message GetInspectRequest {
  //认证ID
  uint64 id = 1;
}
message GetInspectReply {
  //认证信息
  user.v1.InspectInfo inspect = 1;
}


message ConfirmInspectRequest{
  //uid
  uint64 uid = 6;
  //认证ID
  uint64 id = 1;
  // 不合规的参数名
  repeated string illegal_list = 2;
  // 合规的参数名
  repeated string compliance_list = 3;
  // 审核反馈
  string feedback = 4;
  // 是否通过: true: 通过; false:不通过
  bool is_pass = 5;
  // 更新时间
  optional google.protobuf.Timestamp update_time = 7;
}
message ConfirmInspectReply{
}



//筛选条件
message Filter {
  // 名称（厂商名称、英文名称、全称）
  optional string name = 1;
  // 认证状态：(0:未知 ,1:待提交, 2:待审核, 3:审核失败, 4:结束,)
  optional uint32 auth_state = 2;
  // 认证类型: 1认证资质、2修改信息
  optional uint32 auth_type = 3;
  //来源（1.注册 2.后台 3.迁移）
  optional uint32 from = 4;
}
