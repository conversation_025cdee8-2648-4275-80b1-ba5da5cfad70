syntax = "proto3";

package admin.v1;

option go_package = "open-proto/open-admin/api/extern;extern";

import "google/api/annotations.proto";
import "validate/validate.proto";
import "google/protobuf/field_mask.proto";
import "google/protobuf/empty.proto";
import "google/protobuf/timestamp.proto";
import "open-proto/open-common/publisher.proto";
import "open-proto/open-channel-user/user.proto";

service Publisher {
  // 列出发行商列表
  rpc ListPublishers(ListPublishersRequest) returns (ListPublishersReply) {}
  // 创建发行商
  rpc CreatePublisher(CreatePublisherRequest) returns (CreatePublisherReply) {}
  // 更新发行商信息
  rpc UpdatePublisher(UpdatePublisherRequest) returns (UpdatePublisherReply) {}
  // 删除发行商
  rpc DeletePublisher(DeletePublisherRequest) returns (DeletePublisherReply) {}
  // 获取发行商信息
  rpc GetPublisher(GetPublisherRequest) returns (GetPublisherReply) {}
}

message ListPublishersRequest {
  // 公司名
  optional string company_name = 1;
  // 联系人
  optional string contact_person = 2;
  // 联系方式
  optional string contact_number = 3;
  // 联系邮箱
  optional string contact_email = 4;
  // 联系状态
  optional uint32 state = 5;
  // 来源
  optional uint32 from = 6;
  // 页码
  int32 page = 8;
  // 每页条数
  int32 page_size = 9;
}

message ListPublishersReply {
  // 页码
  uint32 page = 1;
  // 每页条数
  uint32 page_size = 2;
  // 总数
  uint32 total = 3;
  // 游戏信息
  repeated publisher.v1.Publisher data = 4;
}

message CreatePublisherRequest {
  // 公司名
  optional string company_name = 1;
  // 联系人
  optional string contact_person = 2;
  // 联系方式
  optional string contact_number = 3;
  // 联系邮箱
  optional string contact_email = 4;
  // 联系状态
  optional uint32 state = 6;
  // 联系情况
  optional string contact_status = 7;
  // 发行商来源
  optional uint32 from = 8;
}

message CreatePublisherReply {}

message UpdatePublisherRequest {
  // 发行商id
  uint64 publisher_id = 2;
  // 发行商的更新数据
  publisher.v1.Publisher publisher = 3;
}

message UpdatePublisherReply {}

message DeletePublisherRequest {
  // 发行商id
  uint64 publisher_id = 2;
}

message DeletePublisherReply {}

message GetPublisherRequest {
  // 发行商id
  uint64 publisher_id = 1;
}

message GetPublisherReply {
  // 发行商信息
  publisher.v1.Publisher publisher = 1;
}
