syntax = "proto3";

package admin.v1;

import "validate/validate.proto";
import "google/protobuf/timestamp.proto";
import "google/protobuf/empty.proto";
import "google/protobuf/field_mask.proto";
import "open-proto/open-common/api.proto";

option go_package = "open-proto/open-admin/api/extern;extern";

// 商户渠道相关
service MerchantChannelForApi {
  // 获取渠道链接的游戏列表
  rpc Games (api.v1.ChannelGameLinksRequest) returns (api.v1.ChannelGameLinksReply) {}
  // 获取渠道链接下的游戏详情
  rpc GameDetail (api.v1.ChannelGameDetailRequest) returns (api.v1.ChannelGameDetailReply) {}
}
