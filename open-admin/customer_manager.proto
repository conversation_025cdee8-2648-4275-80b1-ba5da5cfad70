syntax = "proto3";

package admin.v1;

import "google/api/annotations.proto";
import "validate/validate.proto";

option go_package = "open-proto/open-admin/api/extern;extern";

//客户经理
service CustomerManagerService {
  //列表
  rpc ListCustomer(ListCustomerRequest) returns (ListCustomerReply) {
    option (google.api.http) = {
      get: "/v1/admin/customer_manager"
    };
  }
}

message ListCustomerRequest {
  //页码
  int32 page = 1;
  //每页条数
  int32 page_size = 2;
}
message ListCustomerReply {
  //总条数
  int32 total = 1;
  //客户经理
  repeated CustomerManager data = 2;
}

message CustomerManager {
  uint64 id = 1;
  string email = 2[(validate.rules).string = {
    pattern:  "(?m)^(([^<>()[\\]\\\\.,;:\\s@\"]+(\\.[^<>()[\\]\\\\.,;:\\s@\"]+)*)|(\".+\"))@(minigame\\.vip)$"
    min_bytes: 6,
    max_bytes: 256,
  }];
  string name = 3;
}