syntax = "proto3";

package admin.v1;

import "validate/validate.proto";
import "open-proto/open-common/game.proto";
import "google/protobuf/empty.proto";
import "google/protobuf/timestamp.proto";
option go_package = "open-proto/open-admin/api/extern;extern";

service Game {
  // 游戏推送
  rpc SyncGame(SyncGameRequest) returns (SyncGameReply) {}
  // 游戏列表
  rpc SearchGames(SearchGamesRequest) returns (SearchGamesReply) {}
  // 游戏版本版本
  rpc GameVersionList(GameVersionListRequest) returns (GameVersionListReply) {}
  // 创建游戏
  rpc CreateGame(CreateGameRequest) returns (CreateGameReply) {}
  // 更新游戏
  rpc UpdateGame(UpdateGameRequest) returns (UpdateGameReply) {}
  // 删除游戏
  rpc DeleteGame(DeleteGameRequest) returns (DeleteGameReply) {}
  // 生成游戏AppID
  rpc GenGameAppID(GenGameAppIDRequest) returns (GenGameAppIDReply) {}
  // 获取游戏版本号
  rpc GenGameVersion(GenGameVersionRequest) returns (GenGameVersionReply) {}
  // 提交审核
  rpc SubmitInspect(SubmitInspectRequest) returns (SubmitInspectReply) {}
  rpc WithdrawInspect(WithdrawInspectRequest) returns (WithdrawInspectReply) {}
  // 新增版本
  rpc CreateGameVersion(CreateGameVersionRequest) returns (CreateGameVersionReply) {}
  // 版本详情
  rpc GetGameVersion(GetGameVersionRequest) returns (GetGameVersionReply) {}
  // 游戏上架
  rpc PublishGameVersion(PublishGameVersionRequest) returns (PublishGameVersionReply) {}
  // 游戏下架
  rpc UnpublishGameVersion(UnpublishGameVersionRequest) returns (UnpublishGameVersionReply) {}
  // 设置游戏状态
  rpc SetGameEnable(SetGameEnableRequest) returns (SetGameEnableReply) {}
  // 获取所有已发布的游戏
  rpc GetAllPublishedGames(GetAllPublishedGamesRequest) returns (GetAllPublishedGamesReply) {}
  // 运营后台推送游戏评分和介绍到开发者平台
  rpc PushGameData(PushGameDataRequest) returns (.google.protobuf.Empty) {}
  // 获得游戏的点赞数据
  rpc GetGamesLikeCount(.google.protobuf.Empty) returns (GetGamesLikeCountReply) {}
  // 设置total like count
  rpc SyncGameTotalLikeCount(SyncGameTotalLikeCountRequest) returns (.google.protobuf.Empty) {}
  // 同步游戏到从节点
  rpc SyncGameToSlaveNode(SyncGameToSlaveNodeRequest) returns (SyncGameToSlaveNodeReply) {}
  // 设置游戏的某种类型的数据
  rpc UpsertGameI18nAttributes(UpsertGameI18nAttributesRequest) returns (UpsertGameI18nAttributesReply) {}
  // 游戏的多语言完整配置
  rpc UpsertGameI18nDatas(UpsertGameI18nDatasRequest) returns (UpsertGameI18nDatasReply) {}

  rpc ListGames(.game.v1.ListGamesRequest) returns (.game.v1.ListGamesReply) {};

}

message UpsertGameI18nDatasRequest{
  message GameI18nData {
    string app_id = 1;
    uint32 lang = 2;
    string name = 3;
    string description = 4;
    string how_to_play = 5;
  }
  repeated GameI18nData datas = 1;
}

message UpsertGameI18nDatasReply{}

message UpsertGameI18nAttributesRequest {
  message GameI18nAttribute {
    string app_id = 1;
    // 1.名称 2.简介 3.玩法说明
    uint32 type = 2;
    uint32 lang = 3;
    string content = 4;
  }
  repeated GameI18nAttribute attrs = 1;
}

message UpsertGameI18nAttributesReply {}

message SyncGameTotalLikeCountRequest {
  repeated GameLikeInfo data = 1;
}


message GameLikeInfo {
  uint64 game_id = 1;
  int64 like_count = 2;
}

message SyncGameToSlaveNodeRequest{
  message GameI18nData{
    uint32 lang = 1;
    string name = 2;
    string desc = 3;
    string how_to_play = 4;
  }
  uint64 game_id = 1;
  string app_id = 2;
  uint64 merchant_id = 3;
  uint32 enable = 4;
  uint32 state = 5;
  bool auto_push = 6;
  optional google.protobuf.Timestamp delete_at = 7;
  game.v1.GameVersionInfo game_version = 8;
  double rating = 9;
  uint64 play_count = 12;
  uint64 yesterday_play_count = 13;
  uint64 past_7d_play_count = 14;
  repeated string county = 15;
  string how_to_play = 10;
  string app_url = 11;
  // 版本审核通过的时间
  optional.google.protobuf.Timestamp last_inspected_at = 16;
  uint64 total_like_count = 17;
  optional google.protobuf.Timestamp last_push_at = 18;
  repeated GameI18nData i18n_datas = 19;
  double  estimated_earnings = 20;
  uint32 icon_height = 21;
  uint32 icon_width = 22;
  uint32 big_icon_height = 23;
  uint32 big_icon_width = 24;
  uint32 flash_height = 25;
  uint32 flash_width = 26;
  uint32 banner_height = 27;
  uint32 banner_width = 28;
}

message SyncGameToSlaveNodeReply{}

message GetGamesLikeCountReply {
  repeated GameLikeInfo data = 1;
}


message PushGameDataRequest {
  // appid
  string app_id = 1;
  // 游戏评分
  double rating = 2;
  // 游戏玩法
  string how_to_play = 3;
  // 游戏链接
  string game_url = 4;
}


//游戏审核管理
service GameInspect {
  //游戏审核列表
  rpc ListGameInspects(ListGameInspectsRequest) returns (ListGameInspectsReply) {
  }
  //游戏审核详情
  rpc GetGameInspect(GetGameInspectRequest) returns (GetGameInspectReply) {
  }
  //审核游戏
  rpc ConfirmGameInspect (ConfirmGameInspectRequest) returns (ConfirmGameInspectReply) {
  };

}



message SyncGameRequest {
  // 游戏版本id
  uint64 game_version_id = 1;
}

message SyncGameReply {
}

message ListGameInspectsRequest {
  //搜索条件
  GameInspectFilter filter = 1;
  //页码
  int32 page = 2;
  //每页条数
  int32 page_size = 3;
}
message ListGameInspectsReply {
  //页码
  int32 page = 1;
  //每页条数
  int32 page_size = 2;
  //总页数
  int32 total_page = 3;
  //总条数
  int32 total = 4;
  //游戏审核列表
  repeated game.v1.GameInspectInfo data = 5;
}

message GetGameInspectRequest {
  //游戏审核ID
  uint64 id = 1;
}
message GetGameInspectReply {
  //游戏审核信息
  game.v1.GameInspectInfo game_inspect = 1;
  //游戏版本
  game.v1.GameVersionInfo game_version = 2;
}


message ConfirmGameInspectRequest{
  //游戏审核ID
  uint64 id = 1;
  // 不合规的参数名
  repeated string illegal_list = 2;
  // 合规的参数名
  repeated string compliance_list = 3;
  // 审核反馈
  string feedback = 4;
  // 是否通过: true: 通过; false:不通过
  bool is_pass = 5;
  //uid
  optional uint64 uid = 6;
}
message ConfirmGameInspectReply{
  //游戏审核信息
  game.v1.GameInspectInfo game_inspect = 1;
  //游戏版本
  game.v1.GameVersionInfo game_version = 2;
}

message UpdateGameInspectRequest {
  //游戏审核信息
  game.v1.GameInspectInfo inspect = 1;
}
message UpdateGameInspectReply {
  //MerchantID
  int64 id = 1;
}

//筛选条件
message GameInspectFilter {
  // App Id
  optional string app_id = 1;
  // 游戏名称
  optional string name = 2 [(validate.rules).string.max_len = 64];
  // 提交人
  string creator_name = 3;
  // 审核状态(0.所有;1.审核中;2.撤回审核;3.审核未通过;4.审核通过)
  uint32 state = 4;
  // 审核类型(0.所有;1.新游接入;2.版本更新;3.游戏下架;4.恢复上架)
  uint32 type =5;
  // 提交人类型\来源(0.未知;1.开发者;2.运营经理;3.数据迁移)
  uint32 creator_type = 6;
}


message SearchGamesRequest {
  // 游戏名
  optional string name = 1;
  // 游戏英文名
  optional string name_en = 2;
  //merchant名
  optional string merchant_name = 3;
  // 游戏进度
  optional uint32 progress = 4;
  // 发布状态（1.未发布;2.已发布;3.已下架）
  optional uint32 state = 5;
  //页码
  int32 page = 6;
  //每页条数
  int32 page_size =7;
  // 游戏包类型 1.正式包;2.测试包
  optional uint32 game_package_type = 8;
}

message SearchGamesReply {
  //页码
  uint32 page = 1;
  //每页条数
  uint32 page_size = 2;
  //游戏总数
  uint32 total = 3;
  //游戏信息
  repeated game.v1.AdminGameInfo data = 4;
}


// 游戏版本列表请求
message GameVersionListRequest{
  // 游戏ID
  uint64 id = 1;
  // 上架/下架状态（0.未发布;1.上架;2.下架）)
  optional uint32 publish_state = 2;
}
// 游戏版本列表返回
message GameVersionListReply{
  // 游戏列表
  game.v1.GameAll latest = 1;
  // 游戏版本号列表
  repeated game.v1.GameOnlyVersion data = 2;
}

// 游戏创建请求
message CreateGameRequest {
  // 游戏名称
  string name = 1;
  // 游戏英文名称
  string name_en = 2;
  // 游戏类型
  uint32 type = 3;
  // 屏幕类型 0.未知; 1.竖屏; 2.横屏
  uint32 landscape = 4;
  // 游戏版本字符串标识
  string version = 5;
  // 游戏图标
  optional string icon = 6;
  // 游戏的应用ID，全局唯一
  string app_id = 7;
  // 厂商ID
  uint64 merchant_id = 8;
  // 标签
  repeated uint32 tag = 9;
  // 游戏包类型 1.正式包;2.测试包
  uint32 game_package_type = 10;
}
// 游戏创建响应
message CreateGameReply {
  // 游戏信息
  optional game.v1.GameInfo game = 1;
  // 游戏版本信息
  optional game.v1.GameVersionInfo game_version = 2;
}

// 游戏更新请求
message UpdateGameRequest {
  // UID
  optional uint64  uid = 1;
  // 游戏版本的唯一标识
  uint64 id = 2;
  // 游戏信息
  optional game.v1.GameVersionInfo game_version = 3;
}
// 游戏更新响应
message UpdateGameReply {
  // 版本审核进度(0.未知;1.基础信息;2.游戏资料;3.接入SDK;4.申请审核;5.上架入库)
  optional uint32 progress = 1;
  // 版本当前审核进度(0.未知;1.基础信息;2.游戏资料;3.接入SDK;4.申请审核;5.上架入库)
  optional uint32 progress_current = 2;
}

// 生成游戏AppID请求
message GenGameAppIDRequest {
  // 游戏英文名称
  optional string name_en = 1;
}
// 生成游戏AppID响应
message GenGameAppIDReply {
  // 游戏的应用ID，全局唯一
  string app_id = 1;
}

// 获得游戏版本号请求
message GenGameVersionRequest {
  // 游戏的唯一标识(1创建: 不传 ；2更新版本： 传game-id)
  //  optional uint64 game_id = 1;
}
// 获得游戏版本号响应
message GenGameVersionReply {
  // 游戏版本字符串标识
  string version = 2;
}

// 提交审核请求
message SubmitInspectRequest {
  // uid
  optional uint64 uid = 1;
  // 游戏版本ID
  uint64 game_version_id = 2;
  // 厂商ID
  uint64 merchant_id = 3;
  // 游戏的唯一标识
  uint64 game_id = 4;
  // 游戏的配置
  optional game.v1.GameVersionInfo game_version = 5;
}
// 提交审核响应
message SubmitInspectReply {
  // 游戏版本信息
  optional game.v1.GameVersionInfo game_version = 1;
  // 游戏审核信息
  optional game.v1.GameInspectInfo game_inspect = 2;
}


// 应用/游戏动态请求
message GameDynamicsRequest {
  // 厂商ID
  uint64 merchant_id = 1;
}
// 应用/游戏动态返回
message GameDynamicsReply {
  // 运营中
  uint32 pass_count = 1;
  // 待上架
  uint32 unpublish_count = 2;
  // 游戏版本信息
  repeated game.v1.GameVersionBaseInfo data = 3;
}

message GameFilter {
  //
  optional uint32 state = 1;
}

// 更新游戏包请求
message UpdateGamePackageRequest {
  // uid
  optional uint64 uid = 1;
  // 游戏版本的唯一标识
  uint64 game_version_id = 2;
  // 游戏包
  optional string package = 3;
  // 游戏包名
  optional string package_name = 4;
  // 游戏包大小
  optional uint64 package_size = 5;
}
// 游戏更新响应
message UpdateGamePackageReply {
  // 游戏包名
  optional string package_name = 4;
  // 游戏包大小
  optional string package_size = 5;
}


// 撤回审核请求
message WithdrawInspectRequest {
  //提交人uid
  optional uint64 uid = 1;
  //游戏审核ID
  uint64 id = 2;
}
// 撤回审核响应
message WithdrawInspectReply {
  // 游戏审核信息
  optional game.v1.GameInspectInfo game_inspect = 1;
  // 游戏审核信息
  optional game.v1.GameVersionInfo game_version = 2;
}


// 游戏版本创建请求
message CreateGameVersionRequest {
  // 游戏的唯一标识
  uint64 game_id = 1 ;
  // 是否带入上一个已发布的版本信息
  bool copy_enable = 2;
}
// 游戏版本创建响应
message CreateGameVersionReply {
  // 游戏信息
  optional game.v1.GameInfo game = 1;
  // 游戏版本信息
  optional game.v1.GameVersionInfo game_version = 2;
}


// 游戏版本详情请求
message GetGameVersionRequest{
  // 游戏版本ID
  uint64 id = 1;
}
// 游戏版本详情返回
message GetGameVersionReply{
  // 游戏版本信息
  game.v1.GameVersionInfo game_version = 1;
  // 游戏审核信息
  game.v1.GameInspectInfo game_inspect = 2;
}


// 游戏版本上架请求
message PublishGameVersionRequest {
  //提交人uid
  optional uint64 uid = 1;
  // 游戏版本的唯一标识
  uint64 id = 2;
}
// 游戏版本上架响应
message PublishGameVersionReply {
  // 游戏版本信息
  optional game.v1.GameVersionInfo game_version = 1;
  // 游戏审核信息
  optional game.v1.GameInspectInfo game_inspect = 2;
}

// 游戏版本下架请求
message UnpublishGameVersionRequest {
  //提交人uid
  optional uint64 uid = 1;
  // 游戏版本的唯一标识
  uint64 id = 2;
}
// 游戏版本下架响应
message UnpublishGameVersionReply {
  // 游戏版本信息
  optional game.v1.GameVersionInfo game_version = 1;
  // 游戏审核信息
  optional game.v1.GameInspectInfo game_inspect = 2;
}

// 游戏版本删除请求
message DeleteGameRequest {
  // 游戏的唯一标识
  uint64 game_id = 1;
}
// 游戏版本删除响应
message DeleteGameReply {
}

message SetGameEnableRequest {
  // 游戏的id
  uint64 game_id = 1;
  // 是否禁用：1 启用; 2 禁用
  uint32 enable = 2;
}

message SetGameEnableReply{
}


message GetAllPublishedGamesRequest {
}

message GetAllPublishedGamesReply {
  message PublishedGame{
    uint64 id = 1;
    string name = 2;
    string name_en = 3;
    string banner = 4;
  }
  repeated PublishedGame games = 1; 
}


