syntax = "proto3";

package admin.v1;

import "validate/validate.proto";
import "google/protobuf/timestamp.proto";
import "google/protobuf/empty.proto";
import "google/protobuf/field_mask.proto";

option go_package = "open-proto/open-admin/api/extern;extern";

// 商户渠道数据相关
service MerchantChannelForData {
  // 获取渠道链接过滤时间区间列表
  // 迭代25的需求：渠道的模板，是否支持广告，决定了数据报表是否要展示。通过这个接口，获取渠道链接不需要展示的时间区间列表。
  rpc FilterTimeRange (MerchantChannelForDataFilterTimeRangeRequest) returns (MerchantChannelForDataFilterTimeRangeReply) {}
}

message MerchantChannelForDataFilterTimeRangeRequest{}

message MerchantChannelForDataFilterTimeRangeReply{
  // 渠道链接过滤时间区间列表
  repeated MerchantChannelForDataFilterSite sites=1;
}

message MerchantChannelForDataFilterTimeRange{
  optional google.protobuf.Timestamp start_time = 1;
  optional google.protobuf.Timestamp end_time = 2;
}

message MerchantChannelForDataFilterSite{
  optional string site = 1;
  repeated MerchantChannelForDataFilterTimeRange time_ranges = 2;
}
