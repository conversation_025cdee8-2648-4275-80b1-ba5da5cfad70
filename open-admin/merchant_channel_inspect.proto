syntax = "proto3";

package admin.v1;

option go_package = "open-proto/open-admin/api/extern;extern";

import "google/api/annotations.proto";
import "validate/validate.proto";
import "google/protobuf/field_mask.proto";
import "google/protobuf/empty.proto";
import "google/protobuf/timestamp.proto";
import "open-proto/open-common/merchant_channel_inspect.proto";

// 商户渠道链接审核服务
service MerchantChannelInspect {
  rpc Search(merchantchannelinspect.v1.MerchantChannelInspectSearchRequest) returns (merchantchannelinspect.v1.MerchantChannelInspectSearchReply) {}
  rpc Save(merchantchannelinspect.v1.MerchantChannelInspectCreateRequest) returns (google.protobuf.Empty) {}
  rpc Detail(merchantchannelinspect.v1.MerchantChannelInspectDetailRequest) returns (merchantchannelinspect.v1.MerchantChannelInspectDetailReply) {}
  rpc Confirm(merchantchannelinspect.v1.MerchantChannelInspectConfirmRequest) returns (google.protobuf.Empty) {}
  rpc Submit(merchantchannelinspect.v1.MerchantChannelInspectCreateRequest) returns (google.protobuf.Empty) {}
  rpc Withdraw(merchantchannelinspect.v1.MerchantChannelInspectWithdrawRequest) returns (google.protobuf.Empty) {}
}
