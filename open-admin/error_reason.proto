syntax = "proto3";

package admin.v1;

import "errors/errors.proto";

option go_package = "open-proto/open-admin-user/api/extern;extern";

enum ErrorReason {
  option (errors.default_code) = 500;

  UNKNOWN_ERROR = 0; // 未知错误
  PARAMETER_ERROR = 1[(errors.code) = 400];  // 参数错误

  // User 相关 [500, 1000)
  USER_MOBILE_INCORRECT = 500[(errors.code) = 400]; // 你输入的 手机号 与预留的手机号不符，请重新输入！
  USER_EMAIL_INCORRECT = 501[(errors.code) = 400]; //  你输入的 邮箱 与预留的邮箱不符，请重新输入！
  USER_PASSWORD_INVALID = 502[(errors.code) = 400];  // 用户密码无效
  USER_MOBILE_EXIST = 503[(errors.code) = 400];  // 手机号已存在
  USER_EMAIL_EXIST = 504[(errors.code) = 400];  // 邮箱已存在

  // 消息 相关 [2000, 3000)
  // MESSAGE_PUBLISH_DELETE 删除发布状态的消息
  MESSAGE_PUBLISH_DELETE = 2000[(errors.code) = 400];
  MESSAGE_NOT_FOUND = 2001[(errors.code) = 404];
  MESSAGE_PUBLISH_UPDATE = 2002[(errors.code) = 400];
  MESSAGE_ENABLE_DELETE = 2003[(errors.code) = 400];
  PUBLISH_DISABLE_MESSAGE = 2004[(errors.code) = 400];
  PUBLISH_CANCEL_MESSAGE = 2005[(errors.code) = 400];
  CANCEL_UNPUBLISH_MESSAGE = 2006[(errors.code) =400];
  PUBLISH_PUBLISH_MESSAGE = 2007[(errors.code) = 400]; // deprecated
  UPDATE_CANCEL_UNPUBLISH_MESSAGE = 2008[(errors.code) =400]; // deprecated

  // GAME 相关 [4000,5000)
  NOT_EXIST_GAME = 3006[(errors.code) = 400]; // 游戏不存在
  NOT_EXIST_GAME_VERSION = 4000[(errors.code) = 400]; // 不存在相同游戏版本
  NOT_EXIST_GAME_INSPECT = 3012[(errors.code) = 400]; // 游戏审核不存在
  GAME_INSPECT_STATES_CHANGE = 3011[(errors.code) = 400]; // 游戏审核状态发生变化
  GAME_STATES_CHANGE = 3013[(errors.code) = 400]; // 游戏状态发生变化
  GAME_INSPECTING = 3007[(errors.code) = 400]; // 游戏版本审核中
  GAME_EN_NAME_INVALID = 3008[(errors.code) = 400]; // 游戏英文名不符合规范

  // 
  EXIST_APP_ID = 4001[(errors.code) = 400]; //已存在相同AppId
  EXIST_NAME_EN = 4002[(errors.code) = 400];//已存在相同NameEn
  EXIST_NAME = 4003[(errors.code) = 400];//已存在相同Name
  GAME_HAVE_PUBLISH = 4004[(errors.code) = 400]; // 游戏版本已发布
  EXIST_GAME_UN_PUBLISH = 4005[(errors.code) = 400]; //已存在未发布的游戏
  GAME_NOT_UN_PUBLISH = 4006[(errors.code) = 400]; // 游戏版本未下架
  GAME_INSPECTING_UNPUBLISH_FORBIDDEN = 4007[(errors.code) = 400]; //存在审核中版本，无法下架
  GAME_UNPUBLISH_INSPECTION_ALREADY_COMMITTED = 4008[(errors.code) = 400]; //已经提交游戏下架的申请
  GAME_PUBLISH_INSPECTION_ALREADY_COMMITTED = 4009[(errors.code) = 400]; //已经提交游戏上架的申请

  // 发行商（待废弃）
  PUBLISHER_ID_NOT_EXIST  = 5001 [(errors.code) = 404]; // 发行商不存在
  PUBLISHER_STATES_CHANGE = 5002 [(errors.code) = 400]; // 发行商的信息发生变化

  // 轮播图
  CAROUSEL_ID_NOT_EXIST = 6001 [(errors.code) = 404]; // 轮播图不存在
  CAROUSEL_STATES_CHANGE = 6002 [(errors.code) = 400]; // 轮播图配置状态发生变化
  CAROUSEL_NEED_AT_LEAST_ONE = 6003 [(errors.code) = 400]; // 至少需要保留一张轮播图
  CAROUSEL_NEED_AT_LEAST_THREE = 6004 [(errors.code) = 400]; // 至少需要保留三张轮播图

  // 商户 [7000,8000)
  MERCHANT_INFO_INVALID = 7000 [(errors.code) = 400]; // 发行商信息无效
  MERCHANT_INFO_CREDITCODE_INVALID = 7001[(errors.code) = 400];// 该代码已被注册，请重新输入！
  MERCHANT_NO_FOUND = 7002[(errors.code) = 400];// 发行商不存在
  MERCHANT_EXISTS = 7003[(errors.code) = 400];// 发行商已存在
  MERCHANT_SAVE_FAILED = 7004[(errors.code) = 400];// 发行商保存失败
  MERCHANT_REGISTER_FAILED = 7005[(errors.code) = 400];// 发行商注册失败
  MERCHANT_IS_ENABLE = 7006[(errors.code) = 400];// 发行商已启用
  MERCHANT_IS_DISABLE = 7007[(errors.code) = 400];// 发行商已禁用
  MERCHANT_REMOVE_FAILED = 7008[(errors.code) = 400];// 发行商删除失败
  MERCHANT_NAME_EXISTS = 7009[(errors.code) = 400];// 发行商名称已存在
  MERCHANT_EN_NAME_EXISTS = 7010[(errors.code) = 400];// 发行商英文名称已存在
  MERCHANT_FULL_NAME_EXISTS = 7011[(errors.code) = 400];// 发行商全称已存在
  MERCHANT_EMAIL_EXISTS = 7012[(errors.code) = 400];
  MERCHANT_EXIST_GAME_ONLINE_FORBIDDEN = 7013[(errors.code) = 400]; // 存在上架运营的游戏，请勿禁用！
  MERCHANT_EXIST_GAME_ONLINE_DEL = 7014[(errors.code) = 400]; // 存在上架运营的游戏，请勿删除！
  MERCHANT_EXIST_GAME_INSPECTING_FORBIDDEN = 7015[(errors.code) = 400]; // 存在审核中的游戏，请勿禁用！
  MERCHANT_EXIST_CHANNEL_ONLINE_FORBIDDEN = 7016[(errors.code) = 400]; // 存在渠道链接，请勿禁用！
  MERCHANT_IDENTITY_ID_EXISTS = 7017[(errors.code) = 400]; // 发行商身份ID已存在
  MERCHANT_UNAUTHORIZED = 7018[(errors.code) = 400]; // 发行商未认证
  MERCHANT_TEMPLATE_FORBIDDEN = 7019[(errors.code) = 400]; // 商户不支持该模板

  // 商户认证相关 [8000, 9000)
  MERCHANT_INSPECT_EXISTS = 8000[(errors.code) = 400]; //  存在审核中的信息，请先处理待审信息后修改！
  MERCHANT_INSPECT_PARAMETER_NOT_CHANGED  = 8001[(errors.code) = 400];//存在未修改字段 ！
  MERCHANT_INSPECT_STATES_CHANGE = 8002[(errors.code) = 400]; // 审核状态发生变化
  MERCHANT_INSPECT_NO_EXISTS = 8003[(errors.code) = 400]; //  审核信息不存在！
  MERCHANT_INSPECTING_EXISTS = 8004[(errors.code) = 400]; //  存在待审核的记录！

  // 网站需求相关 [9001,9500)
  SITE_DEMAND_NOT_EXISTS = 9001[(errors.code) = 404]; // 网站需求不存在
  SITE_DEMAND_GET_FAILED = 9002[(errors.code) = 500]; // 获取网站需求失败
  SITE_DEMAND_SEARCH_FAILED = 9003[(errors.code) = 500]; // 获取网站需求失败
  SITE_DEMAND_DELETE_FAILED = 9004[(errors.code) = 500]; // 获取网站需求失败
  SITE_DEMAND_UPDATE_FAILED = 9005[(errors.code) = 500]; // 获取网站需求失败
  SITE_DEMAND_STATE_CHANGED = 9006[(errors.code) = 400]; // 状态发生改变

  // merchant channel 相关 [10000,11000)
  MERCHANT_CHANNEL_NOT_EXISTS = 10000[(errors.code) = 404]; // 渠道链接不存在
  MERCHANT_CHANNEL_FIND_FAILED = 10001[(errors.code) = 500]; // 获取渠道链接失败
  MERCHANT_CHANNEL_DOMAIN_IS_VALID = 10002[(errors.code) = 400]; // 渠道域名不合法
  MERCHANT_CHANNEL_NAME_EXIST = 10003 [(errors.code)  = 400 ]; // 渠道链接已存在
  MERCHANT_CHANNEL_DOMAIN_EXIST = 10004 [(errors.code)  = 400 ]; // 渠道链接已存在
  MERCHANT_CHANNEL_UPDATE_FAILED = 10005 [(errors.code)  = 400 ]; // 更新渠道链接失败
  MERCHANT_CHANNEL_CREATE_FAILED = 10006 [(errors.code)  = 400 ]; // 创建渠道链接失败
  MERCHANT_CHANNEL_LOCKED = 10007 [(errors.code)  = 400 ]; // 渠道链接部署中，禁止操作
  MERCHANT_CHANNEL_DAILY_UPDATE_LIMIT = 10008 [(errors.code)  = 400 ]; // 更换模板次数已达上限
  MERCHANT_CHANNEL_STRATEGY_NOT_WHITELIST_FORBIDDEN = 10010 [(errors.code) = 400]; // 商户不支持该变现策略
  MERCHANT_CHANNEL_STRATEGY_NUMBER_LIMIT = 10009 [(errors.code) = 400]; // 商户变现策略数量超过限制
  MERCHANT_CHANNEL_TOTAL_NUMBER_LIMIT = 10011 [(errors.code) = 400]; // 商户渠道链接数量超过限制
  MERCHANT_CHANNEL_MERCHANT_UNAUTHORIZED = 10012 [(errors.code) = 400]; // 商户未认证，不支持该变现策略
  MERCHANT_CHANNEL_GAME_NOT_EXIST = 10013 [(errors.code) = 400]; // 商户渠道游戏不存在
  MERCHANT_CHANNEL_FORBIDDEN = 10014 [(errors.code) = 400]; // 商户渠道访问被拒绝
  MERCHANT_CHANNEL_BAD_STATE_CANT_SAVE_DRAFT = 10015[(errors.code) = 400]; // 渠道链接状态不正确，无法保存草稿
  MERCHANT_CHANNEL_BAD_STATE_CANT_SUBMIT = 10016[(errors.code) = 400]; // 渠道链接状态不正确，无法提交审核
  MERCHANT_CHANNEL_UPDATE_SAME_TEMPLATE = 10017[(errors.code) = 400]; // 渠道链接更新的模板与当前模板相同，请勿重复提交！

  // 商户渠道链接审核相关 [11000, 12000)
  MERCHANT_CHANNEL_INSPECT_EXISTS = 11000[(errors.code) = 400]; // 存在审核中的信息，请先处理待审信息后修改！
  MERCHANT_CHANNEL_INSPECT_PARAMETER_NOT_CHANGED  = 11001[(errors.code) = 400];// 存在未修改字段 ！
  MERCHANT_CHANNEL_INSPECT_STATES_CHANGE = 11002[(errors.code) = 400]; // 审核状态发生变化
  MERCHANT_CHANNEL_INSPECT_NOT_EXISTS = 11003[(errors.code) = 400]; // 审核信息不存在！
  MERCHANT_CHANNEL_INSPECTING_EXISTS = 11004[(errors.code) = 400]; // 存在待审核的记录！
  MERCHANT_CHANNEL_INSPECT_BAD_STATE_CANT_SAVE_DRAFT = 11005[(errors.code) = 400]; // 审核状态不正确，无法保存草稿
  MERCHANT_CHANNEL_UP_INSPECT_EXISTS = 11006[(errors.code) = 400]; // 存在待上架的审核记录！
  MERCHANT_CHANNEL_DOWN_INSPECT_EXISTS = 11007[(errors.code) = 400]; // 存在待下架的审核记录！
  MERCHANT_CHANNEL_VIOLATION_DOWN_INSPECT_EXISTS = 11008[(errors.code) = 400]; // 存在待违规下架的审核记录！
  MERCHANT_CHANNEL_VIOLATION_DOWN_APPEAL_INSPECT_EXISTS = 11009[(errors.code) = 400]; // 存在待违规下架申诉的审核记录！
  MERCHANT_CHANNEL_STATE_NOT_DOWN = 11010[(errors.code) = 400]; // 渠道链接状态不是下架状态
  MERCHANT_CHANNEL_STATE_NOT_PUBLISHED = 11011[(errors.code) = 400]; // 渠道链接状态不是已发布状态
  MERCHANT_CHANNEL_STATE_NOT_VIOLATION_DOWN = 11012[(errors.code) = 400]; // 渠道链接状态不是违规下架状态
  MERCHANT_CHANNEL_VIOLATION_DOWN_REPUBLISH_INSPECT_EXISTS = 11013[(errors.code) = 400]; // 存在待违规下架重新上架的审核记录！
  MERCHANT_CHANNEL_STATE_NOT_INSPECTING = 11014[(errors.code) = 400]; // 渠道链接状态不是审核中状态
}
