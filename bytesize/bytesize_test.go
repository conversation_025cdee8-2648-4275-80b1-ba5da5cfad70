package bytesize

import (
	"fmt"
	"testing"
)

func TestConvertFileSize(t *testing.T) {
	fileSizeInKB := uint64(900)
	convertedSize := Bytes(fileSizeInKB)
	fmt.Println(convertedSize)

	fileSizeInKB = uint64(1700)
	convertedSize = Bytes(fileSizeInKB)
	fmt.Println(convertedSize)

	fileSizeInKB = uint64(1024.0*1024*1 + 600*1024)
	convertedSize = Bytes(fileSizeInKB)
	fmt.Println(convertedSize)

	convertedSize = Bytes(0)
	fmt.Println(convertedSize)
}
