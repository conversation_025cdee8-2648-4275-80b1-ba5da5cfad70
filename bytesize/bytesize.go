package bytesize

import (
	"fmt"
	"math"
)

// Bytes produces a human readable representation of an IEC size.
//
// See also: ParseBytes.
//
// Bytes(82854982) -> 79 MiB
func Bytes(s uint64) string {
	sizes := []string{"B", "KB", "MB", "GB", "TB", "PB", "EB"}
	return humanateBytes(s, 1024, sizes)
}

func humanateBytes(bytes uint64, base float64, sizes []string) string {
	if bytes == 0 {
		return "0 " + sizes[0]
	}
	i := int(math.Floor(math.Log(float64(bytes)) / math.Log(base)))
	if i == 0 {
		return fmt.Sprintf("%d %s", bytes, sizes[i])
	}
	size := float64(bytes) / math.Pow(float64(base), float64(i))
	if size == math.Floor(size) {
		return fmt.Sprintf("%.0f %s", size, sizes[i])
	}
	return fmt.Sprintf("%.1f %s", size, sizes[i])
}
func logn(n, b float64) float64 {
	return math.Log(n) / math.Log(b)
}
