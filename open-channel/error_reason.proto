syntax = "proto3";

package openchannel.v1;

import "errors/errors.proto";

option go_package = "open-proto/open-channel/api/extern;extern";

enum ErrorReason {
  option (errors.default_code) = 500;

  UNKNOWN_ERROR = 0;

  // GAME 相关 [1000,1000)
  NOT_EXIST_GAME = 1001[(errors.code) = 404]; // 游戏不存在
  NOT_EXIST_PUBLISH_GAME = 1002[(errors.code) = 404]; // 此游戏未存在已发布的版本
  SEARCH_GAME_KEYWORD_EMPTY = 1003[(errors.code) = 400]; // 搜索关键字不能为空

  EMPTY_COMPANY_NAME = 2001[(errors.code) = 400]; // 公司名为空
  EMPTY_CONTACT_NUMBER = 2002[(errors.code) = 400]; // 联系号码为空
}
