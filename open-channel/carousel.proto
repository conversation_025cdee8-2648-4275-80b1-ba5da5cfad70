syntax = "proto3";

package openchannel.v1;

import "google/api/annotations.proto";
import "open-proto/open-common/metadata.proto";
import "google/protobuf/timestamp.proto";
import "google/protobuf/empty.proto";
option go_package = "open-proto/open-channel/api/extern;extern";

service Carousel {
  // 获取轮播图配置
  rpc GetCarousels(GetCarouselsRequest) returns (GetCarouselsReply) {}
  // 重载轮播图列表的缓存
  rpc ReloadCarousels(ReloadCarouselsRequest) returns (ReloadCarouselsReply) {}
}

message GetCarouselsRequest {
  optional uint32 type = 1;
  string lang = 2;
}
message GetCarouselsReply {
  message Carousel {
    string banner = 1;
    uint64 game_id = 2;
    uint32 order = 3;
    uint32 type = 4;
    string name = 5;
  }
  repeated Carousel carousels = 1;
}

message ReloadCarouselsRequest {}
message ReloadCarouselsReply {}