syntax = "proto3";

package openchannel.v1;

import "google/api/annotations.proto";
import "open-proto/open-common/metadata.proto";
import "google/protobuf/timestamp.proto";
import "google/protobuf/empty.proto";
option go_package = "open-proto/open-channel/api/extern;extern";

service Publisher {
  // 创建发行商
  rpc CreatePublisher(CreatePublisherRequest) returns (CreatePublisherReply) {}
}

message CreatePublisherRequest {
  // 公司名
  string company_name = 1;
  // 联系人
  optional string contact_person = 2;
  // 联系方式
  string contact_number = 3;
  // 联系邮箱
  optional string contact_email = 4;
}

message CreatePublisherReply {}
