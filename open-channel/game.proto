syntax = "proto3";

package openchannel.v1;

import "google/protobuf/timestamp.proto";
import "google/protobuf/empty.proto";
option go_package = "open-proto/open-channel/api/extern;extern";

service Game {
  rpc GetGame(GetGameRequest) returns (GetGameReply) {};
  rpc WeekHotList(WeekHotListRequest) returns (WeekHotListReply) {};
  rpc ListGameRecommend(ListGameRecommendRequest) returns (ListGameRecommendReply) {};
  rpc ListGames(ListGamesRequest) returns (ListGamesReply) {};
  rpc ListGameTypeGames(ListGameTypeGamesRequest) returns (ListGameTypeGamesReply) {};
  rpc LikeGame(LikeGameRequest) returns (LikeGameReply) {};
  rpc ListSimilarGame(ListSimilarGameRequest) returns (ListSimilarGameReply) {};
  rpc SearchGame(SearchGameRequest) returns (SearchGameReply) {}
  rpc CreateOrUpdateGameCache(CreateOrUpdateGameCacheRequest) returns (.google.protobuf.Empty) {}
  rpc ReloadGameCache(ReloadGameCacheRequest) returns (.google.protobuf.Empty) {}
  rpc ReloadAllGameI18nCache(.google.protobuf.Empty) returns (.google.protobuf.Empty) {}
  rpc GetGameI18nData(GetGameI18nDataRequest) returns (GetGameI18nDataReply) {}
}

message GetGameI18nDataRequest {
  uint64 id = 1;
  uint32 select_lang = 2;
}

message GetGameI18nDataReply {
  uint64 id = 1;
  string name = 2;
  string description = 3;
  string how_to_play = 4;
}


message ReloadGameCacheRequest {
  uint32 reload_type = 1;
  uint64  game_id = 2;
}

message CreateOrUpdateGameCacheRequest {
  uint64 game_id = 1;
}

message SearchGameRequest {
  int32 page_size = 1;
  int32 page = 2;
  // 搜索内容
  string q = 3;
  string  language = 4;
}

message SearchGameReply {
  repeated GameData data = 1;
  int32 total = 2;
}


message ListSimilarGameRequest {
  string language = 1;
  repeated uint64 game_ids = 2;
}

message ListSimilarGameReply {
  repeated  GameData data = 1;
}


message LikeGameRequest {
  uint64 game_id = 1;
}

message LikeGameReply {
  int64 like_count = 1;
}

message ListGamesRequest {
  int32 page_size = 3;
  int32 page = 2;
  ListGameFilter filter = 1;
  string language = 4;
}

message ListGameFilter {
  // 类型
  optional uint32 type = 1;
  // 标签
  repeated uint32 tags = 2;
}

message ListGamesReply {
  repeated GameData data = 1;
  int32 total = 2;
}

message ListGameTypeGamesRequest {
  int32 page_size = 1;
  int32 page = 2;
  string language = 3;
}

message ListGameTypeGamesReply {
  repeated GameType data = 1;
  int32 total = 2;
}

message GameType {
  string name = 1;
  uint32 id = 3;
  repeated GameData games = 2;
}

message ListGameRecommendRequest {
  string language = 1;
}

message ListGameRecommendReply {
  repeated GameData data = 1;
}

message WeekHotListRequest {
  string language = 1;
}

message WeekHotListReply {
  repeated GameData data = 1;
}


message GetGameRequest {
  // 游戏ID
  uint64 game_id = 1;
  // 语言
  string language = 2;
  // 用户id
  uint64 uid = 3;
}


message GetGameReply {
  GameData game = 1;
}

message GameData {
  uint64 game_id = 1;
  optional string banner = 2;
  repeated uint32 tags = 3;
  optional string description = 4;
  optional string name = 5;
  optional string icon = 6;
  optional int64  like_count = 7;
  optional int64  play_count = 8;
  optional int64  yesterday_play_count = 9;
  repeated string country = 10;
  optional string how_to_play = 11;
  optional string game_url = 12;
  optional double  rating = 13;
  optional uint32 game_type = 14;
  optional uint32 landscape = 15;
  optional string flash = 16;
  optional double package_size = 17;
  .google.protobuf.Timestamp update_at = 18;
  optional uint32 check_loading_fast_3g = 19;
  optional uint32 check_loading_slow_3g = 20;
  repeated string matching = 21;
  optional string app_id = 22;
  repeated uint32 languages = 23;
}