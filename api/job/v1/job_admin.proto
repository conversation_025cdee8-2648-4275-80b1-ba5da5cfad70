syntax = "proto3";

package api.job.v1;

option go_package = "git.minigame.vip/minicloud/service/adsense-bot/api/job/v1;v1";
option java_multiple_files = true;
option java_package = "api.job.v1";
import "google/api/annotations.proto";
import "google/protobuf/empty.proto";
service JobAdmin {
  rpc StartJob(StartJobRequest) returns (google.protobuf.Empty) {
    option (google.api.http) = {
      post: "/v1/admin/job:start"
      body: "*"
    };
  }
  rpc AutoMigrate(AutoMigrateRequest) returns (google.protobuf.Empty) {
    option (google.api.http) = {
      post: "/v1/admin/auto-migrate"
      body: "*"
    };
  }
}

message  StartJobRequest {
  repeated string names = 1;
}

message  AutoMigrateRequest {
  repeated int32 years = 1;
}
