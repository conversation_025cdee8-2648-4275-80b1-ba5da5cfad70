package cldr

import (
	"encoding/json"
	"github.com/google/wire"
	"io"
	"os"
)

var ProviderSet = wire.NewSet(NewCldr)

type territories struct {
	Main struct {
		En struct {
			LocaleDisplayNames struct {
				Territories map[string]string `json:"territories"`
			} `json:"localeDisplayNames"`
		} `json:"en"`
	} `json:"main"`
}

type Cldr struct {
	nameCode map[string]string
}

func NewCldr() *Cldr {
	var territories territories

	file, err := os.Open("./extern/miniutils/cldr/territories.json")
	if err != nil {
		panic(err)
	}
	defer file.Close()

	bytes, err := io.ReadAll(file)
	if err != nil {
		panic(err)
	}

	err = json.Unmarshal(bytes, &territories)
	if err != nil {
		panic(err)
	}
	nameToCode := make(map[string]string)
	for code, name := range territories.Main.En.LocaleDisplayNames.Territories {
		nameToCode[name] = code
	}
	var cldr Cldr
	cldr.nameCode = nameToCode
	return &cldr
}

func (c *Cldr) GetCodeByName(name string) string {
	if code, exists := c.nameCode[name]; exists {
		return code
	}
	return "ZZ"
}
