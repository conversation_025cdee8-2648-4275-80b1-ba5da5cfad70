GOPATH:=$(shell go env GOPATH)
VERSION=$(shell git describe --tags --always)
INTERNAL_PROTO_FILES=$(shell find internal -name *.proto)
API_PROTO_FILES=$(shell find api -name *.proto)
AUDIT_PROTO_FILES=$(shell find extern/minicommon/proto/audit -name *.proto)
OPEN_ADMIN_PROTO_FILES=$(shell find extern/open-proto/open-admin/merchant_channel_for_data.proto)
BRANCH=$(shell git branch  --show-current)
SERVICE=adsense-bot
.PHONY: init
# init env
init:
	go install google.golang.org/protobuf/cmd/protoc-gen-go@latest
	go install google.golang.org/grpc/cmd/protoc-gen-go-grpc@latest
	go install github.com/go-kratos/kratos/cmd/kratos/v2@latest
	go install github.com/go-kratos/kratos/cmd/protoc-gen-go-http/v2@latest
	go install github.com/google/gnostic/cmd/protoc-gen-openapi@latest
	go install github.com/google/wire/cmd/wire@latest



.PHONY: docker-prod
docker-prod:
	docker buildx build \
      --platform linux/amd64 --push -t docker.minigame.vip:9443/minicloud/$(SERVICE):$(VERSION) .

# 生成测试版本镜像并且推送到测试仓库
.PHONY: docker-dev
docker-dev:
	docker buildx build \
      --platform linux/amd64 --push -t docker.minigame.vip:9443/minicloud/$(SERVICE):$(BRANCH) .


.PHONY: config
# generate internal proto
config:
	protoc --proto_path=./internal \
	       --proto_path=./third_party \
 	       --go_out=paths=source_relative:./internal \
	       $(INTERNAL_PROTO_FILES)

.PHONY: api
# generate api proto
api:
	protoc --proto_path=./api \
	       --proto_path=./third_party \
 	       --go_out=paths=source_relative:./api \
 	       --go-http_out=paths=source_relative:./api \
 	       --go-grpc_out=paths=source_relative:./api \
	       --openapi_out=fq_schema_naming=true,default_response=false:. \
	       --openapiv2_out ./api \
           --openapiv2_opt logtostderr=true \
           --openapiv2_opt json_names_for_fields=false \
	       $(API_PROTO_FILES)
	
	make api-extern

.PHONY: api-extern
api-extern:
	protoc --proto_path=./extern \
		   --proto_path=./third_party \
	 	   --go_out=./extern/miniutils/audit \
	 	   --go-http_out=./extern/miniutils/audit \
	 	   --go-grpc_out=./extern/miniutils/audit \
	 	   --go-errors_out=./extern/miniutils/audit \
		   --validate_out=./extern/miniutils/audit,lang=go:./extern/miniutils/audit \
		   $(AUDIT_PROTO_FILES)
	protoc --proto_path=./extern \
		   --proto_path=./third_party \
 	       --go_out=paths=source_relative:./api \
 	       --go-grpc_out=paths=source_relative:./api \
 	       --go-errors_out=paths=source_relative:./api \
 	       --validate_out=paths=source_relative,lang=go:./api \
	       $(OPEN_ADMIN_PROTO_FILES)
.PHONY: ent
ent:
	cd internal/data && ent generate --feature sql/upsert,sql/modifier --target ./ent ./ent/schema
	

.PHONY: build
# build
build:
	mkdir -p bin/ && go build -ldflags "-X main.Version=$(VERSION)" -o ./bin/ ./...

.PHONY: generate
# generate
generate:
	go mod tidy
	go get github.com/google/wire/cmd/wire@latest
	go generate ./...

.PHONY: all
# generate all
all:
	make api;
	make config;
	make generate;

# show help
help:
	@echo ''
	@echo 'Usage:'
	@echo ' make [target]'
	@echo ''
	@echo 'Targets:'
	@awk '/^[a-zA-Z\-\_0-9]+:/ { \
	helpMessage = match(lastLine, /^# (.*)/); \
		if (helpMessage) { \
			helpCommand = substr($$1, 0, index($$1, ":")); \
			helpMessage = substr(lastLine, RSTART + 2, RLENGTH); \
			printf "\033[36m%-22s\033[0m %s\n", helpCommand,helpMessage; \
		} \
	} \
	{ lastLine = $$0 }' $(MAKEFILE_LIST)

.DEFAULT_GOAL := help

.PHONY: wire
wire:
	cd cmd/adsense-bot && wire