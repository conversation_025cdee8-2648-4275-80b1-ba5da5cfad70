//go:build wireinject
// +build wireinject

// The build tag makes sure the stub is not built in the final build.

package main

import (
	"github.com/go-kratos/kratos/v2"
	"github.com/go-kratos/kratos/v2/log"
	"github.com/google/wire"

	"git.minigame.vip/minicloud/service/adsense-bot/internal/biz"
	"git.minigame.vip/minicloud/service/adsense-bot/internal/conf"
	"git.minigame.vip/minicloud/service/adsense-bot/internal/data"
	"git.minigame.vip/minicloud/service/adsense-bot/internal/server"
	"git.minigame.vip/minicloud/service/adsense-bot/internal/service"
	"git.minigame.vip/minicloud/service/adsense-bot/pkg/bot"
	"git.minigame.vip/minicloud/service/adsense-bot/pkg/email"
)

// wireApp init kratos application.
func wireApp(*conf.Server, *conf.Data, *conf.Job, *conf.Google, *conf.Bot, *conf.Adsense, *conf.Email, *conf.Djs, log.Logger, *conf.Audit, *conf.Auth, *conf.Imap, *conf.Analytics, *conf.TgAd, *conf.ChannelGaReport, *conf.ExternalPurchaseAPI, *conf.OpenAdmin) (*kratos.App, func(), error) {
	panic(wire.Build(email.ProviderSet, server.ProviderSet, data.ProviderSet, biz.ProviderSet, service.ProviderSet, bot.ProviderSet, newApp))
}
