nokyc要只显示游戏内广告收入，kyb和白名单还要判断域名选择的是什么模板如果是无广告模板的域名只显示游戏内广告收入，有广告模板的域名显示所有广告收入，还要判断模板切换，如果一个域名1-9号是无广告就1-9号只统计游戏内广告收入，10-20改为有广告就10-20号统计所有广告收入

有一个网站部署历史接口，有以下字段:
商户等级 nokyc kyb 白名单
域名选择的模板是否是无广告模板
网站更新模板时间

如果存在没有对应历史记录的网站，则返回所有的广告数据


游戏内广告收入 指 MANUAL_INTERSTITIAL MANUAL_REWARDED 这两种广告类型


7.15 变更:

有一个网站部署历史接口，有以下字段:
域名
域名变现策略 1 纯净版 2 普通版
网站更新模板时间


纯净版仅显示游戏内广告收入，普通版显示所有广告收入


7.22

实际的接口返回一个列表，返回网站需要只显示游戏内广告的列表，每个数组有网站和对应的起止时间