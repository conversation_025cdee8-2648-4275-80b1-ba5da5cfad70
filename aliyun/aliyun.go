package aliyun

import (
	"errors"
	"fmt"
	cdn20180510 "github.com/alibabacloud-go/cdn-20180510/v4/client"
	openapi "github.com/alibabacloud-go/darabonba-openapi/v2/client"
	sdkerrors "github.com/aliyun/alibaba-cloud-sdk-go/sdk/errors"
	"github.com/aliyun/alibaba-cloud-sdk-go/sdk/requests"
	"github.com/aliyun/alibaba-cloud-sdk-go/services/alidns"
	"github.com/aliyun/alibaba-cloud-sdk-go/services/cas"
	"github.com/aliyun/alibaba-cloud-sdk-go/services/cdn"
)

type GeneralConfig struct {
	// 地域id，不确定的话就填 "ap-southeast-1"
	RegionId        string
	AccessKeyId     string
	AccessKeySecret string
}

type AddCdnDomainOptions struct {
	DomainName string
	SourceIP   string
	SourcePort int
}

func IsDomainRecordDuplicateError(err error) bool {
	if serverError, ok := err.(*sdkerrors.ServerError); ok {
		return serverError.ErrorCode() == "DomainRecordDuplicate"
	}
	return false
}

// 添加CDN域名。添加后还需要添加cname解析记录
func AddCdnDomain(cfg *GeneralConfig, options *AddCdnDomainOptions) (*cdn.AddCdnDomainResponse, error) {
	const cdnSourceFormat = `[{"content": "%s","type": "ipaddr","priority": "20","port": %d,"weight": "15"}]`
	client, err := cdn.NewClientWithAccessKey(cfg.RegionId, cfg.AccessKeyId, cfg.AccessKeySecret)
	if err != nil {
		return nil, err
	}
	req := cdn.CreateAddCdnDomainRequest()
	req.DomainName = options.DomainName
	req.CdnType = "web"
	req.Sources = fmt.Sprintf(cdnSourceFormat, options.SourceIP, options.SourcePort)
	req.Scope = "overseas"
	return client.AddCdnDomain(req)
}

type AddDomainRecordOptions struct {
	//example.com
	DomainName string
	//www
	Record string
	//a
	Type string
	//127.0.0.1
	Value string
}

type SetCdnDomainCertificateOptions struct {
	DomainName string
	// ----BEGIN CERTIFICATE----- MIIFz****-----END CERTIFICATE-----
	Crt string
	//----BEGIN RSA PRIVATE KEY-----QswCQ****----END RSA PRIVATE KEY-----
	Key      string
	CertName string
}

// 设置cdn的域名证书
func SetCdnDomainCertificate(cfg *GeneralConfig, options *SetCdnDomainCertificateOptions) (*cdn.SetDomainServerCertificateResponse, error) {
	client, err := cdn.NewClientWithAccessKey(cfg.RegionId, cfg.AccessKeyId, cfg.AccessKeySecret)
	if err != nil {
		return nil, err
	}
	req := cdn.CreateSetDomainServerCertificateRequest()
	req.DomainName = options.DomainName
	req.ServerCertificateStatus = "on"
	req.ServerCertificate = options.Crt
	req.PrivateKey = options.Key
	req.CertType = "upload"
	req.CertName = options.CertName
	return client.SetDomainServerCertificate(req)
}

type AddDnsRecordOptions struct {
	Record     string
	Type       string
	Value      string
	DomainName string
}

// 添加dns记录
func AddDnsRecord(cfg *GeneralConfig, options *AddDnsRecordOptions) (*alidns.AddDomainRecordResponse, error) {
	client, err := alidns.NewClientWithAccessKey(cfg.RegionId, cfg.AccessKeyId, cfg.AccessKeySecret)
	if err != nil {
		return nil, err
	}
	var req = alidns.CreateAddDomainRecordRequest()
	req.RR = options.Record
	req.Type = options.Type
	req.Value = options.Value
	req.DomainName = options.DomainName
	return client.AddDomainRecord(req)
}

// 列出dns记录
func ListDnsRecords(cfg *GeneralConfig, domainName string) (*alidns.DescribeDomainRecordsResponse, error) {
	client, err := alidns.NewClientWithAccessKey(cfg.RegionId, cfg.AccessKeyId, cfg.AccessKeySecret)
	if err != nil {
		return nil, err
	}
	var req = alidns.CreateDescribeDomainRecordsRequest()
	req.DomainName = domainName
	resp, err := client.DescribeDomainRecords(req)
	if err != nil {
		return nil, err
	}
	if !resp.IsSuccess() {
		return nil, errors.New(resp.String())
	}
	return resp, nil
}

// 删除dns记录
func DeleteDnsRecord(cfg *GeneralConfig, recordId string) (*alidns.DeleteDomainRecordResponse, error) {
	client, err := alidns.NewClientWithAccessKey(cfg.RegionId, cfg.AccessKeyId, cfg.AccessKeySecret)
	if err != nil {
		return nil, err
	}
	req := alidns.CreateDeleteDomainRecordRequest()
	req.RecordId = recordId
	return client.DeleteDomainRecord(req)
}

// 获取域名校验内容，通常用于第一次创建cdn
func DescribeCdnVerifyContent(cfg *GeneralConfig, domainName string) (*cdn.DescribeVerifyContentResponse, error) {
	client, err := cdn.NewClientWithAccessKey(cfg.RegionId, cfg.AccessKeyId, cfg.AccessKeySecret)
	if err != nil {
		return nil, err
	}
	req := cdn.CreateDescribeVerifyContentRequest()
	req.DomainName = domainName
	return client.DescribeVerifyContent(req)
}

// 检验阿里云域名所有权
func VerifyCdnDomainOwner(cfg *GeneralConfig, domainName string) (*cdn.VerifyDomainOwnerResponse, error) {
	client, err := cdn.NewClientWithAccessKey(cfg.RegionId, cfg.AccessKeyId, cfg.AccessKeySecret)
	if err != nil {
		return nil, err
	}
	req := cdn.CreateVerifyDomainOwnerRequest()
	req.DomainName = domainName
	req.VerifyType = "dnsCheck"
	return client.VerifyDomainOwner(req)
}

// 获取cdn的域名状态
func DescribeCdnDomainDetail(cfg *GeneralConfig, domainName string) (*cdn.DescribeCdnDomainDetailResponse, error) {
	client, err := cdn.NewClientWithAccessKey(cfg.RegionId, cfg.AccessKeyId, cfg.AccessKeySecret)
	if err != nil {
		return nil, err
	}
	req := cdn.CreateDescribeCdnDomainDetailRequest()
	req.DomainName = domainName
	return client.DescribeCdnDomainDetail(req)
}

// 列出cdn域名
func ListCdnDomains(cfg *GeneralConfig) (*cdn.DescribeUserDomainsResponse, error) {
	const pageSize = 500
	client, err := cdn.NewClientWithAccessKey(cfg.RegionId, cfg.AccessKeyId, cfg.AccessKeySecret)
	if err != nil {
		return nil, err
	}
	req := cdn.CreateDescribeUserDomainsRequest()
	req.PageSize = requests.NewInteger(pageSize)
	return client.DescribeUserDomains(req)
}

type ListCdnCertsOptions struct {
}

// 列出cdn证书
func ListCdnCerts(cfg *GeneralConfig, domainName string) (*cdn.DescribeDomainCertificateInfoResponse, error) {
	client, err := cdn.NewClientWithAccessKey(cfg.RegionId, cfg.AccessKeyId, cfg.AccessKeySecret)
	if err != nil {
		return nil, err
	}
	req := cdn.CreateDescribeDomainCertificateInfoRequest()
	req.DomainName = domainName
	return client.DescribeDomainCertificateInfo(req)
}

// 获取证书的crt和key
func GetCertDetail(cfg *GeneralConfig, certId string) (*cdn20180510.DescribeCdnCertificateDetailByIdResponse, error) {
	config := &openapi.Config{
		AccessKeyId:     &cfg.AccessKeyId,
		AccessKeySecret: &cfg.AccessKeySecret,
	}
	config.RegionId = &cfg.RegionId
	client, err := cdn20180510.NewClient(config)
	if err != nil {
		return nil, err
	}
	req := &cdn20180510.DescribeCdnCertificateDetailByIdRequest{
		CertId: &certId,
	}
	return client.DescribeCdnCertificateDetailById(req)
}

// 列出用户所有的证书
func ListUserCerts(cfg *GeneralConfig) (*cas.ListUserCertificateOrderResponse, error) {
	client, err := cas.NewClientWithAccessKey(cfg.RegionId, cfg.AccessKeyId, cfg.AccessKeySecret)
	if err != nil {
		return nil, err
	}
	req := cas.CreateListUserCertificateOrderRequest()
	req.OrderType = "CERT"
	return client.ListUserCertificateOrder(req)
}

// 删除用户的证书
func DeleteUserCert(cfg *GeneralConfig, certId string) (*cas.DeleteUserCertificateResponse, error) {
	client, err := cas.NewClientWithAccessKey(cfg.RegionId, cfg.AccessKeyId, cfg.AccessKeySecret)
	if err != nil {
		return nil, err
	}
	req := cas.CreateDeleteUserCertificateRequest()
	req.CertId = requests.Integer(certId)
	return client.DeleteUserCertificate(req)
}

type RefreshCdnCacheOptions struct {
	Path string
	Type string
}

// 刷新cdn缓存
func RefreshCdnCache(cfg *GeneralConfig, options *RefreshCdnCacheOptions) (*cdn.RefreshObjectCachesResponse, error) {
	client, err := cdn.NewClientWithAccessKey(cfg.RegionId, cfg.AccessKeyId, cfg.AccessKeySecret)
	if err != nil {
		return nil, err
	}
	req := cdn.CreateRefreshObjectCachesRequest()
	req.ObjectPath = options.Path
	req.ObjectType = options.Type
	return client.RefreshObjectCaches(req)
}

// 获取cdn的刷新配额，每日的url刷新剩余次数/目录刷新剩余次数
func GetCdnRefreshQuota(cfg *GeneralConfig) (*cdn.DescribeRefreshQuotaResponse, error) {
	client, err := cdn.NewClientWithAccessKey(cfg.RegionId, cfg.AccessKeyId, cfg.AccessKeySecret)
	if err != nil {
		return nil, err
	}
	req := cdn.CreateDescribeRefreshQuotaRequest()
	return client.DescribeRefreshQuota(req)
}

// 列出cdn的刷新任务
func ListCdnRefreshTasks(cfg *GeneralConfig, pageNumber int, pageSize int) (*cdn.DescribeRefreshTasksResponse, error) {
	client, err := cdn.NewClientWithAccessKey(cfg.RegionId, cfg.AccessKeyId, cfg.AccessKeySecret)
	if err != nil {
		return nil, err
	}
	req := cdn.CreateDescribeRefreshTasksRequest()
	req.PageNumber = requests.NewInteger(pageNumber)
	req.PageSize = requests.NewInteger(pageSize)
	return client.DescribeRefreshTasks(req)
}

type GetCdnConfigsOptions struct {
	DomainName    string
	FunctionNames string
	ConfigId      string
}

// 获取CDN配置
func GetCdnConfigs(cfg *GeneralConfig, options *GetCdnConfigsOptions) (*cdn.DescribeCdnDomainConfigsResponse, error) {
	client, err := cdn.NewClientWithAccessKey(cfg.RegionId, cfg.AccessKeyId, cfg.AccessKeySecret)
	if err != nil {
		return nil, err
	}
	req := cdn.CreateDescribeCdnDomainConfigsRequest()
	req.DomainName = options.DomainName
	req.FunctionNames = options.FunctionNames
	req.ConfigId = options.ConfigId
	return client.DescribeCdnDomainConfigs(req)
}

type SetCdnConfigsOptions struct {
	DomainName string
	Functions  string
}

// 设置cdn配置
func SetCdnConfigs(cfg *GeneralConfig, options *SetCdnConfigsOptions) (*cdn.BatchSetCdnDomainConfigResponse, error) {
	client, err := cdn.NewClientWithAccessKey(cfg.RegionId, cfg.AccessKeyId, cfg.AccessKeySecret)
	if err != nil {
		return nil, err
	}
	req := cdn.CreateBatchSetCdnDomainConfigRequest()
	req.DomainNames = options.DomainName
	req.Functions = options.Functions
	return client.BatchSetCdnDomainConfig(req)
}
