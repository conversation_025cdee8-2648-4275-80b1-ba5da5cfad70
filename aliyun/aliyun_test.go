package aliyun

import (
	"fmt"
	"github.com/aliyun/alibaba-cloud-sdk-go/services/alidns"
	"testing"
)

func getAliyunCdnKey() *struct {
	id     string
	secret string
} {
	return &struct {
		id     string
		secret string
	}{
		id:     "LTAI5tBmjnpgmU6EUyraUzNc",
		secret: "******************************",
	}
}

func getAliyunDnsKey() *struct {
	id     string
	secret string
} {
	return &struct {
		id     string
		secret string
	}{
		id:     "LTAI5tKNPBNmfKgLbjxp5HSD",
		secret: "******************************",
	}
}

func TestAddCdnDomain(t *testing.T) {
	KeyID := "LTAI5tBmjnpgmU6EUyraUzNc"
	KeySecret := "******************************"
	var cfg = &GeneralConfig{
		RegionId:        "ap-southeast-1",
		AccessKeyId:     KeyID,
		AccessKeySecret: KeySecret,
	}
	var options = &AddCdnDomainOptions{
		DomainName: "beezee.fun",
		SourceIP:   "***********",
		SourcePort: 80,
	}
	_, err := AddCdnDomain(cfg, options)
	if err != nil {
		t.Error(err)
	}
}

func TestAddDnsRecord(t *testing.T) {
	KeyID := "LTAI5tKNPBNmfKgLbjxp5HSD"
	KeySecret := "******************************"
	var cfg = &GeneralConfig{
		RegionId:        "ap-southeast-1",
		AccessKeyId:     KeyID,
		AccessKeySecret: KeySecret,
	}
	var options = &AddDnsRecordOptions{
		Record:     "test-cache-rule-01",
		Type:       "CNAME",
		Value:      "test-cache-rule-01.beezee.fun.w.cdngslb.com",
		DomainName: "beezee.fun",
	}
	_, err := AddDnsRecord(cfg, options)
	if err != nil {
		if IsDomainRecordDuplicateError(err){
			fmt.Println("IsDomainRecordDuplicateError")
		}else{
			t.Error(err)
		}
	}
}

func TestGeneral(t *testing.T) {
	KeyID := "LTAI5tKNPBNmfKgLbjxp5HSD"
	KeySecret := "******************************"
	client, _ := alidns.NewClientWithAccessKey("ap-southeast-1", KeyID, KeySecret)
	req := alidns.CreateDescribeDomainRecordsRequest()
	req.DomainName = "beezee.fun"
	resp, err := client.DescribeDomainRecords(req)
	if err != nil {
		t.Error(err)
	}
	fmt.Println(resp.String())
}

func TestListDomainRecords(t *testing.T) {
	KeyID := "LTAI5tKNPBNmfKgLbjxp5HSD"
	KeySecret := "******************************"
	var cfg = &GeneralConfig{
		RegionId:        "ap-southeast-1",
		AccessKeyId:     KeyID,
		AccessKeySecret: KeySecret,
	}
	resp, err := ListDnsRecords(cfg, "beezee.fun")
	if err != nil {
		t.Error(err)
	}
	fmt.Println(resp)
}

func TestDeleteDomainRecord(t *testing.T) {
	KeyID := "LTAI5tKNPBNmfKgLbjxp5HSD"
	KeySecret := "******************************"
	var cfg = &GeneralConfig{
		RegionId:        "ap-southeast-1",
		AccessKeyId:     KeyID,
		AccessKeySecret: KeySecret,
	}
	idList := []string{"893693876508125184",
		}

	for _, id := range idList {
		resp, err := DeleteDnsRecord(cfg, id)
		if err != nil {
			t.Error(err)
		}
		fmt.Println(resp)
	}
}

func TestDescribeVerifyContent(t *testing.T) {
	KeyID := "LTAI5tBmjnpgmU6EUyraUzNc"
	KeySecret := "******************************"
	var cfg = &GeneralConfig{
		RegionId:        "ap-southeast-1",
		AccessKeyId:     KeyID,
		AccessKeySecret: KeySecret,
	}
	resp, err := DescribeCdnVerifyContent(cfg, "beezee.fun")
	if err != nil {
		t.Error(err)
	}
	fmt.Println(resp)
}

func TestRefreshCdnCache(t *testing.T) {
	KeyID := "LTAI5tBmjnpgmU6EUyraUzNc"
	KeySecret := "******************************"
	var cfg = &GeneralConfig{
		RegionId:        "ap-southeast-1",
		AccessKeyId:     KeyID,
		AccessKeySecret: KeySecret,
	}
	resp, err := RefreshCdnCache(cfg, &RefreshCdnCacheOptions{
		Path: "https://test.beezee.fun/index3.html\nhttps://test.beezee.fun/index2.html",
		Type: "File",
	})
	if err != nil {
		t.Error(err)
	}
	fmt.Println(resp)
}

func TestVerifyDomainOwner(t *testing.T) {
	KeyID := "LTAI5tBmjnpgmU6EUyraUzNc"
	KeySecret := "******************************"
	var cfg = &GeneralConfig{
		RegionId:        "ap-southeast-1",
		AccessKeyId:     KeyID,
		AccessKeySecret: KeySecret,
	}
	resp, err := VerifyCdnDomainOwner(cfg, "beezee.fun")
	if err != nil {
		t.Error(err)
	}
	fmt.Println(resp)
}

func TestDescribeCdnDomainDetail(t *testing.T) {
	KeyID := "LTAI5tBmjnpgmU6EUyraUzNc"
	KeySecret := "******************************"
	var cfg = &GeneralConfig{
		RegionId:        "ap-southeast-1",
		AccessKeyId:     KeyID,
		AccessKeySecret: KeySecret,
	}
	resp, err := DescribeCdnDomainDetail(cfg, "beezee.fun2")
	if err != nil {
		t.Error(err)
	}
	fmt.Println(resp)
}

func TestListCdnDomains(t *testing.T) {
	KeyID := "LTAI5tBmjnpgmU6EUyraUzNc"
	KeySecret := "******************************"
	var cfg = &GeneralConfig{
		RegionId:        "ap-southeast-1",
		AccessKeyId:     KeyID,
		AccessKeySecret: KeySecret,
	}
	resp, err := ListCdnDomains(cfg)
	if err != nil {
		t.Error(err)
	}
	for _, v := range resp.Domains.PageData {
		fmt.Println(v.DomainName, v.Cname, v.Sources, v.DomainStatus)
	}
}

func TestGetCdnCerts(t *testing.T) {
	KeyID := "LTAI5tBmjnpgmU6EUyraUzNc"
	KeySecret := "******************************"
	var cfg = &GeneralConfig{
		RegionId:        "ap-southeast-1",
		AccessKeyId:     KeyID,
		AccessKeySecret: KeySecret,
	}
	resp, err := ListCdnCerts(cfg, "test.beezee.fun")
	if err != nil {
		fmt.Println(resp.GetOriginHttpResponse().StatusCode)
		t.Fatal(err)
	}
	fmt.Println(resp)
}

func TestGetCertDetail(t *testing.T) {
	KeyID := "LTAI5tBmjnpgmU6EUyraUzNc"
	KeySecret := "******************************"
	var cfg = &GeneralConfig{
		RegionId:        "ap-southeast-1",
		AccessKeyId:     KeyID,
		AccessKeySecret: KeySecret,
	}
	resp, err := GetCertDetail(cfg, "0")
	if err != nil {
		t.Fatal(err)
	}
	fmt.Println(*resp.Body.Cert)
	fmt.Println(*resp.Body.Key)
}

func TestListCerts(t *testing.T) {
	KeyID := "LTAI5tBmjnpgmU6EUyraUzNc"
	KeySecret := "******************************"
	var cfg = &GeneralConfig{
		RegionId:        "ap-southeast-1",
		AccessKeyId:     KeyID,
		AccessKeySecret: KeySecret,
	}
	resp, err := ListUserCerts(cfg)
	if err != nil {
		t.Fatal(err)
	}
	fmt.Println(resp)
}

func TestDeleteUserCert(t *testing.T) {
	KeyID := "LTAI5tBmjnpgmU6EUyraUzNc"
	KeySecret := "******************************"
	var cfg = &GeneralConfig{
		RegionId:        "ap-southeast-1",
		AccessKeyId:     KeyID,
		AccessKeySecret: KeySecret,
	}
	resp, err := DeleteUserCert(cfg, "12786905")
	if err != nil {
		t.Fatal(err)
	}
	fmt.Println(resp)
}

func TestGetCdnRefreshQuota(t *testing.T) {
	var cfg = &GeneralConfig{
		RegionId:        "ap-southeast-1",
		AccessKeyId:     getAliyunCdnKey().id,
		AccessKeySecret: getAliyunCdnKey().secret,
	}
	resp, err := GetCdnRefreshQuota(cfg)
	if err != nil {
		t.Fatal(err)
	}
	fmt.Println(resp)
}

func TestListCdnRefreshTasks(t *testing.T) {
	var cfg = &GeneralConfig{
		RegionId:        "ap-southeast-1",
		AccessKeyId:     getAliyunCdnKey().id,
		AccessKeySecret: getAliyunCdnKey().secret,
	}
	resp, err := ListCdnRefreshTasks(cfg, 1, 1)
	if err != nil {
		t.Fatal(err)
	}
	fmt.Println(resp)
}

func TestGetCdnConfigs(t *testing.T) {
	var cfg = &GeneralConfig{
		RegionId:        "ap-southeast-1",
		AccessKeyId:     getAliyunCdnKey().id,
		AccessKeySecret: getAliyunCdnKey().secret,
	}
	resp, err := GetCdnConfigs(cfg, &GetCdnConfigsOptions{
		DomainName:    "test-args-ignore.beezee.fun",
		FunctionNames: "set_hashkey_args",
		ConfigId:      "",
	})
	if err != nil {
		t.Fatal(err)
	}
	fmt.Println(resp)
}

const aliCloudCdnConfigFunctions = `[
	{
	  "functionArgs": [
		{
		  "argName": "disable",
		  "argValue": "on"
		},
		{
		  "argName": "keep_oss_args",
		  "argValue": "on"
		}
	  ],
	  "functionName": "set_hashkey_args"
	},
	{
	  "functionArgs": [
		{
		  "argName": "enable",
		  "argValue": "on"
		},
		{
		  "argName": "trim_css",
		  "argValue": "on"
		},
		{
		  "argName": "trim_js",
		  "argValue": "on"
		}
	  ],
	  "functionName": "tesla"
	},
	{
	  "functionArgs": [
		{
		  "argName": "enable",
		  "argValue": "on"
		}
	  ],
	  "functionName": "gzip"
	}
]`

func TestSetCdnConfigs(t *testing.T) {
	var cfg = &GeneralConfig{
		RegionId:        "ap-southeast-1",
		AccessKeyId:     getAliyunCdnKey().id,
		AccessKeySecret: getAliyunCdnKey().secret,
	}
	resp, err := SetCdnConfigs(cfg, &SetCdnConfigsOptions{
		DomainName: "test.beezee.fun",
		Functions:  aliCloudCdnConfigFunctions,
	})
	if err != nil {
		t.Fatal(err)
	}
	fmt.Println(resp)
}