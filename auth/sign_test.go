package auth

import (
	"context"
	"github.com/go-playground/assert/v2"
	"testing"
)

func Test_SignContent(t *testing.T) {
	type Req struct {
		state     int
		Email     string `json:"email,omitempty"`
		Password  string `json:"-,omitempty"`
		Sign      string `json:"sign,omitempty"`
		Timestamp int64  `json:"timestamp,omitempty"`
	}
	{
		req := &Req{
			state:     1,
			Email:     "<EMAIL>",
			Password:  "123456",
			Sign:      "email=<EMAIL>&timestamp=123456&",
			Timestamp: 123456,
		}
		signParam, sign, err := Sign(context.Background(), req, "")
		assert.Equal(t, err, nil)
		assert.Equal(t, signParam, "email=<EMAIL>&timestamp=123456&")
		assert.Equal(t, sign, "40c40ce3f851e78c2a419fe82707b02dd5c2eab300bd6f4e6b0ce4e539772942")
	}
}

func Test_CheckSign(t *testing.T) {
	type Req struct {
		state     int
		Email     string `json:"email,omitempty"`
		Password  string `json:"-,omitempty"`
		Sign      string `json:"sign,omitempty"`
		Timestamp int64  `json:"timestamp,omitempty"`
	}

	{
		req := &Req{
			state:     1,
			Email:     "<EMAIL>",
			Password:  "123456",
			Sign:      "40c40ce3f851e78c2a419fe82707b02dd5c2eab300bd6f4e6b0ce4e539772942",
			Timestamp: 123456,
		}
		err := CheckSign(context.Background(), req, "")
		assert.Equal(t, err, nil)
	}

	{
		req := &Req{
			state:     1,
			Email:     "<EMAIL>",
			Password:  "123456",
			Sign:      "email=<EMAIL>&timestamp=123456&1",
			Timestamp: 123456,
		}
		err := CheckSign(context.Background(), req, "")
		assert.Equal(t, err, ErrorBadSign)
	}

	{
		err := CheckSign(context.Background(), "bad req", "")
		assert.Equal(t, err, ErrorBadReq)
	}
}
