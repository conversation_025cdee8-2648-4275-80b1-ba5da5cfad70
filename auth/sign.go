package auth

import (
	"context"
	"crypto/sha256"
	"encoding/hex"
	"errors"
	"fmt"
	"reflect"
	"sort"
	"strings"
)

/*
  签名/验签
  注意 **sign** 字段为保留参数，业务参数请勿使用该名称
*/

var (
	ErrorBadSigner = errors.New("bad signer")
	ErrorBadSign   = errors.New("bad sign")
	ErrorBadReq    = errors.New("bad request")
)

const (
	SignKey = "sign" // 签名参数名称，业务参数请勿使用此名称
	Ignored = "-"
)

//type Signer func(ctx context.Context, secret, source string) (string, error)

// Sign 根据请求生成签名内容
func Sign(ctx context.Context, req interface{}, secret string) (signParam, sign string, err error) {
	//if signer == nil {
	//	err = ErrorBadSigner
	//	return
	//}

	params := make(map[string]any)
	names := []string{}
	v := reflect.ValueOf(req)
	if v.Kind() == reflect.Ptr {
		v = v.Elem()
	}

	if v.Kind() != reflect.Struct { // 非结构体返回错误提示
		err = ErrorBadReq
		return
	}

	t := v.Type()
	// 遍历结构体字段
	// 指定tagName值为map中key;字段值为map中value
	for i := 0; i < v.NumField(); i++ {
		fi := t.Field(i)
		if tagValue := fi.Tag.Get("json"); tagValue != "" {
			tagItems := strings.Split(tagValue, ",")
			if len(tagItems) > 0 {
				switch tagItems[0] {
				case SignKey: // 签名字段
					signParam = v.Field(i).String()
				case Ignored: // 跳过不包含的字段
					// skip
				default: // 其它请求参数
					params[tagItems[0]] = v.Field(i).Interface()
					names = append(names, tagItems[0])
				}

			}
		}
	}

	// 对参数名称进行字典排序
	if len(names) > 0 {
		sort.Strings(names)
	}
	// 拼接签名字符串
	var signContent string
	for _, name := range names {
		if value, ok := params[name]; ok {
			signContent += fmt.Sprintf("%s=%v&", name, value)
		}
	}

	sign, err = signer(ctx, secret, signContent)
	if err != nil {
		return
	}
	return
}

func signer(ctx context.Context, secret string, content string) (string, error) {
	hash := sha256.New()
	str := fmt.Sprintf("%s%s", secret, content)
	// 将字符串转换为字节数组并写入哈希对象
	hash.Write([]byte(str))

	// 计算哈希值
	hashValue := hash.Sum(nil)

	// 将哈希值转换为十六进制字符串
	hashString := hex.EncodeToString(hashValue)
	return hashString, nil
}

// CheckSign 检查签名
func CheckSign(ctx context.Context, req interface{}, secret string) (err error) {
	var (
		signParam, sign string
	)
	signParam, sign, err = Sign(ctx, req, secret)
	if err != nil {
		return
	}

	// 校验签名
	if sign != signParam {
		err = ErrorBadSign
		return
	}

	return
}
