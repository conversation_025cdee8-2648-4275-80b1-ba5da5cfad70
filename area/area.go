package area

import (
	"encoding/json"
	"github.com/google/wire"
	"io"
	"os"
)

var ProviderSet = wire.NewSet(NewArea)

type area struct {
	Code      string `json:"code"`
	Name      string `json:"name"`
	ChildList []area `json:"childList"`
}

type Area struct {
	codeName map[string]string
}

func NewArea() *Area {
	var areas []area

	file, err := os.Open("./extern/miniutils/area/area.json")
	if err != nil {
		panic(err)
	}
	defer file.Close()

	bytes, err := io.ReadAll(file)
	if err != nil {
		panic(err)
	}

	err = json.Unmarshal(bytes, &areas)
	if err != nil {
		panic(err)
	}
	areaMap := make(map[string]string)
	buildAreaMap(areas, areaMap)
	var area Area
	area.codeName = areaMap
	return &area
}

func buildAreaMap(areas []area, areaMap map[string]string) {
	for _, area := range areas {
		areaMap[area.Code] = area.Name
		if area.ChildList != nil {
			buildAreaMap(area.ChildList, areaMap)
		}
	}
}

func (c *Area) GetNameByCode(code string) string {
	if name, exists := c.codeName[code]; exists {
		return name
	}
	return ""
}
