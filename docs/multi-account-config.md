# AdSense Bot 多账号配置指南

## 概述

AdSense Bot 现在支持多个 AdSense 账号的数据采集，您可以通过配置文件灵活管理多个账号，并为不同的任务指定不同的执行策略。

## Google 账号配置

### 配置结构

```yaml
google:
configs:
- account_id: "unique_account_id"      # 账号唯一标识
account_name: "账号显示名称"          # 便于管理的名称
clientID: "your_client_id"          # Google OAuth2 客户端ID
clientSecret: "your_client_secret"   # Google OAuth2 客户端密钥
code: "your_refresh_token"          # 刷新令牌
pubID: "pub-****************"       # AdSense 发布商ID
use_sa: false                       # 是否使用服务账号
enabled: true                       # 是否启用该账号
description: "账号描述信息"          # 可选的描述
tags: ["tag1", "tag2"]             # 账号标签，用于分组
```

### 字段说明

- **account_id**: 账号的唯一标识符，建议使用有意义的名称
- **account_name**: 账号的显示名称，用于日志和管理界面
- **pubID**: AdSense 发布商ID，这是最重要的标识符
- **enabled**: 控制账号是否启用，禁用的账号不会被创建服务实例
- **tags**: 标签系统，可用于账号分组和批量操作

## 任务配置

### 执行模式

任务支持三种执行模式：

#### 1. 单账号模式 (SINGLE)
```yaml
- name: "siteData"
schedule: "0 2 * * *"
execution_mode: SINGLE
pub_id: "pub-****************"  # 指定单个账号
```

#### 2. 多账号模式 (MULTIPLE)
```yaml
- name: "channelData"
schedule: "0 3 * * *"
execution_mode: MULTIPLE
pub_ids:
- "pub-****************"
- "pub-****************"
```

#### 3. 全账号模式 (ALL)
```yaml
- name: "pageUrlData"
schedule: "0 4 * * *"
execution_mode: ALL  # 对所有启用的账号执行
```

### 任务配置字段

- **execution_mode**: 执行模式（SINGLE/MULTIPLE/ALL）
- **pub_id**: 单账号模式下指定的账号ID
- **pub_ids**: 多账号模式下指定的账号ID列表
- **all_accounts**: 全账号模式标志（已废弃，使用execution_mode: ALL）

## 配置示例

### 基本多账号配置

```yaml
google:
configs:
# 主账号
- account_id: "main"
account_name: "主要收入账号"
pubID: "pub-****************"
enabled: true
tags: ["main", "high-priority"]

# 备用账号
- account_id: "backup"
account_name: "备用账号"
pubID: "pub-****************"
enabled: true
tags: ["backup", "secondary"]

# 测试账号（禁用）
- account_id: "test"
account_name: "测试账号"
pubID: "pub-****************"
enabled: false
tags: ["test"]
```

### 任务调度配置

```yaml
job:
jobs:
# 主账号每日数据采集
- name: "mainAccountDaily"
schedule: "0 2 * * *"
execution_mode: SINGLE
pub_id: "pub-****************"

# 所有账号周报
- name: "weeklyReport"
schedule: "0 9 * * 1"
execution_mode: ALL

# 特定账号组合
- name: "productionAccounts"
schedule: "0 6 * * *"
execution_mode: MULTIPLE
pub_ids:
- "pub-****************"
- "pub-****************"
```

## 最佳实践

### 1. 账号命名规范
- 使用有意义的 account_id，如 "main", "backup", "region_us" 等
- account_name 使用中文或英文描述，便于识别

### 2. 标签使用
- 使用标签对账号进行分类：["production", "test"]
- 按地区分类：["us", "eu", "asia"]
- 按优先级分类：["high", "medium", "low"]

### 3. 任务调度
- 错开不同账号的采集时间，避免API限制
- 重要账号使用单独的任务配置
- 批量操作使用多账号或全账号模式

### 4. 错误处理
- 单个账号失败不会影响其他账号
- 禁用有问题的账号而不是删除配置
- 查看日志了解账号状态

## 迁移指南

### 从单账号配置迁移

如果您之前使用单账号配置：

```yaml
# 旧配置
google:
configs:
- clientID: "xxx"
pubID: "pub-****************"
```

迁移到新配置：

```yaml
# 新配置
google:
configs:
- account_id: "main_account"
account_name: "主账号"
clientID: "xxx"
pubID: "pub-****************"
enabled: true
tags: ["main"]
```

### 任务配置迁移

旧的任务配置会自动使用第一个启用的账号，但建议明确指定：

```yaml
# 推荐的新配置
job:
jobs:
- name: "siteData"
execution_mode: SINGLE
pub_id: "pub-****************"
```

## 故障排除

### 常见问题

1. **账号未启用**: 检查 `enabled: true` 设置
2. **PubID 错误**: 确认 pubID 格式正确
3. **认证失败**: 检查 clientID、clientSecret 和 refresh token
4. **任务执行失败**: 查看日志中的具体错误信息

### 日志查看

系统会记录详细的账号创建和任务执行日志：

```
成功创建GoogleService，账号: 主账号, PubID: pub-****************
跳过已禁用的账号：测试账号 (PubID: pub-****************)
GoogleServiceManager初始化完成，共创建 2 个AdSense服务
```

## API 使用

### 获取账号列表

```go
// 获取所有启用的账号
pubIDs := adsenseRepo.GetAllPubIDs()

// 根据任务配置获取账号列表
pubIDs, err := adsenseRepo.GetPubIDsForJob(jobConfig)
```

### 账号信息查询

```go
// 获取账号配置信息
config, err := googleServiceManager.GetAccountInfo(pubID)

// 按标签查询账号
pubIDs := googleServiceManager.GetAccountsByTags([]string{"production"})
```
