# Service层多账户配置实现

## 概述

本次修改实现了基于配置的多账户任务执行功能，支持三种执行模式：SINGLE、MULTIPLE、ALL。如果没有设置execution_mode，默认为ALL模式（给全部账号获取数据）。

## 修改内容

### 1. internal/service/job.go

#### 简化的类型定义
- `JobFunc`: 统一的任务函数类型，接收job配置参数：`func(ctx context.Context, jobConfig *conf.Job_JobConfig) error`
- `Jobs`: 全局任务映射，替代原来的DefaultJobs

#### 新增方法
- `createMultiAccountJobWrapper()`: 创建多账号任务包装器，根据配置决定执行哪些账号
- `createSimpleJobWrapper()`: 创建简单任务包装器，用于不需要多账号功能的任务

#### 修改方法
- `Init()`: 初始化统一的任务映射
- `StartJob()`: 适配新的函数签名，使用默认配置

#### 主要特性
- 统一的任务函数签名，所有任务都接收job配置
- 根据job配置的execution_mode决定执行哪些账号
- 支持单账号、多账号、全账号三种模式
- 详细的日志记录，包括执行模式和处理的账号数量
- 代码更简洁，没有重复的映射和包装器

### 2. internal/data/adsense.go

#### 修改方法
- `GetPubIDsForJob()`: 添加默认执行模式处理

#### 主要特性
- 如果execution_mode未设置（UNSPECIFIED），默认使用ALL模式
- 添加详细的日志记录，说明使用的执行模式
- 对ALL模式显示处理的账号数量

### 3. internal/conf/conf.proto

#### 重要修复：枚举值调整
- 添加了 `UNSPECIFIED = 0` 作为枚举零值
- 调整了其他枚举值：`SINGLE = 1`, `MULTIPLE = 2`, `ALL = 3`
- 解决了protobuf枚举零值导致的默认模式判断错误问题

### 4. internal/server/worker.go

#### 简化任务调度逻辑
- 使用统一的Jobs映射查找任务函数
- 传递完整的job配置给任务执行函数
- 移除了复杂的回退逻辑

#### 主要特性
- 简洁的调度逻辑：只需要查找一个映射
- 统一的配置传递：所有任务都接收完整的job配置
- 错误处理：保持原有的错误处理和日志记录机制

## 执行模式详解

### UNSPECIFIED模式（枚举零值）
- protobuf枚举的默认零值
- 当配置中未设置`execution_mode`时的值
- 系统会自动转换为ALL模式并记录日志

### SINGLE模式
- 使用`pub_id`字段指定单个账号
- 只对指定的账号执行任务
- 如果账号不存在或未启用，任务会失败

### MULTIPLE模式
- 使用`pub_ids`字段指定多个账号
- 对列表中的所有有效账号执行任务
- 跳过不存在或未启用的账号，不会导致任务失败

### ALL模式
- 对所有启用的账号执行任务
- 忽略`pub_id`和`pub_ids`字段
- 这是未设置`execution_mode`时的默认行为

## 重要修复说明

### 枚举零值问题
在protobuf中，枚举的零值（0）表示未设置状态。之前的设计中`SINGLE = 0`会导致：
- 未设置`execution_mode`时被误判为单账号模式
- 违背了"默认为全账号模式"的设计意图

**修复方案**：
- 添加`UNSPECIFIED = 0`作为明确的未设置状态
- 重新分配枚举值：`SINGLE = 1`, `MULTIPLE = 2`, `ALL = 3`
- 在代码中检测到`UNSPECIFIED`时自动转换为`ALL`模式

## 配置示例

```yaml
job:
jobs:
# 单账号模式
- name: "siteData"
schedule: "0 2 * * *"
execution_mode: SINGLE
pub_id: "pub-1111111111111111"

# 多账号模式
- name: "channelData"
schedule: "0 3 * * *"
execution_mode: MULTIPLE
pub_ids:
- "pub-1111111111111111"
- "pub-2222222222222222"

# 全账号模式
- name: "pageUrlData"
schedule: "0 4 * * *"
execution_mode: ALL

# 默认模式（未设置execution_mode，默认为ALL）
- name: "siteCountryData"
schedule: "0 5 * * *"
# 将使用ALL模式
```

## 设计优势

- **统一的函数签名**：所有任务函数都使用相同的签名，便于维护
- **简洁的代码结构**：只有一个任务映射，没有重复代码
- **灵活的配置支持**：支持三种执行模式，满足不同场景需求
- **默认行为合理**：未设置execution_mode时默认使用ALL模式

## 日志优化

- **统一的任务完成日志**：所有任务都在包装器层输出统一格式的完成日志，避免冗余
- **多账号任务详细信息**：显示执行模式、成功账号数/总账号数
- **简单任务简洁信息**：显示任务名称和完成状态
- **清晰的错误处理**：部分账号失败时显示具体的失败信息
- **默认模式提示**：对未设置execution_mode的任务进行明确提示

### 日志示例

**多账号任务**：
```
INFO 任务 siteData 完成，执行模式: SINGLE，成功: 1/1 个账号
INFO 任务 channelData 完成，执行模式: MULTIPLE，成功: 2/2 个账号
INFO 任务 pageUrlData 完成，执行模式: ALL，成功: 3/3 个账号
WARN 任务 siteCountryData 部分账号执行失败: [账号 pub-xxx: 连接超时]
INFO 任务 siteCountryData 完成，执行模式: ALL，成功: 2/3 个账号
```

**简单任务**：
```
INFO 任务 generateGamesTable 完成
INFO 任务 autoCreateSchema 完成
INFO 任务 appCountrySummary 完成
```

## 测试建议

1. 测试单账号模式：配置一个有效的pub_id
2. 测试多账号模式：配置多个pub_ids，包括有效和无效的
3. 测试全账号模式：不设置pub_id和pub_ids
4. 测试默认行为：不设置execution_mode
5. 验证向后兼容性：使用原有配置格式
