package biz

import (
	"context"
	"crypto/md5"
	"encoding/base64"
	"encoding/hex"

	"github.com/go-kratos/kratos/v2/log"
)

type Bot struct {
}
type Message struct {
	MsgType  string    `json:"msgtype"` // text/markdown/image/news
	Text     *Text     `json:"text,omitempty"`
	Markdown *Markdown `json:"markdown,omitempty"`
	Image    *Image    `json:"image,omitempty"`
	News     *News     `json:"news,omitempty"`
}

type Text struct {
	Content             string   `json:"content"`
	MentionedList       []string `json:"mentioned_list,omitempty"`
	MentionedMobileList []string `json:"mentioned_mobile_list,omitempty"`
}

type Markdown struct {
	Content string `json:"content"`
}

// Image JPG/PNG < 2MiB (before base64 encoding)
type Image struct {
	Base64 string `json:"base64"`
	MD5    string `json:"md5"` // before base64 encoding
}

type News struct {
	Articles []Article `json:"articles"` // 1~8 articles
}

type Article struct {
	Title       string `json:"title"`                 // truncated to 128 bytes
	Description string `json:"description,omitempty"` // truncated to 512 bytes
	URL         string `json:"url"`
	PicURL      string `json:"picurl,omitempty"` // JPG/PNG, large:1068×455px, small:150×150px
}

func NewTextMessage(text string, MentionedList []string, MentionedMobileList []string) *Message {
	return &Message{
		MsgType: "text",
		Text: &Text{
			Content:             text,
			MentionedList:       MentionedList,
			MentionedMobileList: MentionedMobileList,
		},
	}
}

func NewMarkdownMessage(markdown string) *Message {
	return &Message{
		MsgType: "markdown",
		Markdown: &Markdown{
			Content: markdown,
		},
	}
}

func NewImageMessage(image []byte) *Message {
	dgst := md5.Sum(image)
	return &Message{
		MsgType: "image",
		Image: &Image{
			Base64: base64.StdEncoding.EncodeToString(image),
			MD5:    hex.EncodeToString(dgst[:]),
		},
	}
}

type BotRepo interface {
	SendText(ctx context.Context, msg string) error
	SendMarkdown(ctx context.Context, msg string) error

	SendImage(ctx context.Context, image []byte) error
}

// BotUsecase is a bot usecase.
type BotUsecase struct {
	repo BotRepo
	log  *log.Helper
}

func (u *BotUsecase) SendMd(ctx context.Context, content string) error {
	return u.repo.SendMarkdown(ctx, content)
}

func (u *BotUsecase) SendImage(ctx context.Context, content []byte) error {
	return u.repo.SendImage(ctx, content)
}

// NewBotUsecase new a Greeter usecase.
func NewBotUsecase(repo BotRepo, logger log.Logger) *BotUsecase {
	return &BotUsecase{repo: repo, log: log.NewHelper(logger)}
}
