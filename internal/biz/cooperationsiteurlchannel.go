package biz

import (
	"context"
)

type ICooperationSiteUrlChannel interface {
	ID() int
	Value() CooperationSiteUrlChannelValue
}

type CooperationSiteUrlChannel struct {
	id    int
	value CooperationSiteUrlChannelValue
}

func (g *CooperationSiteUrlChannel) Value() CooperationSiteUrlChannelValue {
	return g.value
}

func (g *CooperationSiteUrlChannel) ID() int {
	return g.id
}

type CooperationSiteUrlChannelValue struct {
	Name               string   `json:"name"`
	State              int32    `json:"state"`
	Site               string   `json:"site"`
	UrlChannel         string   `json:"url_channel"`
	CooperationChannel string   `json:"cooperation_channel"`
	Manager            []string `json:"manager"`
	GameCount          int32    `json:"game_count"`
	CreatedAt          int64    `json:"created_at"`
}

func NewCooperationSiteUrlChannel(id int, value CooperationSiteUrlChannelValue) ICooperationSiteUrlChannel {
	return &CooperationSiteUrlChannel{
		id:    id,
		value: value,
	}
}

type CooperationSiteUrlChannelRepo interface {
	ListCooperationSite(ctx context.Context) ([]ICooperationSiteUrlChannel, error)
	CreateOrUpdate(ctx context.Context, datas []CooperationSiteUrlChannelValue) error
	List(ctx context.Context) ([]ICooperationSiteUrlChannel, error)
}
