package biz

import (
	"context"
	"time"
)

type AnalyticsSiteData struct {
	Date                               time.Time `json:"date,omitempty"`
	PropertiesID                       string    `json:"properties_id,omitempty"`
	Site                               string    `json:"site,omitempty"`
	PageViews                          int       `json:"page_views,omitempty"`
	ActiveUsers                        int       `json:"active_users,omitempty"`
	TotalUsers                         int       `json:"total_users,omitempty"`
	NewUsers                           int       `json:"new_users,omitempty"`
	AverageSessionDuration             float64   `json:"average_session_duration,omitempty"`
	ScreenPageViewsPerUser             float64   `json:"screen_page_views_per_user,omitempty"`
	AverageEngagementTimePerActiveUser float64   `json:"average_engagement_time_per_active_user,omitempty"`
	// 游戏页相关数据
	GamePageViews              int     `json:"game_page_views,omitempty"`
	GameTotalUsers             int     `json:"game_total_users,omitempty"`
	GameAverageSessionDuration float64 `json:"game_average_session_duration,omitempty"`
	// SDK启动事件相关数据
	SdkStartEventCount int                `json:"sdk_start_event_count,omitempty"`
	SdkStartTotalUsers int                `json:"sdk_start_total_users,omitempty"`
	Browsers           []*ActiveUsersData `json:"browsers,omitempty"`
	VisitPageCount     int                `json:"visit_page_count,omitempty"`
	Languages          []*ActiveUsersData `json:"languages,omitempty"`
	Ua                 []*ActiveUsersData `json:"ua,omitempty"`
	DeviceResolution   []*ActiveUsersData `json:"device_resolution,omitempty"`
	Dpi                []*ActiveUsersData `json:"dpi,omitempty"`
	AudioFp            []*ActiveUsersData `json:"audio_fp,omitempty"`
	CanvasFp           []*ActiveUsersData `json:"canvas_fp,omitempty"`
	FontFp             []*ActiveUsersData `json:"font_fp,omitempty"`
	Tz                 []*ActiveUsersData `json:"tz,omitempty"`
	VisRes             []*ActiveUsersData `json:"vis_res,omitempty"`
	GlMod              []*ActiveUsersData `json:"gl_mod,omitempty"`
	MiniLang           []*ActiveUsersData `json:"mini_lang,omitempty"`
}

type AnalyticsSiteDataResponse struct {
	Date                               time.Time `json:"date,omitempty"`
	PropertiesID                       string    `json:"properties_id,omitempty"`
	Site                               string    `json:"site,omitempty"`
	PageViews                          int       `json:"page_views,omitempty"`
	ActiveUsers                        int       `json:"active_users,omitempty"`
	TotalUsers                         int       `json:"total_users,omitempty"`
	NewUsers                           int       `json:"new_users,omitempty"`
	AverageSessionDuration             float64   `json:"average_session_duration,omitempty"`
	ScreenPageViewsPerUser             float64   `json:"screen_page_views_per_user,omitempty"`
	AverageEngagementTimePerActiveUser float64   `json:"average_engagement_time_per_active_user,omitempty"`
	// 游戏页相关数据
	GamePageViews              int     `json:"game_page_views,omitempty"`
	GameTotalUsers             int     `json:"game_total_users,omitempty"`
	GameAverageSessionDuration float64 `json:"game_average_session_duration,omitempty"`
	// SDK启动事件相关数据
	SdkStartEventCount    int                `json:"sdk_start_event_count,omitempty"`
	SdkStartTotalUsers    int                `json:"sdk_start_total_users,omitempty"`
	Browsers              []*ActiveUsersData `json:"browsers,omitempty"`
	BrowserMap            map[string]int     `json:"browser_map,omitempty"`
	SumBrowserActiveUsers int                `json:"sum_browser_active_users,omitempty"`
}

type ActiveUsersData struct {
	Name        string `json:"name,omitempty"`
	ActiveUsers int    `json:"active_users,omitempty"`
}

type AnalyticsSiteRepo interface {
	Query(ctx context.Context, propertiesID, start, end string, queryBaseData bool) ([]*AnalyticsSiteData, error)
	Save(ctx context.Context, data []*AnalyticsSiteData) error
	Delete(ctx context.Context, start, end time.Time) error
	GetSiteGaData(ctx context.Context, start time.Time, end time.Time) ([]*AnalyticsSiteDataResponse, error)
	SetSiteVisitPageCount(ctx context.Context, propertiesID string, start string, end string) error
	GetVisitPageCountGaData(ctx context.Context, start time.Time, end time.Time) (map[string]int, error)
}
