package biz

import (
	"context"
	"fmt"
	"time"

	"github.com/go-kratos/kratos/v2/log"
)

// GamePurchaseData 游戏内购数据结构
type GamePurchaseData struct {
	UserID             string    `json:"user_id"`
	ProductOrderID     int32     `json:"product_order_id"`
	AppID              string    `json:"app_id"`
	Payload            string    `json:"payload"`
	Currency           string    `json:"currency"`
	Amount             int32     `json:"amount"`
	Bot                string    `json:"bot"`
	PayURL             string    `json:"pay_url"`
	Status             string    `json:"status"`
	PayChannelTid      string    `json:"pay_channel_tid"`
	PayMethodTid       string    `json:"pay_method_tid"`
	Platform           string    `json:"platform"`
	Domain             string    `json:"domain"`
	PayChannel         string    `json:"pay_channel"`
	PayMethod          string    `json:"pay_method"`
	PayCountry         string    `json:"pay_country"`
	PayoutAmount       int32     `json:"payout_amount"`
	CheckoutAmount     int32     `json:"checkout_amount"`
	ProductAmount      int32     `json:"product_amount"`
	ProductTitle       string    `json:"product_title"`
	ProductDescription string    `json:"product_description"`
	ProductID          string    `json:"product_id"`
	CreateTime         time.Time `json:"create_time"`
	UpdateTime         time.Time `json:"update_time"`
}

// GamePurchaseSummaryData 游戏内购每日汇总数据结构
type GamePurchaseSummaryData struct {
	Date         time.Time `json:"date"`          // 日期(YYYY-MM-DD格式)
	Domain       string    `json:"domain"`        // 业务域名
	GameAppid    string    `json:"game_appid"`    // 游戏应用ID
	PayAmount    int       `json:"pay_amount"`    // 支付金额
	PayTimes     int       `json:"pay_times"`     // 支付次数
	PayUsers     int       `json:"pay_users"`     // 支付用户数
	PayoutAmount int       `json:"payout_amount"` // 支出金额
	CreateTime   time.Time `json:"create_time"`   // 创建时间
	UpdateTime   time.Time `json:"update_time"`   // 更新时间
}

// GamePurchaseRepo 游戏内购数据仓库接口
type GamePurchaseRepo interface {
	// Create 批量创建游戏内购数据
	Create(ctx context.Context, purchases []*GamePurchaseData) error
	// FetchFromExternalAPI 从外部API获取内购数据
	FetchFromExternalAPI(ctx context.Context, startTime, endTime int64) ([]*GamePurchaseData, error)
	// FetchAllSummariesFromExternalAPI 从外部API获取所有内购汇总数据（包含分页逻辑）
	FetchAllSummariesFromExternalAPI(ctx context.Context) ([]*GamePurchaseSummaryData, error)
}

// GamePurchaseSummaryRepo 游戏内购汇总数据仓库接口
type GamePurchaseSummaryRepo interface {
	// Create 批量创建游戏内购汇总数据
	Create(ctx context.Context, summaries []*GamePurchaseSummaryData) error
	// Delete 删除所有汇总数据
	Delete(ctx context.Context) error
}

// PurchaseUsecase 购买业务用例
type PurchaseUsecase struct {
	repo        GamePurchaseRepo
	summaryRepo GamePurchaseSummaryRepo
	log         *log.Helper
}

// NewPurchaseUsecase 创建购买业务用例
func NewPurchaseUsecase(repo GamePurchaseRepo, summaryRepo GamePurchaseSummaryRepo, logger log.Logger) *PurchaseUsecase {
	return &PurchaseUsecase{
		repo:        repo,
		summaryRepo: summaryRepo,
		log:         log.NewHelper(log.With(logger, "module", "biz/purchase")),
	}
}

// FetchAndCreatePurchases 从外部API获取内购数据并创建
func (uc *PurchaseUsecase) FetchAndCreatePurchases(ctx context.Context) error {
	var endTime, startTime int64
	endTime = 0
	startTime = 0 // 24小时前

	return uc.FetchAndCreatePurchasesWithTimeRange(ctx, startTime, endTime)
}

// FetchAndCreatePurchasesWithTimeRange 从外部API获取指定时间范围的内购数据并创建
func (uc *PurchaseUsecase) FetchAndCreatePurchasesWithTimeRange(ctx context.Context, startTime, endTime int64) error {
	// 从外部API获取数据
	purchases, err := uc.repo.FetchFromExternalAPI(ctx, startTime, endTime)
	if err != nil {
		uc.log.WithContext(ctx).Errorf("从外部API获取内购数据失败: %v", err)
		return fmt.Errorf("获取外部内购数据失败: %w", err)
	}

	if len(purchases) == 0 {
		uc.log.WithContext(ctx).Info("外部API返回的内购数据为空")
		return nil
	}

	if len(purchases) == 0 {
		uc.log.WithContext(ctx).Warn("所有外部内购数据验证失败")
		return fmt.Errorf("所有外部内购数据验证失败")
	}

	// 调用数据层批量创建
	err = uc.repo.Create(ctx, purchases)
	if err != nil {
		uc.log.WithContext(ctx).Errorf("批量创建外部内购数据失败: %v", err)
		return fmt.Errorf("创建外部内购数据失败: %w", err)
	}

	uc.log.WithContext(ctx).Infof("成功从外部API获取并创建%d条内购数据", len(purchases))
	return nil
}

// FetchAndCreatePurchaseSummaries 从外部API获取内购汇总数据并创建
func (uc *PurchaseUsecase) FetchAndCreatePurchaseSummaries(ctx context.Context) error {
	// 从外部API获取所有汇总数据（分页逻辑在data层处理）
	summaries, err := uc.repo.FetchAllSummariesFromExternalAPI(ctx)
	if err != nil {
		uc.log.WithContext(ctx).Errorf("从外部API获取内购汇总数据失败: %v", err)
		return fmt.Errorf("获取外部内购汇总数据失败: %w", err)
	}

	if len(summaries) == 0 {
		uc.log.WithContext(ctx).Info("外部API返回的内购汇总数据为空")
		return nil
	}

	// 先删除现有数据
	err = uc.summaryRepo.Delete(ctx)
	if err != nil {
		uc.log.WithContext(ctx).Errorf("删除现有内购汇总数据失败: %v", err)
		return fmt.Errorf("删除现有内购汇总数据失败: %w", err)
	}

	// 调用数据层批量创建
	err = uc.summaryRepo.Create(ctx, summaries)
	if err != nil {
		uc.log.WithContext(ctx).Errorf("批量创建外部内购汇总数据失败: %v", err)
		return fmt.Errorf("创建外部内购汇总数据失败: %w", err)
	}

	uc.log.WithContext(ctx).Infof("成功创建%d条外部内购汇总数据", len(summaries))
	return nil
}
