/**
* <AUTHOR>
* @Date 2024/2/6
**/

package biz

import (
	"context"

	"github.com/go-kratos/kratos/v2/log"

	"git.minigame.vip/minicloud/service/adsense-bot/internal/conf"
)

type ShardingSchemaRepo interface {
	//Create(ctx context.Context, year int) error

	//创建分区主表
	CreatePartitionTables(_ context.Context) (err error)
	//创建分区表
	CreateChildTables(_ context.Context, year int) (err error)

	//删除表
	//Remove(ctx context.Context, tableName string) (error error)
}

type ShardingSchemaUsecase struct {
	repo     ShardingSchemaRepo
	ccafRepo AdSenseChannelCountryAdFormatRepo
	conf     *conf.Data
	log      *log.Helper
}

// CreateTableSchema 定时创建分区表
func (uc *ShardingSchemaUsecase) CreateTableSchema(ctx context.Context, year int) error {
	err := uc.repo.CreatePartitionTables(ctx)
	if err != nil {
		uc.log.Errorf("CreatePartitionTables Error %v", err)
		return err
	}
	err = uc.repo.CreateChildTables(ctx, year)
	if err != nil {
		uc.log.Errorf("CreateChildTables Error %v", err)
		return err
	}
	return err
}

// AutoMigrate 迁移数据
func (uc *ShardingSchemaUsecase) AutoMigrate(ctx context.Context, year int) error {
	return uc.ccafRepo.AutoMigrate(ctx, year)
}

// RemoveOutOfDate 删除过期的表
func (uc *ShardingSchemaUsecase) RemoveOutOfDate(ctx context.Context) (err error) {
	//saveMonth := int(uc.conf.GetSharding().GetOutOfDateMonth())
	//if saveMonth <= 0 {
	//	return errors.New("error config.data.sharding.out_of_date_month is lte 0")
	//}
	//按月获取
	//partitionTable := partition.LastMonth(channelcountryadformatpartition.Table, saveMonth)
	//err = uc.repo.Remove(ctx, partitionTable)
	//if err != nil {
	//	return
	//}
	return
}

func NewShardingSchemaUsecase(repo ShardingSchemaRepo, ccafRepo AdSenseChannelCountryAdFormatRepo, conf *conf.Data, logger log.Logger) *ShardingSchemaUsecase {
	return &ShardingSchemaUsecase{
		repo:     repo,
		ccafRepo: ccafRepo,
		conf:     conf,
		log:      log.NewHelper(log.With(logger, "module", "usecase/sharding-schema")),
	}
}
