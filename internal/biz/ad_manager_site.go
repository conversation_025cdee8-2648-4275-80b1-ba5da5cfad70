package biz

import (
	"context"
	"time"
)

type AdManagerSite struct {
	// Date holds the value of the "date" field.
	Date time.Time `json:"date,omitempty"`
	// 子级广告资源网代码
	ChildNetworkCode string `json:"child_network_code,omitempty"`
	// Site holds the value of the "site" field.
	Site string `json:"site,omitempty"`
	// 子级广告资源网 ID
	ChildNetworkID string `json:"child_network_id,omitempty"`
	// Ad Exchange 展示次数
	AdExchangeImpressions int `json:"ad_exchange_impressions,omitempty"`
	// Ad Exchange 点击次数
	AdExchangeClicks int `json:"ad_exchange_clicks,omitempty"`
	// Ad Exchange 点击率
	AdExchangeCtr float64 `json:"ad_exchange_ctr,omitempty"`
	// Ad Exchange 收入
	AdExchangeRevenue float64 `json:"ad_exchange_revenue,omitempty"`
	// Ad Exchange 平均 eCPM
	AdExchangeAverageEcpm float64 `json:"ad_exchange_average_ecpm,omitempty"`
	// Ad Exchange 广告请求数
	AdExchangeAdRequests int `json:"ad_exchange_ad_requests,omitempty"`
	// Ad Exchange 匹配率
	AdExchangeMatchRate float64 `json:"ad_exchange_match_rate,omitempty"`
}

type AdManagerSiteRepo interface {
	Save(ctx context.Context, data []*AdManagerSite) error
	Query(ctx context.Context, start time.Time, end time.Time) ([]*AdManagerSite, error)
}
