package biz

import (
	"context"
)

type ICooperation interface {
	ID() int
	Value() CooperationValue
}

type Cooperation struct {
	id    int
	value CooperationValue
}

func (g *Cooperation) Value() CooperationValue {
	return g.value
}

func (g *Cooperation) ID() int {
	return g.id
}

type CooperationValue struct {
	Name             string   `json:"name"`
	State            int32    `json:"state"`
	SiteCount        int32    `json:"site_count"`
	UrlChannelCount  int32    `json:"url_channel_count"`
	OperationalState int32    `json:"operational_state"`
	Manager          []string `json:"manager"`
	GameCount        int32    `json:"game_count"`
	CreatedAt        int64    `json:"created_at"`
}

func NewCooperation(id int, value CooperationValue) ICooperation {
	return &Cooperation{
		id:    id,
		value: value,
	}
}

type CooperationRepo interface {
	CreateOrUpdate(ctx context.Context, datas []CooperationValue) error
	DeleteUnusedCooperation(ctx context.Context, cooperation []string) error
}
