package biz

import (
	"context"
	"time"
)

// 域名广告显示策略常量
const (
	MonetizationStrategyAllAds  = 0 // 显示所有广告
	MonetizationStrategyGameAds = 1 // 只显示游戏内广告
)

var (
	// InGameAdFormats 游戏内广告类型常量
	InGameAdFormats = []interface{}{"MANUAL_INTERSTITIAL", "MANUAL_REWARDED"}
)

// SiteDeploymentHistory 网站广告显示状态数据结构
type SiteDeploymentHistory struct {
	Domain               string     `json:"domain"`                // 域名
	MonetizationStrategy int        `json:"monetization_strategy"` // 广告显示策略：0 显示所有广告，1 只显示游戏内广告
	StartTime            *time.Time `json:"start_time"`            // 时间段开始时间（只显示游戏内广告的开始时间）
	EndTime              *time.Time `json:"end_time"`              // 时间段结束时间（只显示游戏内广告的结束时间，可能为空表示无限期）
}

// DeploymentHistoryRepo 网站广告显示状态仓储接口
type DeploymentHistoryRepo interface {
	GetSiteDeploymentHistory(ctx context.Context, domains []string) (map[string][]*SiteDeploymentHistory, error)
}
