package biz

import (
	"context"
	"time"
)

// SiteReductionResult 站点数据变化比较结果
type SiteReductionResult struct {
	Date                           time.Time `json:"date"`
	Site                           string    `json:"site"`
	LatestCollectedAt              time.Time `json:"latest_collected_at"`
	PreviousCollectedAt            time.Time `json:"previous_collected_at"`
	LatestImpressions              int       `json:"latest_impressions"`
	PreviousImpressions            int       `json:"previous_impressions"`
	ImpressionsChange              int       `json:"impressions_change"`
	LatestClicks                   int       `json:"latest_clicks"`
	PreviousClicks                 int       `json:"previous_clicks"`
	ClicksChange                   int       `json:"clicks_change"`
	LatestAdRequests               int       `json:"latest_ad_requests"`
	PreviousAdRequests             int       `json:"previous_ad_requests"`
	AdRequestsChange               int       `json:"ad_requests_change"`
	LatestEstimatedEarnings        float64   `json:"latest_estimated_earnings"`
	PreviousEstimatedEarnings      float64   `json:"previous_estimated_earnings"`
	EstimatedEarningsChange        float64   `json:"estimated_earnings_change"`
	ImpressionsChangePercent       *float64  `json:"impressions_change_percent"`
	ClicksChangePercent            *float64  `json:"clicks_change_percent"`
	AdRequestsChangePercent        *float64  `json:"ad_requests_change_percent"`
	EstimatedEarningsChangePercent *float64  `json:"estimated_earnings_change_percent"`
}

type AdReduction struct {
	Date             time.Time `json:"date"`
	Site             string    `json:"site"`
	ReductionDate    time.Time `json:"reduction_date"`
	DeltaDay         int       `json:"delta_day"`
	ReductionType    string    `json:"reduction_type"`
	ReductionRateR   float64   `json:"reduction_rate_r"`
	ReductionRateI   float64   `json:"reduction_rate_i"`
	ReductionRateC   float64   `json:"reduction_rate_c"`
	ReductionRateE   float64   `json:"reduction_rate_e"`
	ReductionChangeR int       `json:"reduction_change_r"`
	ReductionChangeI int       `json:"reduction_change_i"`
	ReductionChangeC int       `json:"reduction_change_c"`
	ReductionChangeE float64   `json:"reduction_change_e"`
}

type AdReductionRepo interface {
	Save(ctx context.Context, data []*AdReduction) error
}
