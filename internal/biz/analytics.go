package biz

import (
	"context"
	"errors"
	"time"

	"github.com/go-kratos/kratos/v2/log"

	"git.minigame.vip/minicloud/service/adsense-bot/internal/conf"
	"git.minigame.vip/minicloud/service/adsense-bot/pkg/date"
)

type AnalyticsData struct {
	Date                               time.Time `json:"date,omitempty"`
	PropertiesID                       string    `json:"properties_id,omitempty"`
	PageViews                          int       `json:"page_views,omitempty"`
	ActiveUsers                        int       `json:"active_users,omitempty"`
	TotalUsers                         int       `json:"total_users,omitempty"`
	NewUsers                           int       `json:"new_users,omitempty"`
	PlayCount                          int       `json:"play_count,omitempty"`
	GameImpression                     int       `json:"game_impression,omitempty"`
	Upu                                int       `json:"upu,omitempty"`
	PaymentTimes                       int       `json:"payment_times,omitempty"`
	AverageSessionDuration             float64   `json:"average_session_duration,omitempty"`
	ScreenPageViewsPerUser             float64   `json:"screen_page_views_per_user,omitempty"`
	AverageEngagementTimePerActiveUser float64   `json:"average_engagement_time_per_active_user,omitempty"`
}

type AnalyticsRepo interface {
	Query(ctx context.Context, propertiesID, start, end string) ([]*AnalyticsData, error)
	Save(ctx context.Context, data []*AnalyticsData) error
	ListPropertiesID(ctx context.Context, id []string) ([]string, error)
}

// AnalyticsUsecase is an analytics usecase.
type AnalyticsUsecase struct {
	analyticsRepo     AnalyticsRepo
	analyticsSiteRepo AnalyticsSiteRepo

	analyticsAccountId []string
	propertiesID       []string

	log *log.Helper
}

func (uc *AnalyticsUsecase) SaveAnalyticsBaseData(ctx context.Context, dateRange string) error {
	analyticsData := make([]*AnalyticsData, 0)
	start, end := date.GetDateRange(dateRange, time.Now())
	propertiesIDs, err := uc.analyticsRepo.ListPropertiesID(ctx, uc.analyticsAccountId)
	if err != nil {
		return err
	}
	if len(uc.propertiesID) > 0 {
		propertiesIDs = append(uc.propertiesID, propertiesIDs...)
	}
	for _, propertyID := range propertiesIDs {
		data, err := uc.analyticsRepo.Query(ctx, propertyID, start.Format(time.DateOnly), end.Format(time.DateOnly))
		if err != nil {
			return errors.New("failed to query analytics data, propertyID:" + propertyID + ", err: " + err.Error())
		}
		analyticsData = append(analyticsData, data...)
	}
	err = uc.analyticsRepo.Save(ctx, analyticsData)
	if err != nil {
		return err
	}
	return nil
}

func (uc *AnalyticsUsecase) SaveAnalyticsSiteData(ctx context.Context, dateRange string) error {
	analyticsData := make([]*AnalyticsSiteData, 0)
	start, end := date.GetDateRange(dateRange, time.Now())
	err := uc.analyticsSiteRepo.Delete(ctx, start, end)
	if err != nil {
		return err
	}
	propertiesIDs, err := uc.analyticsRepo.ListPropertiesID(ctx, uc.analyticsAccountId)
	if err != nil {
		return err
	}
	if len(uc.propertiesID) > 0 {
		propertiesIDs = append(uc.propertiesID, propertiesIDs...)
	}
	queryBaseData := true
	for _, propertyID := range propertiesIDs {
		if dateRange != "TODAY" {
			err := uc.analyticsSiteRepo.SetSiteVisitPageCount(ctx, propertyID, start.Format(time.DateOnly), end.Format(time.DateOnly))
			if err != nil {
				return err
			}
			queryBaseData = false
		}
		data, err := uc.analyticsSiteRepo.Query(ctx, propertyID, start.Format(time.DateOnly), end.Format(time.DateOnly), queryBaseData)
		if err != nil {
			return err
		}
		analyticsData = append(analyticsData, data...)
	}
	err = uc.analyticsSiteRepo.Save(ctx, analyticsData)
	if err != nil {
		return err
	}
	return nil
}

// NewAnalyticsUsecase new an analytics usecase.
func NewAnalyticsUsecase(analyticsConf *conf.Analytics, analyticsRepo AnalyticsRepo, analyticsSiteRepo AnalyticsSiteRepo, logger log.Logger) *AnalyticsUsecase {
	return &AnalyticsUsecase{
		analyticsRepo:      analyticsRepo,
		analyticsSiteRepo:  analyticsSiteRepo,
		analyticsAccountId: analyticsConf.GetAccountId(),
		propertiesID:       analyticsConf.GetPropertiesId(),
		log:                log.NewHelper(log.With(logger, "module", "usecase/analytics")),
	}
}
