package biz

import (
	"context"
	"time"
)

type BusinessSiteData struct {
	Date                   time.Time `json:"date"`
	Site                   string    `json:"site"`
	EstimatedEarnings      float64   `json:"estimated_earnings"`
	PageViews              int       `json:"page_views"`
	PageViewsRpm           float64   `json:"page_views_rpm"`
	IMPRESSIONS            int       `json:"impressions"`
	ImpressionsRpm         float64   `json:"impressions_rpm"`
	AdRequestsCoverage     float64   `json:"ad_requests_coverage"`
	CLICKS                 int       `json:"clicks"`
	AdRequests             int       `json:"ad_requests"`
	ImpressionsCtr         float64   `json:"impressions_ctr"`
	ActiveViewViewability  float64   `json:"active_view_viewability"`
	CostPerClick           float64   `json:"cost_per_click"`
	MatchedAdRequests      int       `json:"matched_ad_requests"`
	ImpressionsPerPageView float64   `json:"impressions_per_page_view"`
}

type BusinessSiteRepo interface {
	Create(ctx context.Context, data []*BusinessSiteData) error
	// CleanupStoppedSiteData 清理已下架网站的历史数据
	CleanupStoppedSiteData(ctx context.Context) error
}
