package service

import (
	"context"
	"fmt"
	"strconv"
	"time"

	"github.com/go-kratos/kratos/v2/log"
	"github.com/sourcegraph/conc"
	"google.golang.org/protobuf/types/known/emptypb"

	pb "git.minigame.vip/minicloud/service/adsense-bot/api/job/v1"
	"git.minigame.vip/minicloud/service/adsense-bot/internal/biz"
	"git.minigame.vip/minicloud/service/adsense-bot/pkg/date"
)

var DefaultJobs map[string]JobFunc

type JobFunc func(ctx context.Context, dateRange, currencyCode string) error

type JobService struct {
	uc               *biz.AdsenseUsecase
	buc              *biz.BotUsecase
	shardingSchemaUc *biz.ShardingSchemaUsecase
	analyticsUseCase *biz.AnalyticsUsecase
	purchaseUsecase  *biz.PurchaseUsecase

	log *log.Helper

	pb.UnimplementedJobAdminServer
}

func NewJobService(uc *biz.AdsenseUsecase, buc *biz.BotUsecase, analyticsUseCase *biz.AnalyticsUsecase, purchaseUsecase *biz.PurchaseUsecase,
	shardingSchemaUc *biz.ShardingSchemaUsecase, logger log.Logger) *JobService {
	job := &JobService{
		uc:               uc,
		buc:              buc,
		shardingSchemaUc: shardingSchemaUc,
		purchaseUsecase:  purchaseUsecase,
		analyticsUseCase: analyticsUseCase,
		log:              log.NewHelper(log.With(logger, "module", "service/job")),
	}
	return job
}
func (s *JobService) StartJob(_ context.Context, req *pb.StartJobRequest) (rsp *emptypb.Empty, err error) {
	wg := conc.NewWaitGroup()
	for _, name := range req.GetNames() {
		jobFunc, ok := DefaultJobs[name]
		if !ok {
			s.log.Warnf("can not find job: %s", name)
			continue
		}
		wg.Go(func() {
			err := jobFunc(context.TODO(), "", "")
			if err != nil {
				panic(err)
			}
		})

	}
	err = wg.WaitAndRecover().AsError()
	if err != nil {
		return nil, err
	}
	return &emptypb.Empty{}, nil
}

func (s *JobService) AutoMigrate(ctx context.Context, req *pb.AutoMigrateRequest) (rsp *emptypb.Empty, err error) {
	//for _, year := range req.GetYears() {
	err = s.shardingSchemaUc.AutoMigrate(ctx, int(req.GetYears()[0]))
	if err != nil {
		return
	}
	//}
	return &emptypb.Empty{}, nil
}

func (s *JobService) Init() {
	DefaultJobs = map[string]JobFunc{
		JobNameSiteCountry: s.siteCountryData,

		JobNamePageUrl: s.pageURLData,

		JobNameChannelCountry: s.channelCountryData,

		JobNameUrlChannelCountry: s.urlChannelCountryData,

		JobNameSite: s.siteData,

		JobNameSiteAdFormat: s.siteAdFormatData,
		JobNameSiteAdUnit:   s.siteAdUnitData,

		JobNameSiteAdFormatHistory: s.siteAdFormatHistoryData,

		JobNameSiteCountryAdFormat:       s.siteCountryAdFormatData,
		JobNameChannelAdFormat:           s.channelAdFormatData,
		JobNameChannel:                   s.channelData,
		JobNamePageUrlAdFormat:           s.pageURLAdFormatData,
		JobNameUrlChannel:                s.urlChannelData,
		JobNameUrlChannelAdFormat:        s.urlChannelAdFormatData,
		JobNameUrlChannelCountryAdFormat: s.urlChannelCountryAdFormatData,
		JobNameChannelCountryAdFormat:    s.channelCountryAdFormatData,
		JobNameGenerateGames:             s.generateGamesTable,
		JobNameGenerateGamesHkd:          s.generateGamesHKDTable,

		JobNameGenerateGamesCountry:    s.generateGamesCountryTable,
		JobNameGenerateGamesCountryHkd: s.generateGamesCountryHKDTable,

		JobNameCustomChannel:                     s.customChannelData,
		JobNameAutoCreateSchema:                  s.autoCreateSchema,
		JobNameGenerateCooperationSiteUrlChannel: s.generateCooperationSiteUrlChannel,
		JobNameAppCountrySummary:                 s.appCountrySummary,
		JobNameDjsSiteAdFormatCountryData:        s.djsSiteAdFormatCountryData,
		JobNameAnalyticsBase:                     s.analyticsBaseData,
		JobNameAnalyticsSite:                     s.analyticsSiteData,
		JobNameMergeSite:                         s.mergeSite,
		JobNameGenerateGaChannelReport:           s.generateGaChannelReport,

		JobNameTgAd: s.tgAdData,

		JobPurchaseInfo:        s.purchaseInfoData,
		JobPurchaseSummaryInfo: s.purchaseSummaryData,

		JobBusinessSite: s.businessSiteData,
	}
}

func (s *JobService) siteCountryData(ctx context.Context, dateRange, currencyCode string) error {
	metrics := StandardAdSenseMetrics
	orderBy := StandardOrderBy
	dimensions := SiteCountryDimensions
	if dateRange == "" {
		dateRange = DateRangeLast7Days
	}
	if currencyCode == "" {
		currencyCode = biz.USD
	}
	adSenseData, err := s.uc.GetAdSenseDataByCsv(ctx, metrics, orderBy, dimensions, []string{}, dateRange, currencyCode)
	if err != nil {
		s.log.WithContext(ctx).Errorf("[siteCountryData]: failed to get site country data: %v", err)
		return err
	}
	if dateRange != DateRangeToday {
		// 获取额外的历史数据（15天和30天）
		additionalData, err := s.fetchAdditionalHistoricalData(ctx, metrics, orderBy, dimensions, currencyCode)
		if err != nil {
			return err
		}
		// 合并所有数据
		adSenseData.Data = append(adSenseData.Data, additionalData...)
	}
	err = s.uc.SaveSiteCountryData(ctx, adSenseData, currencyCode)
	if err != nil {
		s.log.WithContext(ctx).Errorf("[siteCountryData]: failed to save site country data: %v", err)
		return err
	}
	/*err = s.uc.SendMsg(ctx, fmt.Sprintf("实时新增采集siteCountry数据<font color=\"warning\">%d条</font>,请注意。", len(adSenseData.Rows)))
	if err != nil {
		return err
	}*/
	s.log.WithContext(ctx).Infof("[siteCountryData]: job done")
	return nil
}

func (s *JobService) pageURLData(ctx context.Context, dateRange, currencyCode string) error {
	metrics := StandardAdSenseMetrics
	orderBy := StandardOrderBy
	dimensions := PageUrlDimensions
	var filters []string
	if dateRange == "" {
		dateRange = DateRangeYesterday
	}
	if currencyCode == "" {
		currencyCode = biz.USD
	}
	adSenseData, err := s.uc.GetAdSenseData(ctx, metrics, orderBy, dimensions, filters, dateRange, currencyCode)
	if err != nil {
		s.log.WithContext(ctx).Errorf("[pageURLData]: failed to get adsense page url data: %v", err)
		return err
	}
	err = s.uc.SavePageURLData(ctx, adSenseData, currencyCode)
	if err != nil {
		s.log.WithContext(ctx).Errorf("[pageURLData]: failed to save page url data: %v", err)
		return err
	}
	//err = s.uc.SendMsg(ctx, fmt.Sprintf("实时新增采集pageURL数据<font color=\"warning\">%d条</font>,请注意。", len(adSenseData.Rows)))
	//if err != nil {
	//	return err
	//}
	s.log.WithContext(ctx).Infof("[pageURLData]: job done")
	return nil
}

func (s *JobService) channelCountryData(ctx context.Context, dateRange, currencyCode string) error {
	metrics := StandardAdSenseMetrics
	orderBy := StandardOrderBy
	dimensions := ChannelCountryDimensions
	if dateRange == "" {
		dateRange = DateRangeYesterday
	}
	if currencyCode == "" {
		currencyCode = biz.USD
	}
	adSenseData, err := s.uc.GetAdSenseData(ctx, metrics, orderBy, dimensions, []string{}, dateRange, currencyCode)
	if err != nil {
		s.log.WithContext(ctx).Errorf("[channelCountryData]: failed to get channel country data: %v", err)
		return err
	}
	err = s.uc.SaveChannelCountryData(ctx, adSenseData, currencyCode)
	if err != nil {
		s.log.WithContext(ctx).Errorf("[channelCountryData]: failed to save channel country data: %v", err)
		return err
	}
	//err = s.uc.SendMsg(ctx, fmt.Sprintf("实时新增采集channelCountry数据<font color=\"warning\">%d条</font>,请注意。", len(adSenseData.Rows)))
	//if err != nil {
	//	return err
	//}
	s.log.WithContext(ctx).Infof("[channelCountryData]: job done")
	return nil
}

func (s *JobService) urlChannelCountryData(ctx context.Context, dateRange, currencyCode string) error {
	metrics := StandardAdSenseMetrics
	orderBy := StandardOrderBy
	dimensions := UrlChannelCountryDimensions
	if dateRange == "" {
		dateRange = DateRangeYesterday
	}
	if currencyCode == "" {
		currencyCode = biz.USD
	}
	adSenseData, err := s.uc.GetAdSenseData(ctx, metrics, orderBy, dimensions, []string{}, dateRange, currencyCode)
	if err != nil {
		s.log.WithContext(ctx).Errorf("[urlChannelCountryData]: failed to get url channel country data: %v", err)
		return err

	}
	err = s.uc.SaveUrlChannelCountryData(ctx, adSenseData, currencyCode)
	if err != nil {
		s.log.WithContext(ctx).Errorf("[urlChannelCountryData]: failed to save url channel country data: %v", err)
		return err
	}
	//err = s.uc.SendMsg(ctx, fmt.Sprintf("实时新增采集urlChannelCountry数据<font color=\"warning\">%d条</font>,请注意。", len(adSenseData.Rows)))
	//if err != nil {
	//	return err
	//}
	s.log.WithContext(ctx).Infof("[urlChannelCountryData]: url channel done")
	return nil
}

// fetchAdditionalHistoricalData 获取额外的历史数据（15天和30天）
func (s *JobService) fetchAdditionalHistoricalData(ctx context.Context, metrics, orderBy, dimensions []string, currencyCode string) ([][]string, error) {
	var allData [][]string

	// 定义需要获取的日期范围
	dateRanges := HistoricalDateRanges

	for _, dateRangeType := range dateRanges {
		generateDateRange, err := date.GenerateDateRange(dateRangeType)
		if err != nil {
			s.log.Errorf("failed to generate date range for %s: %v", dateRangeType, err)
			return nil, err
		}

		// 只要前15当天的数据
		data, err := s.uc.GetAdSenseDataByCustomDateRange(ctx, metrics, orderBy, dimensions, []string{}, currencyCode, &date.Range{
			StartTime: generateDateRange.StartTime,
			EndTime:   generateDateRange.StartTime, // 只要当天的数据，不需要范围
		})
		if err != nil {
			s.log.Errorf("failed to get AdSense data for %s: %v", dateRangeType, err)
			return nil, err
		}

		// 将数据追加到结果中
		allData = append(allData, data.Data...)
	}

	return allData, nil
}

func (s *JobService) siteData(ctx context.Context, dateRange, currencyCode string) error {
	metrics := StandardAdSenseMetrics
	orderBy := StandardOrderBy
	dimensions := SiteDimensions
	if dateRange == "" {
		dateRange = DateRangeLast7Days
	}
	if currencyCode == "" {
		currencyCode = biz.USD
	}
	adSenseData, err := s.uc.GetAdSenseDataByCsv(ctx, metrics, orderBy, dimensions, []string{}, dateRange, currencyCode)
	if err != nil {
		s.log.WithContext(ctx).Errorf("[siteData]: failed to get site  data: %v", err)
		return err
	}
	if dateRange != DateRangeToday {
		// 获取额外的历史数据（15天和30天）
		additionalData, err := s.fetchAdditionalHistoricalData(ctx, metrics, orderBy, dimensions, currencyCode)
		if err != nil {
			return err
		}
		// 合并所有数据
		adSenseData.Data = append(adSenseData.Data, additionalData...)
	}
	err = s.uc.SaveSiteData(ctx, adSenseData, currencyCode)
	if err != nil {
		s.log.WithContext(ctx).Errorf("[siteData]: failed to save site  data: %v", err)
		return err
	}
	if dateRange == DateRangeToday {
		err := s.uc.SaveUnifiedSite(ctx, adSenseData)
		if err != nil {
			s.log.WithContext(ctx).Errorf("[siteData]: failed to save unified site: %v", err)
			return err
		}
	}
	/*	err = s.uc.SendMsg(ctx, fmt.Sprintf("实时新增采集site数据<font color=\"warning\">%d条</font>,请注意。", len(adSenseData.Rows)))
		if err != nil {
			return err

		}*/
	s.log.WithContext(ctx).Infof("[siteData]: site data done")
	return nil
}
func (s *JobService) siteAdFormatData(ctx context.Context, dateRange, currencyCode string) error {
	metrics := NewStandardAdSenseMetrics
	orderBy := StandardOrderBy
	dimensions := SiteAdFormatDimensions
	if dateRange == "" {
		dateRange = DateRangeLast7Days
	}
	if currencyCode == "" {
		currencyCode = biz.USD
	}
	saveHistory := false
	if dateRange != DateRangeToday {
		saveHistory = true
	}
	adSenseData, err := s.uc.GetAdSenseDataByCsv(ctx, metrics, orderBy, dimensions, []string{}, dateRange, currencyCode)
	if err != nil {
		s.log.WithContext(ctx).Errorf("[siteAdFormatData]: failed to get site ad format data: %v", err)
		return err
	}
	if dateRange != DateRangeToday {
		// 获取额外的历史数据（15天和30天）
		additionalData, err := s.fetchAdditionalHistoricalData(ctx, metrics, orderBy, dimensions, currencyCode)
		if err != nil {
			return err
		}
		// 合并所有数据
		adSenseData.Data = append(adSenseData.Data, additionalData...)
	}
	err = s.uc.SaveSiteAdFormatData(ctx, adSenseData, currencyCode, saveHistory, dateRange)
	if err != nil {
		s.log.WithContext(ctx).Errorf("[siteAdFormatData]: failed to save site ad format data: %v", err)
		return err

	}
	/*err = s.uc.SendMsg(ctx, fmt.Sprintf("实时新增采集siteAdFormat数据<font color=\"warning\">%d条</font>,请注意。", len(adSenseData.Rows)))
	if err != nil {
		return err

	}*/
	s.log.WithContext(ctx).Infof("[siteAdFormatData]: job done")
	return nil
}
func (s *JobService) siteCountryAdFormatData(ctx context.Context, dateRange, currencyCode string) error {
	metrics := StandardAdSenseMetrics
	orderBy := StandardOrderBy
	dimensions := SiteCountryAdFormatDimensions
	if dateRange == "" {
		dateRange = DateRangeLast7Days
	}
	if currencyCode == "" {
		currencyCode = biz.USD
	}
	adSenseData, err := s.uc.GetAdSenseDataByCsv(ctx, metrics, orderBy, dimensions, []string{}, dateRange, currencyCode)
	if err != nil {
		s.log.WithContext(ctx).Errorf("[siteCountryAdFormatData]: failed to get site country ad format data: %v", err)
		return err
	}
	if dateRange != DateRangeToday {
		// 获取额外的历史数据（15天和30天）
		additionalData, err := s.fetchAdditionalHistoricalData(ctx, metrics, orderBy, dimensions, currencyCode)
		if err != nil {
			return err
		}
		// 合并所有数据
		adSenseData.Data = append(adSenseData.Data, additionalData...)
	}
	err = s.uc.SaveSiteCountryAdFormatData(ctx, adSenseData, currencyCode)
	if err != nil {
		s.log.WithContext(ctx).Errorf("[siteCountryAdFormatData]: failed to save site country ad format data: %v", err)
		return err

	}
	//err = s.uc.SendMsg(ctx, fmt.Sprintf("实时新增采集siteCountryAdFormat数据<font color=\"warning\">%d条</font>,请注意。", len(adSenseData.Rows)))
	//if err != nil {
	//	return err
	//
	//}
	s.log.WithContext(ctx).Infof("[siteCountryAdFormatData]: job done")
	return nil
}

func (s *JobService) channelData(ctx context.Context, dateRange, currencyCode string) error {
	metrics := StandardAdSenseMetrics
	orderBy := StandardOrderBy
	dimensions := ChannelDimensions
	var filters []string
	if dateRange == "" {
		dateRange = DateRangeYesterday
	}
	if currencyCode == "" {
		currencyCode = biz.USD
	}
	adSenseData, err := s.uc.GetAdSenseData(ctx, metrics, orderBy, dimensions, filters, dateRange, currencyCode)
	if err != nil {
		s.log.WithContext(ctx).Errorf("[channelData]: failed to get channel  data: %v", err)
		return err

	}
	err = s.uc.SaveChannelData(ctx, adSenseData, currencyCode)
	if err != nil {
		s.log.WithContext(ctx).Errorf("[channelData]: failed to save channel  data: %v", err)
		return err
	}
	/*err = s.uc.SendMsg(ctx, fmt.Sprintf("实时新增采集channel数据<font color=\"warning\">%d条</font>,请注意。", len(adSenseData.Rows)))
	if err != nil {
		return err
	}*/
	s.log.WithContext(ctx).Infof("[channelData]: job done")
	return nil
}

func (s *JobService) channelAdFormatData(ctx context.Context, dateRange, currencyCode string) error {
	metrics := StandardAdSenseMetrics
	orderBy := StandardOrderBy
	if dateRange == "" {
		dateRange = DateRangeYesterday
	}
	if currencyCode == "" {
		currencyCode = biz.USD
	}
	dimensions := ChannelAdFormatDimensions
	var filters []string
	adSenseData, err := s.uc.GetAdSenseData(ctx, metrics, orderBy, dimensions, filters, dateRange, currencyCode)
	if err != nil {
		s.log.WithContext(ctx).Errorf("[channelAdFormatData]: failed to get channel ad format data: %v", err)
		return err

	}
	err = s.uc.SaveChannelAdFormatData(ctx, adSenseData, currencyCode)
	if err != nil {
		s.log.WithContext(ctx).Errorf("[channelAdFormatData]: failed to save channel ad format data: %v", err)
		return err

	}
	//err = s.uc.SendMsg(ctx, fmt.Sprintf("实时新增采集channelAdFormat数据<font color=\"warning\">%d条</font>,请注意。", len(adSenseData.Rows)))
	//if err != nil {
	//	return err
	//
	//}
	s.log.WithContext(ctx).Infof("[channelAdFormatData]: job done")
	return nil
}

func (s *JobService) channelCountryAdFormatData(ctx context.Context, dateRange, currencyCode string) error {
	metrics := StandardAdSenseMetrics
	orderBy := StandardOrderBy
	dimensions := ChannelCountryAdFormatDimensions
	var filters []string
	if dateRange == "" {
		dateRange = DateRangeYesterday
	}
	if currencyCode == "" {
		currencyCode = biz.USD
	}
	adSenseData, err := s.uc.GetAdSenseDataByCsv(ctx, metrics, orderBy, dimensions, filters, dateRange, currencyCode)
	if err != nil {
		s.log.WithContext(ctx).Errorf("[channelCountryAdFormatData]: failed to get channel country ad format data: %v", err)
		return err
	}
	err = s.uc.SaveChannelCountryAdFormatData(ctx, adSenseData, currencyCode)
	if err != nil {
		s.log.WithContext(ctx).Errorf("[channelCountryAdFormatData]: failed to save channel country ad format data: %v", err)
		return err
	}
	/*err = s.uc.SendMsg(ctx, fmt.Sprintf("实时新增采集channelCountryAdFormat数据<font color=\"warning\">%d条</font>,请注意。", len(adSenseData.Rows)))
	if err != nil {
		return err
	}*/
	s.log.WithContext(ctx).Infof("[channelCountryAdFormatData]: job done")
	return nil
}

func (s *JobService) pageURLAdFormatData(ctx context.Context, dateRange, currencyCode string) error {
	metrics := StandardAdSenseMetrics
	orderBy := StandardOrderBy
	dimensions := PageUrlAdFormatDimensions
	var filters []string
	if dateRange == "" {
		dateRange = DateRangeYesterday
	}
	if currencyCode == "" {
		currencyCode = biz.USD
	}
	adSenseData, err := s.uc.GetAdSenseData(ctx, metrics, orderBy, dimensions, filters, dateRange, currencyCode)
	if err != nil {
		s.log.WithContext(ctx).Errorf("[pageURLAdFormatData]: failed to get adsense page url data: %v", err)
		return err
	}
	err = s.uc.SavePageURLAdFormatData(ctx, adSenseData, currencyCode)
	if err != nil {
		s.log.WithContext(ctx).Errorf("[pageURLAdFormatData]: failed to save page  url data: %v", err)
		return err
	}
	/*err = s.uc.SendMsg(ctx, fmt.Sprintf("实时新增采集pageURLAdFormat数据<font color=\"warning\">%d条</font>,请注意。", len(adSenseData.Rows)))
	if err != nil {
		return err
	}*/
	s.log.WithContext(ctx).Infof("[pageURLAdFormatData]: job done")
	return nil
}
func (s *JobService) urlChannelData(ctx context.Context, dateRange, currencyCode string) error {
	metrics := StandardAdSenseMetrics
	orderBy := StandardOrderBy
	dimensions := UrlChannelDimensions
	if dateRange == "" {
		dateRange = DateRangeYesterday
	}
	if currencyCode == "" {
		currencyCode = biz.USD
	}
	adSenseData, err := s.uc.GetAdSenseData(ctx, metrics, orderBy, dimensions, []string{}, dateRange, currencyCode)
	if err != nil {
		s.log.WithContext(ctx).Errorf("[urlChannelData]: failed to get url channel  data: %v", err)
		return err
	}
	err = s.uc.SaveUrlChannelData(ctx, adSenseData, currencyCode)
	if err != nil {
		s.log.WithContext(ctx).Errorf("[urlChannelData]: failed to save url channel  data: %v", err)
		return err
	}
	/*err = s.uc.SendMsg(ctx, fmt.Sprintf("实时新增采集urlChannel数据<font color=\"warning\">%d条</font>,请注意。", len(adSenseData.Rows)))
	if err != nil {
		return err
	}*/
	s.log.WithContext(ctx).Infof("[urlChannelData]: url channel done")
	return nil
}
func (s *JobService) urlChannelAdFormatData(ctx context.Context, dateRange, currencyCode string) error {
	metrics := StandardAdSenseMetrics
	orderBy := StandardOrderBy
	dimensions := UrlChannelAdFormatDimensions
	if dateRange == "" {
		dateRange = DateRangeYesterday
	}
	if currencyCode == "" {
		currencyCode = biz.USD
	}
	adSenseData, err := s.uc.GetAdSenseData(ctx, metrics, orderBy, dimensions, []string{}, dateRange, currencyCode)
	if err != nil {
		s.log.WithContext(ctx).Errorf("[urlChannelAdFormatData]: failed to get url channel ad format data: %v", err)
		return err
	}
	err = s.uc.SaveUrlChannelAdFormatData(ctx, adSenseData, currencyCode)
	if err != nil {
		s.log.WithContext(ctx).Errorf("[urlChannelAdFormatData]: failed to save url channel ad format data: %v", err)
		return err
	}
	/*	err = s.uc.SendMsg(ctx, fmt.Sprintf("实时新增采集urlChannelAdFormat数据<font color=\"warning\">%d条</font>,请注意。", len(adSenseData.Rows)))
		if err != nil {
			return err
		}*/
	s.log.WithContext(ctx).Infof("[urlChannelAdFormatData]: url channel done")
	return nil
}
func (s *JobService) urlChannelCountryAdFormatData(ctx context.Context, dateRange, currencyCode string) error {
	metrics := StandardAdSenseMetrics
	orderBy := StandardOrderBy
	dimensions := UrlChannelCountryAdFormatDimensions
	if dateRange == "" {
		dateRange = DateRangeYesterday
	}
	if currencyCode == "" {
		currencyCode = biz.USD
	}
	adSenseData, err := s.uc.GetAdSenseDataByCsv(ctx, metrics, orderBy, dimensions, []string{}, dateRange, currencyCode)
	if err != nil {
		s.log.WithContext(ctx).Errorf("[urlChannelCountryAdFormatData]: failed to get url channel country ad format data: %v", err)
		return err

	}
	err = s.uc.SaveUrlChannelCountryAdFormatData(ctx, adSenseData, currencyCode)
	if err != nil {
		s.log.WithContext(ctx).Errorf("[urlChannelCountryAdFormatData]: failed to save url channel country ad format data: %v", err)
		return err

	}
	/*err = s.uc.SendMsg(ctx, fmt.Sprintf("实时新增采集urlChannelCountryAdFormat数据<font color=\"warning\">%d条</font>,请注意。", len(adSenseData.Rows)))
	if err != nil {
		return err

	}*/
	s.log.WithContext(ctx).Infof("[urlChannelCountryAdFormatData]: url channel country done")
	return nil
}

func (s *JobService) djsSiteAdFormatCountryData(ctx context.Context, _, _ string) error {
	metrics := DjsAdSenseMetrics
	dimensions := DjsAdSenseDimensions
	adSenseData, err := s.uc.GetDjsSiteAdFormatCountryData(ctx, metrics, dimensions)
	if err != nil {
		s.log.WithContext(ctx).Errorf("[djsSiteAdFormatCountryData]: failed to get site adformat  country data: %v", err)
		return err
	}

	data, err := s.uc.GenerateExcel(append(dimensions, metrics...), adSenseData)
	if err != nil {
		return err
	}

	err = s.uc.SendEmail(ctx, data, &biz.SendInfo{
		ReportName: ReportNameDjsTodayData,
		DateRange:  DateRangeToday,
		SendTime:   SendTime4Hour,
	})
	if err != nil {
		return err
	}
	s.log.WithContext(ctx).Infof("[djsSiteAdFormatCountryData]:djsSiteAdFormatCountryData done")
	return nil
}

func (s *JobService) generateGamesHKDTable(ctx context.Context, dateRange, _ string) error {
	cstSh, _ := time.LoadLocation("Asia/Shanghai")
	if dateRange == "" {
		dateRange = DateRangeYesterday
	}

	start, end := date.GetDateRange(dateRange, time.Now().In(cstSh))

	pvs, err := s.uc.ListHKDChannels(ctx, start, end)
	if err != nil {
		s.log.WithContext(ctx).Errorf("[generateGamesHKDTable]: list channel pv data err: %v", err)
		return err

	}
	adFormatsMap, err := s.uc.ListHKDChannelAdFormats(ctx, start, end)
	if err != nil {
		s.log.WithContext(ctx).Errorf("[generateGamesHKDTable]: list channel ad format err : %v", err)
		return err

	}
	err = s.uc.MergeHKDChannels(ctx, pvs, adFormatsMap)
	if err != nil {
		s.log.WithContext(ctx).Errorf("[generateGamesHKDTable]: generate game data err : %v", err)
		return err

	}
	s.log.WithContext(ctx).Infof("[generateGamesHKDTable]: done ! ! ! !")
	return nil
}

func (s *JobService) generateGamesTable(ctx context.Context, dateRange, _ string) error {
	cstSh, _ := time.LoadLocation("Asia/Shanghai")
	if dateRange == "" {
		dateRange = DateRangeYesterday
	}

	start, end := date.GetDateRange(dateRange, time.Now().In(cstSh))

	pvs, err := s.uc.ListChannels(ctx, start, end)
	if err != nil {
		s.log.WithContext(ctx).Errorf("[generateGamesTable]: list channel pv data err: %v", err)
		return err

	}
	adFormatsMap, err := s.uc.ListChannelAdFormats(ctx, start, end)
	if err != nil {
		s.log.WithContext(ctx).Errorf("[generateGamesTable]: list channel ad format err : %v", err)
		return err

	}
	err = s.uc.MergeChannels(ctx, pvs, adFormatsMap)
	if err != nil {
		s.log.WithContext(ctx).Errorf("[generateGamesTable]: generate game data err : %v", err)
		return err

	}
	s.log.WithContext(ctx).Infof("[generateGamesTable]: done ! ! ! !")
	return nil
}
func (s *JobService) generateGamesCountryTable(ctx context.Context, dateRange, _ string) error {
	cstSh, _ := time.LoadLocation("Asia/Shanghai")
	if dateRange == "" {
		dateRange = DateRangeYesterday
	}
	start, end := date.GetDateRange(dateRange, time.Now().In(cstSh))

	pvs, err := s.uc.ListChannelCountriesData(ctx, start, end)
	if err != nil {
		s.log.WithContext(ctx).Errorf("[generateGamesCountryTable]: list channel country pv data err: %v", err)
		return err

	}
	adFormatsMap, err := s.uc.ListChannelCountryAdFormats(ctx, start, end)
	if err != nil {
		s.log.WithContext(ctx).Errorf("[generateGamesCountryTable]: list channel country ad format err : %v", err)
		return err

	}
	err = s.uc.MergeChannelsCountry(ctx, pvs, adFormatsMap)
	if err != nil {
		s.log.WithContext(ctx).Errorf("[generateGamesCountryTable]: generate game country data err : %v", err)
		return err

	}
	s.log.WithContext(ctx).Infof("[generateGamesCountryTable]: done ! ! ! !")
	return nil
}
func (s *JobService) generateGamesCountryHKDTable(ctx context.Context, dateRange, _ string) error {
	cstSh, _ := time.LoadLocation("Asia/Shanghai")
	if dateRange == "" {
		dateRange = DateRangeYesterday
	}
	start, end := date.GetDateRange(dateRange, time.Now().In(cstSh))

	pvs, err := s.uc.ListChannelCountriesHKDData(ctx, start, end)
	if err != nil {
		s.log.WithContext(ctx).Errorf("[generateGamesCountryHKDTable]: list channel country pv data err: %v", err)
		return err

	}
	adFormatsMap, err := s.uc.ListChannelCountryAdFormatsHKD(ctx, start, end)
	if err != nil {
		s.log.WithContext(ctx).Errorf("[generateGamesCountryHKDTable]: list channel country ad format err : %v", err)
		return err

	}
	err = s.uc.MergeChannelsCountryHKD(ctx, pvs, adFormatsMap)
	if err != nil {
		s.log.WithContext(ctx).Errorf("[generateGamesCountryHKDTable]: generate game country data err : %v", err)
		return err

	}
	s.log.WithContext(ctx).Infof("[generateGamesCountryHKDTable]: done ! ! ! !")
	return nil
}

func (s *JobService) customChannelData(ctx context.Context, _, _ string) error {
	customChannelData, err := s.uc.GetAdsenseCustomChannelData(ctx)
	if err != nil {
		s.log.WithContext(ctx).Errorf("[customChannelData]: failed to get custom channel data: %v", err)
		return err
	}
	err = s.uc.SaveCustomChannelData(ctx, customChannelData)
	if err != nil {
		s.log.WithContext(ctx).Errorf("[customChannelData]: failed to save custom channel data: %v", err)
		return err
	}
	s.log.WithContext(ctx).Infof("[customChannelData]: done!!!")
	return nil
}

func (s *JobService) autoCreateSchema(ctx context.Context, dateRange, _ string) error {
	year := time.Now().Year() + 1
	var err error
	if dateRange != "" {
		year, err = strconv.Atoi(dateRange)
		if err != nil {
			s.log.WithContext(ctx).Errorf("[autoCreateSchema]: parse int err: %v", err)
			return err
		}
	}
	err = s.shardingSchemaUc.CreateTableSchema(ctx, year)
	if err != nil {
		return err
	}
	s.log.WithContext(ctx).Infof("[autoCreateSchema]: done!!!")

	return nil
}

func (s *JobService) generateCooperationSiteUrlChannel(ctx context.Context, _, _ string) error {
	urlChannelData, err := s.uc.GetAdsenseUrlChannelData(ctx)
	if err != nil {
		s.log.WithContext(ctx).Errorf("[generateCooperationSiteUrlChannel]: failed to get adsense url channel data: %v", err)
		return err
	}
	site, err := s.uc.GetCooperationSite(ctx)
	if err != nil {
		s.log.WithContext(ctx).Errorf("[generateCooperationSiteUrlChannel]: failed to get cooperation site: %v", err)
		return err
	}
	err = s.uc.SaveNoCooperationSite(ctx)
	if err != nil {
		return err
	}
	err = s.uc.SaveCooperationSiteUrlChannel(ctx, urlChannelData, site)
	if err != nil {
		s.log.WithContext(ctx).Errorf("[generateCooperationSiteUrlChannel]: failed to save Cooperation Site UrlChannel channel data: %v", err)
		return err
	}
	err = s.uc.CreateOrUpdateCooperation(ctx)
	if err != nil {
		s.log.WithContext(ctx).Errorf("[generateCooperationSiteUrlChannel]: failed to create or update cooperation: %v", err)
		return err
	}
	s.log.WithContext(ctx).Infof("[generateCooperationSiteUrlChannel]: done!!!")
	return nil
}

func (s *JobService) appCountrySummary(ctx context.Context, _, _ string) error {
	err := s.uc.SaveAppCountrySummary(ctx)
	if err != nil {
		s.log.WithContext(ctx).Errorf("[appCountrySummary]: failed to save app country summary: %v", err)
		return err
	}

	s.log.WithContext(ctx).Infof("[appCountrySummary]: done!!!")
	return nil
}

func (s *JobService) Fetch(ctx context.Context, number uint32) error {
	err := s.uc.FetchEmailData(ctx, number)
	if err != nil {
		return err
	}
	return nil
}

func (s *JobService) analyticsBaseData(ctx context.Context, dateRange, _ string) error {
	if dateRange == "" {
		dateRange = DateRangeLast7Days
	}
	err := s.analyticsUseCase.SaveAnalyticsBaseData(ctx, dateRange)
	if err != nil {
		s.log.WithContext(ctx).Errorf("[analyticsBaseData]: failed to get google  analytics data failed: %v", err)
		return err
	}

	s.log.WithContext(ctx).Infof("[analyticsBaseData]: job done")
	return nil
}

func (s *JobService) analyticsSiteData(ctx context.Context, dateRange, _ string) error {
	if dateRange == "" {
		dateRange = DateRangeLast7Days
	}
	err := s.analyticsUseCase.SaveAnalyticsSiteData(ctx, dateRange)
	if err != nil {
		s.log.WithContext(ctx).Errorf("[analyticsSiteData]: failed to get google  analytics site data failed: %v", err)
		return err
	}

	s.log.WithContext(ctx).Infof("[analyticsSiteData]: job done")
	return nil
}

func (s *JobService) mergeSite(ctx context.Context, dateRange, _ string) error {
	if dateRange == "" {
		dateRange = DateRangeLast7Days
	}
	start, end := date.GetDateRange(dateRange, time.Now())

	siteMaps, err := s.uc.ListSites(ctx, start, end)
	if err != nil {
		s.log.WithContext(ctx).Errorf("[mergeSite]: list adsense site data err: %v", err)
		return err
	}
	adManagerSiteMap, err := s.uc.ListAdManagerSites(ctx, start, end)
	if err != nil {
		s.log.WithContext(ctx).Errorf("[mergeSite]: list ad manager site data err : %v", err)
		return err
	}
	err = s.uc.MergeSites(ctx, siteMaps, adManagerSiteMap)
	if err != nil {
		s.log.WithContext(ctx).Errorf("[mergeSite]: merge adsense adx site data err : %v", err)
		return err
	}
	s.log.WithContext(ctx).Infof("[mergeSite]: done ! ! ! !")
	return nil
}

func (s *JobService) tgAdData(ctx context.Context, _ string, _ string) error {
	err := s.uc.SaveTgAd(ctx)
	if err != nil {
		s.log.WithContext(ctx).Errorf("[tgAdData]: save tg ad data err :%v", err)
		return err
	}
	s.log.WithContext(ctx).Infof("[tgAdData]: done ! ! ! !")
	return nil
}

func (s *JobService) siteAdFormatHistoryData(ctx context.Context, dateRange string, currencyCode string) error {
	metrics := NewStandardAdSenseMetrics
	orderBy := StandardOrderBy
	dimensions := SiteAdFormatDimensions
	if dateRange == "" {
		dateRange = DateRangeLast30Days
	}
	if currencyCode == "" {
		currencyCode = biz.USD
	}

	adSenseData, err := s.uc.GetAdSenseDataByCsv(ctx, metrics, orderBy, dimensions, []string{}, dateRange, currencyCode)
	if err != nil {
		s.log.WithContext(ctx).Errorf("[siteAdFormatHistoryData]: failed to get site ad format data: %v", err)
		return err
	}

	err = s.uc.SaveSiteAdFormatHistoryData(ctx, adSenseData)
	if err != nil {
		s.log.WithContext(ctx).Errorf("[siteAdFormatHistoryData]: failed to save site ad format data: %v", err)
		return err

	}
	s.log.WithContext(ctx).Infof("[siteAdFormatHistoryData]: job done")
	return nil
}

func (s *JobService) generateGaChannelReport(ctx context.Context, dateRange, _ string) error {
	if dateRange == "" {
		dateRange = "MONTH_TO_DATE" // 这个值在date包中定义，暂时保持硬编码
	}
	now := time.Now().Local()
	start, end := date.GetDateRange(dateRange, now)
	if dateRange == "MONTH_TO_DATE" {
		end = end.AddDate(0, 0, -1)
	}
	// 如果是1号，就查询上个月的数据
	if now.Day() == 1 {
		start, end = date.GetDateRange("LAST_MONTH", now)
	}
	s.log.WithContext(ctx).Infof("[generateGaChannelReport]: start: %v, end: %v", start, end)
	estimatedEarningsData, err := s.uc.GetSiteEstimatedEarnings(ctx, start, end)
	if err != nil {
		s.log.WithContext(ctx).Errorf("[generateGaChannelReport]: failed to get site estimated earnings: %v", err)
		return err
	}
	gaData, gaVisitPageCountMap, err := s.uc.GetSiteGaData(ctx, start, end)
	if err != nil {
		s.log.WithContext(ctx).Errorf("[generateGaChannelReport]: failed to get site ga data: %v", err)
		return err
	}
	maxSiteEstimatedEarningsData, err := s.uc.GetMaxSiteEstimatedEarnings(ctx, start, end)
	if err != nil {
		s.log.WithContext(ctx).Errorf("[generateGaChannelReport]: failed to get max site estimated earnings: %v", err)
		return err
	}
	data, err := s.uc.GenerateGaChannelReportExcel(estimatedEarningsData, gaData, gaVisitPageCountMap, maxSiteEstimatedEarningsData)
	if err != nil {
		s.log.WithContext(ctx).Errorf("[generateGaChannelReport]: failed to generate ga channel report: %v", err)
		return err
	}
	err = s.uc.SendGaChannelReportEmail(ctx, data, &biz.SendInfo{
		ReportName: fmt.Sprintf("渠道监控报表 %s-%s", start.Format(time.DateOnly), end.Format(time.DateOnly)),
	})
	if err != nil {
		s.log.WithContext(ctx).Errorf("[generateGaChannelReport]: failed to send ga channel report: %v", err)
		return err
	}
	s.log.WithContext(ctx).Infof("[generateGaChannelReport]: job done")
	return nil
}

func (s *JobService) purchaseInfoData(ctx context.Context, _, _ string) error {
	// 调用业务逻辑层处理
	err := s.purchaseUsecase.FetchAndCreatePurchases(ctx)
	if err != nil {
		s.log.WithContext(ctx).Errorf("[FetchAndCreatePurchases]: 从外部API获取并创建内购数据失败: %v", err)
		return err
	}

	s.log.WithContext(ctx).Info("[FetchAndCreatePurchases]: 成功从外部API获取并创建内购数据")
	return nil
}

func (s *JobService) purchaseSummaryData(ctx context.Context, _, _ string) error {
	// 调用业务逻辑层处理汇总数据
	err := s.purchaseUsecase.FetchAndCreatePurchaseSummaries(ctx)
	if err != nil {
		s.log.WithContext(ctx).Errorf("[FetchAndCreatePurchaseSummaries]: 从外部API获取并创建内购汇总数据失败: %v", err)
		return err
	}

	s.log.WithContext(ctx).Info("[FetchAndCreatePurchaseSummaries]: 成功从外部API获取并创建内购汇总数据")
	return nil
}

func (s *JobService) siteAdUnitData(ctx context.Context, dateRange string, currencyCode string) error {
	metrics := NewStandardAdSenseMetrics
	orderBy := StandardOrderBy
	dimensions := SiteAdUnitDimensions
	if dateRange == "" {
		dateRange = DateRangeLast7Days
	}
	if currencyCode == "" {
		currencyCode = biz.USD
	}

	adSenseData, err := s.uc.GetAdSenseDataByCsv(ctx, metrics, orderBy, dimensions, []string{}, dateRange, currencyCode)
	if err != nil {
		s.log.WithContext(ctx).Errorf("[siteAdUnitData]: failed to get site ad unit data: %v", err)
		return err
	}
	if dateRange != DateRangeToday {
		// 获取额外的历史数据（15天和30天）
		additionalData, err := s.fetchAdditionalHistoricalData(ctx, metrics, orderBy, dimensions, currencyCode)
		if err != nil {
			s.log.WithContext(ctx).Errorf("[siteAdUnitData]: failed to fetch additional historical data: %v", err)
			return err
		}
		// 合并所有数据
		adSenseData.Data = append(adSenseData.Data, additionalData...)
	}
	err = s.uc.SaveSiteAdUnitData(ctx, adSenseData)
	if err != nil {
		s.log.WithContext(ctx).Errorf("[siteAdUnitData]: failed to save site ad unit data: %v", err)
		return err

	}

	s.log.WithContext(ctx).Infof("[siteAdUnitData]: job done")
	return nil
}

// businessSiteData 生成商户平台渠道特定的数据
func (s *JobService) businessSiteData(ctx context.Context, dateRange, _ string) error {
	if dateRange == "" {
		dateRange = DateRangeLast7Days
	}
	start, end := date.GetDateRange(dateRange, time.Now())

	err := s.uc.SaveBusinessSiteData(ctx, start, end)
	if err != nil {
		s.log.WithContext(ctx).Errorf("[mergeSite]: list adsense site data err: %v", err)
		return err
	}

	s.log.WithContext(ctx).Info("[businessSiteData]: job done")
	return nil
}
