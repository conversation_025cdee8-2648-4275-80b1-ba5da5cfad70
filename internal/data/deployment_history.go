package data

import (
	"context"
	"crypto/tls"
	"time"

	"github.com/go-kratos/kratos/v2/log"
	"github.com/go-kratos/kratos/v2/middleware/logging"
	"github.com/go-kratos/kratos/v2/middleware/recovery"
	"github.com/go-kratos/kratos/v2/transport/grpc"
	ggrpc "google.golang.org/grpc"

	extern "git.minigame.vip/minicloud/service/adsense-bot/api/open-proto/open-admin"
	"git.minigame.vip/minicloud/service/adsense-bot/internal/biz"
	"git.minigame.vip/minicloud/service/adsense-bot/internal/conf"
)

// deploymentHistoryRepo
type deploymentHistoryRepo struct {
	data       *Data
	log        *log.Helper
	mockMode   bool
	grpcClient extern.MerchantChannelForDataClient
}

// NewDeploymentHistoryRepo .
func NewDeploymentHistoryRepo(data *Data, openAdminConf *conf.OpenAdmin, logger log.Logger) biz.DeploymentHistoryRepo {
	repo := &deploymentHistoryRepo{
		data:     data,
		log:      log.NewHelper(log.With(logger, "module", "data/deployment_history")),
		mockMode: openAdminConf.MockMode,
	}

	// 初始化gRPC客户端
	if openAdminConf.GetAddr() != "" {
		repo.initGrpcClient(openAdminConf, logger)
	}

	return repo
}

// initGrpcClient 初始化gRPC客户端
func (r *deploymentHistoryRepo) initGrpcClient(conf *conf.OpenAdmin, logger log.Logger) {
	timeout := 30 * time.Second
	if conf.Timeout != nil {
		timeout = conf.Timeout.AsDuration()
	}

	var conn *ggrpc.ClientConn
	var err error
	if conf.GrpcTls {
		conn, err = grpc.Dial(
			context.Background(),
			grpc.WithEndpoint(conf.GetAddr()),
			grpc.WithTimeout(timeout),
			grpc.WithTLSConfig(&tls.Config{}),
			grpc.WithMiddleware(recovery.Recovery(),
				logging.Client(logger)),
		)
	} else {
		conn, err = grpc.DialInsecure(
			context.Background(),
			grpc.WithEndpoint(conf.GetAddr()),
			grpc.WithTimeout(timeout),
			grpc.WithMiddleware(
				recovery.Recovery(),
				logging.Client(logger)),
		)
	}

	if err != nil {
		r.log.Errorf("Failed to create gRPC connection: %v", err)
		return
	}

	r.grpcClient = extern.NewMerchantChannelForDataClient(conn)
	r.log.Info("gRPC client initialized successfully")
}

// GetSiteDeploymentHistory 获取指定域名的游戏内广告过滤时间段信息
// 返回需要只显示游戏内广告的网站和对应的时间段
func (r *deploymentHistoryRepo) GetSiteDeploymentHistory(ctx context.Context, domains []string) (map[string][]*biz.SiteDeploymentHistory, error) {
	if len(domains) == 0 {
		r.log.WithContext(ctx).Warn("GetSiteDeploymentHistory called with empty domains list")
		return make(map[string][]*biz.SiteDeploymentHistory), nil
	}

	r.log.WithContext(ctx).Infof("Fetching game-only ad filter time ranges for %d domains", len(domains))

	// 如果开启了mock模式，返回mock数据
	if r.mockMode {
		r.log.WithContext(ctx).Info("Mock mode enabled, returning mock data")
		return r.getMockDeploymentHistory(), nil
	}

	// 检查gRPC客户端是否可用
	if r.grpcClient == nil {
		r.log.WithContext(ctx).Error("gRPC client not initialized")
		return make(map[string][]*biz.SiteDeploymentHistory), nil
	}

	// 获取游戏内广告过滤时间段信息
	return r.getGameOnlyAdFilterStatus(ctx, domains)
}

// getGameOnlyAdFilterStatus 获取网站游戏内广告过滤时间段信息
func (r *deploymentHistoryRepo) getGameOnlyAdFilterStatus(ctx context.Context, domains []string) (map[string][]*biz.SiteDeploymentHistory, error) {
	r.log.WithContext(ctx).Info("Fetching game-only ad filter time ranges via gRPC")

	// 获取渠道链接过滤时间区间列表（网站需要只显示游戏内广告的时间列表）
	filterReq := &extern.MerchantChannelForDataFilterTimeRangeRequest{}
	filterResp, err := r.grpcClient.FilterTimeRange(ctx, filterReq)
	if err != nil {
		r.log.WithContext(ctx).Errorf("Failed to get filter time ranges via gRPC: %v", err)
		return nil, err
	}

	// 处理各域名的过滤时间段信息
	result := r.checkCurrentFilterStatus(ctx, domains, filterResp)

	r.log.WithContext(ctx).Infof("Successfully processed filter time ranges for %d domains, got %d sites with time ranges",
		len(domains), len(result))

	return result, nil
}

// checkCurrentFilterStatus 返回各域名的游戏内广告过滤时间段信息
// 返回需要只显示游戏内广告的网站和对应的时间段
func (r *deploymentHistoryRepo) checkCurrentFilterStatus(ctx context.Context, domains []string, filterResp *extern.MerchantChannelForDataFilterTimeRangeReply) map[string][]*biz.SiteDeploymentHistory {
	result := make(map[string][]*biz.SiteDeploymentHistory)

	// 创建域名集合用于快速查找
	domainSet := make(map[string]bool)
	for _, domain := range domains {
		domainSet[domain] = true
	}

	r.log.WithContext(ctx).Infof("Processing filter time ranges for %d sites", len(filterResp.GetSites()))

	// 处理每个站点的过滤时间区间
	for _, site := range filterResp.GetSites() {
		siteDomain := site.GetSite()
		if siteDomain == "" {
			r.log.WithContext(ctx).Warn("Empty site domain in filter response")
			continue
		}

		// 只处理请求的域名
		if !domainSet[siteDomain] {
			continue
		}

		// 为每个时间段创建一个记录
		var siteHistories []*biz.SiteDeploymentHistory
		for _, timeRange := range site.GetTimeRanges() {
			if timeRange.GetStartTime() == nil {
				r.log.WithContext(ctx).Warnf("Time range with nil start_time for site %s", siteDomain)
				continue
			}

			startTime := timeRange.GetStartTime().AsTime()

			// 检查是否为零时间值，如果是则跳过
			if startTime.IsZero() {
				r.log.WithContext(ctx).Warnf("Time range with zero start_time (0001-01-01 00:00:00 +0000) for site %s, skipping", siteDomain)
				continue
			}

			var endTime *time.Time
			if timeRange.GetEndTime() != nil {
				endTimeValue := timeRange.GetEndTime().AsTime()
				endTime = &endTimeValue
			}

			// 创建时间段记录
			history := &biz.SiteDeploymentHistory{
				Domain:               siteDomain,
				MonetizationStrategy: biz.MonetizationStrategyGameAds, // 在这个时间段内只显示游戏内广告
				StartTime:            &startTime,
				EndTime:              endTime, // 可能为空，表示无限期
			}

			siteHistories = append(siteHistories, history)
		}

		// 如果有时间段记录，则添加到结果中
		if len(siteHistories) > 0 {
			result[siteDomain] = siteHistories
			r.log.WithContext(ctx).Infof("Site %s has %d filter time ranges", siteDomain, len(siteHistories))
		}
	}

	r.log.WithContext(ctx).Infof("Processed %d sites with filter time ranges", len(result))
	return result
}

// getMockDeploymentHistory 生成mock数据用于调试
func (r *deploymentHistoryRepo) getMockDeploymentHistory() map[string][]*biz.SiteDeploymentHistory {
	d := time.Date(2025, 7, 21, 10, 5, 39, 0, time.UTC)
	d2 := time.Date(2025, 7, 26, 10, 5, 39, 0, time.UTC)
	d3 := time.Date(2025, 7, 28, 10, 5, 39, 0, time.UTC)

	result := map[string][]*biz.SiteDeploymentHistory{
		"znepuzzle.com": {
			{
				Domain:               "znepuzzle.com",
				MonetizationStrategy: biz.MonetizationStrategyGameAds, // 只显示游戏内广告
				StartTime:            &d,
				EndTime:              &d2,
			},
			{
				Domain:               "znepuzzle.com",
				MonetizationStrategy: biz.MonetizationStrategyGameAds, // 只显示游戏内广告
				StartTime:            &d3,
				EndTime:              nil, // 无结束时间，表示从开始时间到无限期
			},
		},
		"zte.minigame.com": {
			{
				Domain:               "zte.minigame.com",
				MonetizationStrategy: biz.MonetizationStrategyGameAds, // 只显示游戏内广告
				StartTime:            &d,
				EndTime:              &d2,
			},
		},
	}

	return result
}
