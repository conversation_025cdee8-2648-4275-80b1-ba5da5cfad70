package data

import (
	"fmt"
	"time"

	"github.com/go-kratos/kratos/v2/log"
	"google.golang.org/api/analyticsdata/v1beta"

	"git.minigame.vip/minicloud/service/adsense-bot/internal/biz"
	"git.minigame.vip/minicloud/service/adsense-bot/pkg/cast"
)

// DimensionDataResult 维度数据结果
type DimensionDataResult struct {
	Name        string                            // 请求名称
	Data        map[string][]*biz.ActiveUsersData // 数据映射
	Error       error                             // 错误信息
	Description string                            // 描述信息
	RowCount    int                               // 数据行数
	ProcessTime time.Duration                     // 处理时间
}

// BatchDimensionResult 批量维度数据结果
type BatchDimensionResult struct {
	Results      map[string]*DimensionDataResult // 按名称索引的结果
	TotalRows    int                             // 总行数
	SuccessCount int                             // 成功请求数
	ErrorCount   int                             // 失败请求数
	ProcessTime  time.Duration                   // 总处理时间
}

// NewBatchDimensionResult 创建新的批量结果
func NewBatchDimensionResult() *BatchDimensionResult {
	return &BatchDimensionResult{
		Results: make(map[string]*DimensionDataResult),
	}
}

// AddResult 添加结果
func (br *BatchDimensionResult) AddResult(result *DimensionDataResult) {
	br.Results[result.Name] = result
	br.TotalRows += result.RowCount

	if result.Error != nil {
		br.ErrorCount++
	} else {
		br.SuccessCount++
	}
}

// GetResult 获取指定名称的结果
func (br *BatchDimensionResult) GetResult(name string) (*DimensionDataResult, bool) {
	result, exists := br.Results[name]
	return result, exists
}

// GetData 获取指定名称的数据
func (br *BatchDimensionResult) GetData(name string) (map[string][]*biz.ActiveUsersData, error) {
	result, exists := br.Results[name]
	if !exists {
		return nil, fmt.Errorf("result with name '%s' not found", name)
	}

	if result.Error != nil {
		return nil, fmt.Errorf("result '%s' has error: %w", name, result.Error)
	}

	return result.Data, nil
}

// HasErrors 检查是否有错误
func (br *BatchDimensionResult) HasErrors() bool {
	return br.ErrorCount > 0
}

// GetErrors 获取所有错误
func (br *BatchDimensionResult) GetErrors() map[string]error {
	errors := make(map[string]error)
	for name, result := range br.Results {
		if result.Error != nil {
			errors[name] = result.Error
		}
	}
	return errors
}

// GetSummary 获取结果摘要
func (br *BatchDimensionResult) GetSummary() string {
	return fmt.Sprintf("Total: %d requests, Success: %d, Errors: %d, Rows: %d, Time: %v",
		br.SuccessCount+br.ErrorCount, br.SuccessCount, br.ErrorCount, br.TotalRows, br.ProcessTime)
}

// DimensionDataProcessor 维度数据处理器
type DimensionDataProcessor struct {
	log *log.Helper
}

// NewDimensionDataProcessor 创建新的数据处理器
func NewDimensionDataProcessor(logger *log.Helper) *DimensionDataProcessor {
	return &DimensionDataProcessor{
		log: logger,
	}
}

// ProcessBatchResponse 处理批量响应
func (p *DimensionDataProcessor) ProcessBatchResponse(
	batchResp *analyticsdata.BatchRunReportsResponse,
	configs []DimensionRequestConfig,
) *BatchDimensionResult {
	startTime := time.Now()
	result := NewBatchDimensionResult()

	// 验证响应数量
	if len(batchResp.Reports) != len(configs) {
		for i, config := range configs {
			var err error
			if i >= len(batchResp.Reports) {
				err = fmt.Errorf("missing report for request '%s'", config.Name)
			}

			result.AddResult(&DimensionDataResult{
				Name:        config.Name,
				Data:        make(map[string][]*biz.ActiveUsersData),
				Error:       err,
				Description: config.Description,
				RowCount:    0,
			})
		}
		result.ProcessTime = time.Since(startTime)
		return result
	}

	// 处理每个报告
	for i, config := range configs {
		reportResult := p.processReport(batchResp.Reports[i], config)
		result.AddResult(reportResult)
	}

	result.ProcessTime = time.Since(startTime)
	return result
}

// processReport 处理单个报告
func (p *DimensionDataProcessor) processReport(
	report *analyticsdata.RunReportResponse,
	config DimensionRequestConfig,
) *DimensionDataResult {
	startTime := time.Now()
	dataMap := make(map[string][]*biz.ActiveUsersData)
	rowCount := 0

	for _, row := range report.Rows {
		// 验证数据完整性
		if len(row.DimensionValues) < 3 || len(row.MetricValues) < 1 {
			p.log.Warnf("Incomplete data row for '%s': dimensions=%d, metrics=%d",
				config.Name, len(row.DimensionValues), len(row.MetricValues))
			continue
		}

		// 生成键
		key := generateSiteDateKey(row.DimensionValues[0].Value, row.DimensionValues[1].Value)

		// 初始化数组
		if dataMap[key] == nil {
			dataMap[key] = make([]*biz.ActiveUsersData, 0)
		}

		// 添加数据
		dataMap[key] = append(dataMap[key], &biz.ActiveUsersData{
			Name:        row.DimensionValues[2].Value,
			ActiveUsers: cast.StringToInt(row.MetricValues[0].Value),
		})

		rowCount++
	}

	return &DimensionDataResult{
		Name:        config.Name,
		Data:        dataMap,
		Error:       nil,
		Description: config.Description,
		RowCount:    rowCount,
		ProcessTime: time.Since(startTime),
	}
}

// ValidateDataIntegrity 验证数据完整性
func (p *DimensionDataProcessor) ValidateDataIntegrity(result *BatchDimensionResult, expectedRequests []string) error {
	for _, expected := range expectedRequests {
		if _, exists := result.Results[expected]; !exists {
			return fmt.Errorf("missing expected result: %s", expected)
		}
	}

	if result.HasErrors() {
		errors := result.GetErrors()
		return fmt.Errorf("batch processing has errors: %v", errors)
	}

	return nil
}
