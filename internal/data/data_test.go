package data

import (
	"testing"

	"git.minigame.vip/minicloud/service/adsense-bot/internal/data/ent/urlchannelcountryadformatinfopartition"
)

func Test_decodeSource(t *testing.T) {
	tests := []struct {
		name string // description of this test case
		// Named input parameters for target function.
		value string
		want  string
		want2 string
		want3 string
		want4 string
		want5 string
	}{
		{
			name:  "normal case",
			value: "host=localhost user=postgres password=123456 dbname=minigameData port=5432 sslmode=disable TimeZone=UTC",
			want:  "postgres",
			want2: "localhost",
			want3: "5432",
			want4: "minigameData",
			want5: "123456",
		},
		{
			name:  "different order",
			value: "password=testpass port=1234 host=testhost dbname=testdb user=testuser",
			want:  "testuser",
			want2: "testhost",
			want3: "1234",
			want4: "testdb",
			want5: "testpass",
		},
		{
			name:  "missing optional params",
			value: "host=127.0.0.1 user=admin password=adminpass dbname=mydb port=5432",
			want:  "admin",
			want2: "127.0.0.1",
			want3: "5432",
			want4: "mydb",
			want5: "adminpass",
		},
		{
			name:  "with extra spaces",
			value: "host=remote.db   user=dbuser    password=pass123   dbname=prod    port=3306",
			want:  "dbuser",
			want2: "remote.db",
			want3: "3306",
			want4: "prod",
			want5: "pass123",
		},
	}
	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			got, got2, got3, got4, got5 := decodeSource(tt.value)
			// TODO: update the condition below to compare got with tt.want.
			if got != tt.want {
				t.Errorf("decodeSource() = %v, want %v", got, tt.want)
			}
			if got2 != tt.want2 {
				t.Errorf("decodeSource() = %v, want %v", got2, tt.want2)
			}
			if got3 != tt.want3 {
				t.Errorf("decodeSource() = %v, want %v", got3, tt.want3)
			}
			if got4 != tt.want4 {
				t.Errorf("decodeSource() = %v, want %v", got4, tt.want4)
			}
			if got5 != tt.want5 {
				t.Errorf("decodeSource() = %v, want %v", got5, tt.want5)
			}
		})
	}
}

func Test_isPartitionTable(t *testing.T) {
	type args struct {
		name string
	}
	tests := []struct {
		name string
		args args
		want bool
	}{
		{
			name: "",
			args: args{
				name: "",
			},
			want: false,
		},
		{
			name: "",
			args: args{
				name: urlchannelcountryadformatinfopartition.Table,
			},
			want: true,
		},
	}
	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			if got := isPartitionTable(tt.args.name); got != tt.want {
				t.Errorf("isPartitionTable() = %v, want %v", got, tt.want)
			}
		})
	}
}
