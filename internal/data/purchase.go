package data

import (
	"bytes"
	"context"
	"encoding/json"
	"fmt"
	"io"
	"net/http"
	"net/url"
	"strconv"
	"time"

	"github.com/go-kratos/kratos/v2/log"

	"git.minigame.vip/minicloud/service/adsense-bot/internal/biz"
	"git.minigame.vip/minicloud/service/adsense-bot/internal/conf"
	"git.minigame.vip/minicloud/service/adsense-bot/internal/data/ent"
)

type gamePurchaseRepo struct {
	data       *Data
	log        *log.Helper
	httpClient *http.Client
	url        string
}

// NewPurchaseRepo 创建游戏购买数据仓库
func NewPurchaseRepo(data *Data, conf *conf.ExternalPurchaseAPI, logger log.Logger) biz.GamePurchaseRepo {
	// 设置HTTP客户端超时时间
	timeout := 30 * time.Second
	if conf.Timeout != nil {
		timeout = conf.Timeout.AsDuration()
	}

	httpClient := &http.Client{
		Timeout: timeout,
	}

	return &gamePurchaseRepo{
		data:       data,
		log:        log.NewHelper(log.With(logger, "module", "data/purchase")),
		httpClient: httpClient,
		url:        conf.Url,
	}
}

// Create 批量创建游戏购买记录
func (r *gamePurchaseRepo) Create(ctx context.Context, purchases []*biz.GamePurchaseData) error {
	if len(purchases) == 0 {
		return nil
	}

	// 开启事务
	tx, err := r.data.db.Tx(ctx)
	if err != nil {
		r.log.WithContext(ctx).Errorf("开启事务失败: %v", err)
		return err
	}

	err = r.delete(ctx, tx)
	if err != nil {
		return rollbackTx(tx, err)
	}

	const batchSize = 4000
	bulk := make([]*ent.GamePurchaseCreate, 0, batchSize)

	for i, purchase := range purchases {
		create := tx.GamePurchase.Create().
			SetUserID(purchase.UserID).
			SetProductOrderID(int(purchase.ProductOrderID)).
			SetAppID(purchase.AppID).
			SetPayload(purchase.Payload).
			SetCurrency(purchase.Currency).
			SetAmount(int(purchase.Amount)).
			SetBot(purchase.Bot).
			SetPayURL(purchase.PayURL).
			SetStatus(purchase.Status).
			SetPayChannelTid(purchase.PayChannelTid).
			SetPayMethodTid(purchase.PayMethodTid).
			SetPlatform(purchase.Platform).
			SetDomain(purchase.Domain).
			SetPayChannel(purchase.PayChannel).
			SetPayMethod(purchase.PayMethod).
			SetPayCountry(purchase.PayCountry).
			SetPayoutAmount(int(purchase.PayoutAmount)).
			SetCheckoutAmount(int(purchase.CheckoutAmount)).SetProductTitle(purchase.ProductTitle).
			SetProductDescription(purchase.ProductDescription).
			SetProductID(purchase.ProductID).
			SetProductAmount(int(purchase.ProductAmount)).
			SetCreateAt(purchase.CreateTime).
			SetUpdateAt(purchase.UpdateTime)

		bulk = append(bulk, create)

		if (i+1)%batchSize == 0 || i == len(purchases)-1 {
			err := r.executeBatch(ctx, tx, bulk)
			if err != nil {
				return rollbackTx(tx, err)
			}
			bulk = bulk[:0] // 清空切片
		}
	}

	if err := tx.Commit(); err != nil {
		r.log.WithContext(ctx).Errorf("提交事务失败: %v", err)
		return err
	}

	return nil
}

// executeBatch 执行批量插入
func (r *gamePurchaseRepo) executeBatch(ctx context.Context, tx *ent.Tx, bulk []*ent.GamePurchaseCreate) error {
	if len(bulk) == 0 {
		return nil
	}

	err := tx.GamePurchase.CreateBulk(bulk...).
		Exec(ctx)

	if err != nil {
		r.log.WithContext(ctx).Errorf("批量插入失败: %v", err)
		return err
	}

	return nil
}

// ExternalPurchaseRequest 外部API请求体结构
type ExternalPurchaseRequest struct {
	StartTime string `json:"startTime"`
	EndTime   string `json:"endTime"`
	Page      int    `json:"page"`
}

// ExternalPurchaseItem 外部API返回的购买数据项
type ExternalPurchaseItem struct {
	ID                 int     `json:"id"`
	Amount             string  `json:"amount"`
	Bot                string  `json:"bot"`
	CreateAt           string  `json:"createAt"`
	Currency           string  `json:"currency"`
	Domain             string  `json:"domain"`
	GameAppid          string  `json:"gameAppid"`
	PayChannel         string  `json:"payChannel"`
	PayChannelTid      string  `json:"payChannelTid"`
	PayCountry         string  `json:"payCountry"`
	CheckoutAmount     int     `json:"checkoutAmount"`
	PayMethod          string  `json:"payMethod"`
	PayMethodTid       string  `json:"payMethodTid"`
	PayUrl             string  `json:"payUrl"`
	Payload            string  `json:"payload"`
	PayoutAmount       float64 `json:"payoutAmount"`
	Platform           string  `json:"platform"`
	ProductAmount      int     `json:"productAmount"`
	ProductDescription string  `json:"productDescription"`
	ProductId          string  `json:"productId"`
	ProductOrderId     int     `json:"productOrderId"`
	ProductTitle       string  `json:"productTitle"`
	Status             string  `json:"status"`
	UpdateAt           string  `json:"updateAt"`
	UserId             string  `json:"userId"`
}

// ExternalPurchaseData 外部API响应的data部分
type ExternalPurchaseData struct {
	List  []*ExternalPurchaseItem `json:"list"`
	Total int                     `json:"total"`
}

// ExternalPurchaseResponse 外部API响应结构
type ExternalPurchaseResponse struct {
	Code    int                   `json:"code"`
	Message string                `json:"message"`
	Data    *ExternalPurchaseData `json:"data"`
	Ts      int64                 `json:"ts"`
	Tid     string                `json:"tid"`
}

// ExternalPurchaseSummaryRequest paySummary API请求体结构
type ExternalPurchaseSummaryRequest struct {
	Page int `json:"page"`
}

// ExternalPurchaseSummaryItem paySummary API返回的汇总数据项
type ExternalPurchaseSummaryItem struct {
	ID           int    `json:"id"`
	CreateAt     string `json:"createAt"`
	Date         string `json:"date"`
	Domain       string `json:"domain"`
	GameAppid    string `json:"gameAppid"`
	PayAmount    int    `json:"payAmount"`
	PayTimes     int    `json:"payTimes"`
	PayUsers     int    `json:"payUsers"`
	PayoutAmount int    `json:"payoutAmount"`
	UpdateAt     string `json:"updateAt"`
}

// ExternalPurchaseSummaryData paySummary API响应的data部分
type ExternalPurchaseSummaryData struct {
	List  []*ExternalPurchaseSummaryItem `json:"list"`
	Total int                            `json:"total"`
}

// ExternalPurchaseSummaryResponse paySummary API响应结构
type ExternalPurchaseSummaryResponse struct {
	Code    int                          `json:"code"`
	Message string                       `json:"message"`
	Data    *ExternalPurchaseSummaryData `json:"data"`
	Ts      int64                        `json:"ts"`
	Tid     string                       `json:"tid"`
}

// FetchFromExternalAPI 从外部API获取购买数据
func (r *gamePurchaseRepo) FetchFromExternalAPI(ctx context.Context, startTime, endTime int64) ([]*biz.GamePurchaseData, error) {

	var allPurchases []*biz.GamePurchaseData
	page := 1
	const maxPageSize = 500 // 每页最多500条数据

	for {
		// 构建请求URL
		apiURL, err := url.Parse(r.url + "/v1/televssvc/api/reporting/pay")
		if err != nil {
			r.log.WithContext(ctx).Errorf("解析API URL失败: %v", err)
			return nil, fmt.Errorf("解析API URL失败: %w", err)
		}

		// 构建请求体
		requestBody := ExternalPurchaseRequest{
			StartTime: strconv.FormatInt(startTime, 10),
			EndTime:   strconv.FormatInt(endTime, 10),
			Page:      page,
		}

		// 序列化请求体为JSON
		jsonData, err := json.Marshal(requestBody)
		if err != nil {
			r.log.WithContext(ctx).Errorf("序列化请求体失败: %v", err)
			return nil, fmt.Errorf("序列化请求体失败: %w", err)
		}

		r.log.WithContext(ctx).Infof("请求外部API: %s, 请求体: %s", apiURL.String(), string(jsonData))

		// 创建HTTP POST请求
		req, err := http.NewRequestWithContext(ctx, "POST", apiURL.String(), bytes.NewBuffer(jsonData))
		if err != nil {
			r.log.WithContext(ctx).Errorf("创建HTTP请求失败: %v", err)
			return nil, fmt.Errorf("创建HTTP请求失败: %w", err)
		}

		// 设置请求头
		req.Header.Set("Content-Type", "application/json")
		req.Header.Set("Accept", "application/json")

		// 发送请求
		resp, err := r.httpClient.Do(req)
		if err != nil {
			r.log.WithContext(ctx).Errorf("调用外部API失败: %v", err)
			return nil, fmt.Errorf("调用外部API失败: %w", err)
		}

		// 检查响应状态码
		if resp.StatusCode != http.StatusOK {
			resp.Body.Close()
			r.log.WithContext(ctx).Errorf("外部API返回错误状态码: %d", resp.StatusCode)
			return nil, fmt.Errorf("外部API返回错误状态码: %d", resp.StatusCode)
		}

		// 读取响应体
		body, err := io.ReadAll(resp.Body)
		resp.Body.Close()
		if err != nil {
			r.log.WithContext(ctx).Errorf("读取响应体失败: %v", err)
			return nil, fmt.Errorf("读取响应体失败: %w", err)
		}

		// 解析JSON响应
		var apiResponse ExternalPurchaseResponse
		if err := json.Unmarshal(body, &apiResponse); err != nil {
			r.log.WithContext(ctx).Errorf("解析JSON响应失败: %v", err)
			return nil, fmt.Errorf("解析JSON响应失败: %w", err)
		}

		// 检查API响应状态
		if apiResponse.Code != 200 {
			r.log.WithContext(ctx).Errorf("外部API返回错误: %s", apiResponse.Message)
			return nil, fmt.Errorf("外部API返回错误: %s", apiResponse.Message)
		}

		// 检查数据是否为空
		if apiResponse.Data == nil || len(apiResponse.Data.List) == 0 {
			r.log.WithContext(ctx).Infof("第%d页没有更多数据，结束分页查询", page)
			break
		}

		// 转换数据格式
		pagePurchases, err := r.convertExternalDataToBizData(apiResponse.Data.List)
		if err != nil {
			r.log.WithContext(ctx).Errorf("转换外部数据格式失败: %v", err)
			return nil, fmt.Errorf("转换外部数据格式失败: %w", err)
		}

		allPurchases = append(allPurchases, pagePurchases...)

		r.log.WithContext(ctx).Infof("第%d页获取到%d条数据，总计%d条，API总数: %d",
			page, len(apiResponse.Data.List), len(allPurchases), apiResponse.Data.Total)

		// 检查是否还有更多页面
		// 如果当前页数据少于最大页面大小，说明已经是最后一页
		if len(apiResponse.Data.List) < maxPageSize {
			r.log.WithContext(ctx).Infof("当前页数据量(%d)小于最大页面大小(%d)，结束分页查询",
				len(apiResponse.Data.List), maxPageSize)
			break
		}

		// 检查是否已经获取了所有数据
		if len(allPurchases) >= apiResponse.Data.Total {
			r.log.WithContext(ctx).Infof("已获取所有数据(%d/%d)，结束分页查询",
				len(allPurchases), apiResponse.Data.Total)
			break
		}

		page++
	}

	return allPurchases, nil
}

// convertExternalDataToBizData 将外部API数据转换为业务数据格式
func (r *gamePurchaseRepo) convertExternalDataToBizData(items []*ExternalPurchaseItem) ([]*biz.GamePurchaseData, error) {
	var purchases []*biz.GamePurchaseData

	for _, item := range items {
		// 转换金额（从字符串转为整数，单位为美分）
		amount, err := strconv.ParseInt(item.Amount, 10, 64)
		if err != nil {
			r.log.Errorf("转换金额失败: %v, amount: %s", err, item.Amount)
			return nil, err
		}
		createAtSec, err := strconv.ParseInt(item.CreateAt, 10, 64)
		if err != nil {
			r.log.Errorf("转换创建时间失败: %v, createAt: %s", err, item.CreateAt)
			return nil, err
		}
		updateAtSec, err := strconv.ParseInt(item.UpdateAt, 10, 64)
		if err != nil {
			r.log.Errorf("转换更新时间失败: %v, updateAt: %s", err, item.UpdateAt)
			return nil, err
		}

		purchase := &biz.GamePurchaseData{
			UserID:             item.UserId,
			ProductOrderID:     int32(item.ProductOrderId),
			AppID:              item.GameAppid,
			Payload:            item.Payload,
			Currency:           item.Currency,
			Amount:             int32(amount),
			Bot:                item.Bot,
			PayURL:             item.PayUrl,
			Status:             item.Status,
			PayChannelTid:      item.PayChannelTid,
			PayMethodTid:       item.PayMethodTid,
			Platform:           item.Platform,
			Domain:             item.Domain,
			PayChannel:         item.PayChannel,
			PayMethod:          item.PayMethod,
			PayCountry:         item.PayCountry,
			PayoutAmount:       int32(item.PayoutAmount),
			CheckoutAmount:     int32(item.CheckoutAmount),
			ProductAmount:      int32(item.ProductAmount),
			ProductTitle:       item.ProductTitle,
			ProductDescription: item.ProductDescription,
			ProductID:          item.ProductId,
			CreateTime:         time.Unix(createAtSec, 0),
			UpdateTime:         time.Unix(updateAtSec, 0),
		}

		purchases = append(purchases, purchase)
	}

	return purchases, nil
}

func (r *gamePurchaseRepo) delete(ctx context.Context, tx *ent.Tx) error {
	_, err := tx.GamePurchase.Delete().Exec(ctx)
	if err != nil {
		r.log.WithContext(ctx).Errorf("删除内购数据失败: %v", err)
		return err
	}
	return nil
}

// FetchAllSummariesFromExternalAPI 从外部API获取所有内购汇总数据（包含分页逻辑）
func (r *gamePurchaseRepo) FetchAllSummariesFromExternalAPI(ctx context.Context) ([]*biz.GamePurchaseSummaryData, error) {
	var allSummaries []*biz.GamePurchaseSummaryData
	page := 1
	const maxPageSize = 500 // 每页最多500条数据

	for {
		// 获取单页数据
		summaries, err := r.fetchSummaryFromExternalAPI(ctx, page)
		if err != nil {
			return nil, err
		}

		if len(summaries) == 0 {
			r.log.WithContext(ctx).Infof("第%d页没有更多汇总数据，结束分页查询", page)
			break
		}

		allSummaries = append(allSummaries, summaries...)
		r.log.WithContext(ctx).Infof("第%d页获取到%d条汇总数据，总计%d条", page, len(summaries), len(allSummaries))

		// 如果当前页数据少于最大页面大小，说明已经是最后一页
		if len(summaries) < maxPageSize {
			r.log.WithContext(ctx).Infof("当前页数据量(%d)小于最大页面大小(%d)，结束分页查询", len(summaries), maxPageSize)
			break
		}

		page++
	}

	return allSummaries, nil
}

// fetchSummaryFromExternalAPI 从外部API获取单页内购汇总数据
func (r *gamePurchaseRepo) fetchSummaryFromExternalAPI(ctx context.Context, page int) ([]*biz.GamePurchaseSummaryData, error) {
	// 构建请求URL
	apiURL, err := url.Parse(r.url + "/v1/televssvc/api/reporting/paySummary")
	if err != nil {
		r.log.WithContext(ctx).Errorf("解析paySummary API URL失败: %v", err)
		return nil, fmt.Errorf("解析paySummary API URL失败: %w", err)
	}

	// 构建请求体
	requestBody := ExternalPurchaseSummaryRequest{
		Page: page,
	}

	// 序列化请求体为JSON
	jsonData, err := json.Marshal(requestBody)
	if err != nil {
		r.log.WithContext(ctx).Errorf("序列化paySummary请求体失败: %v", err)
		return nil, fmt.Errorf("序列化paySummary请求体失败: %w", err)
	}

	r.log.WithContext(ctx).Infof("请求paySummary API: %s, 请求体: %s", apiURL.String(), string(jsonData))

	// 创建HTTP POST请求
	req, err := http.NewRequestWithContext(ctx, "POST", apiURL.String(), bytes.NewBuffer(jsonData))
	if err != nil {
		r.log.WithContext(ctx).Errorf("创建paySummary HTTP请求失败: %v", err)
		return nil, fmt.Errorf("创建paySummary HTTP请求失败: %w", err)
	}

	// 设置请求头
	req.Header.Set("Content-Type", "application/json")
	req.Header.Set("Accept", "application/json")

	// 发送请求
	resp, err := r.httpClient.Do(req)
	if err != nil {
		r.log.WithContext(ctx).Errorf("调用paySummary API失败: %v", err)
		return nil, fmt.Errorf("调用paySummary API失败: %w", err)
	}

	// 检查响应状态码
	if resp.StatusCode != http.StatusOK {
		resp.Body.Close()
		r.log.WithContext(ctx).Errorf("paySummary API返回错误状态码: %d", resp.StatusCode)
		return nil, fmt.Errorf("paySummary API返回错误状态码: %d", resp.StatusCode)
	}

	// 读取响应体
	body, err := io.ReadAll(resp.Body)
	resp.Body.Close()
	if err != nil {
		r.log.WithContext(ctx).Errorf("读取paySummary响应体失败: %v", err)
		return nil, fmt.Errorf("读取paySummary响应体失败: %w", err)
	}

	// 解析JSON响应
	var apiResponse ExternalPurchaseSummaryResponse
	if err := json.Unmarshal(body, &apiResponse); err != nil {
		r.log.WithContext(ctx).Errorf("解析paySummary JSON响应失败: %v", err)
		return nil, fmt.Errorf("解析paySummary JSON响应失败: %w", err)
	}

	// 检查API响应状态
	if apiResponse.Code != 200 {
		r.log.WithContext(ctx).Errorf("paySummary API返回错误: %s", apiResponse.Message)
		return nil, fmt.Errorf("paySummary API返回错误: %s", apiResponse.Message)
	}

	// 检查数据是否为空
	if apiResponse.Data == nil || len(apiResponse.Data.List) == 0 {
		r.log.WithContext(ctx).Infof("paySummary API第%d页没有数据", page)
		return []*biz.GamePurchaseSummaryData{}, nil
	}

	// 转换数据格式
	summaries, err := r.convertExternalSummaryDataToBizData(apiResponse.Data.List)
	if err != nil {
		r.log.WithContext(ctx).Errorf("转换paySummary外部数据格式失败: %v", err)
		return nil, fmt.Errorf("转换paySummary外部数据格式失败: %w", err)
	}

	r.log.WithContext(ctx).Infof("paySummary API第%d页获取到%d条汇总数据，API总数: %d",
		page, len(apiResponse.Data.List), apiResponse.Data.Total)

	return summaries, nil
}

// convertExternalSummaryDataToBizData 将外部paySummary API数据转换为业务数据格式
func (r *gamePurchaseRepo) convertExternalSummaryDataToBizData(items []*ExternalPurchaseSummaryItem) ([]*biz.GamePurchaseSummaryData, error) {
	var summaries []*biz.GamePurchaseSummaryData

	for _, item := range items {
		// 转换时间戳
		createAtSec, err := strconv.ParseInt(item.CreateAt, 10, 64)
		if err != nil {
			r.log.Errorf("解析createAt时间戳失败: %v, 原始值: %s", err, item.CreateAt)
			return nil, fmt.Errorf("解析createAt时间戳失败: %w", err)
		}

		updateAtSec, err := strconv.ParseInt(item.UpdateAt, 10, 64)
		if err != nil {
			r.log.Errorf("解析updateAt时间戳失败: %v, 原始值: %s", err, item.UpdateAt)
			return nil, fmt.Errorf("解析updateAt时间戳失败: %w", err)
		}
		date, err := time.Parse("2006-01-02", item.Date)
		if err != nil {
			r.log.Errorf("解析日期失败: %v, 原始值: %s", err, item.Date)
			return nil, fmt.Errorf("解析日期失败: %w", err)
		}

		summary := &biz.GamePurchaseSummaryData{
			Date:         date,
			Domain:       item.Domain,
			GameAppid:    item.GameAppid,
			PayAmount:    item.PayAmount,
			PayTimes:     item.PayTimes,
			PayUsers:     item.PayUsers,
			PayoutAmount: item.PayoutAmount,
			CreateTime:   time.Unix(createAtSec, 0),
			UpdateTime:   time.Unix(updateAtSec, 0),
		}

		summaries = append(summaries, summary)
	}

	return summaries, nil
}
