package data

import (
	"context"

	"entgo.io/ent/dialect/sql"
	"github.com/go-kratos/kratos/v2/log"

	"git.minigame.vip/minicloud/service/adsense-bot/internal/biz"
	"git.minigame.vip/minicloud/service/adsense-bot/internal/data/ent"
	"git.minigame.vip/minicloud/service/adsense-bot/internal/data/ent/gamecountrypartition"
	"git.minigame.vip/minicloud/service/adsense-bot/internal/data/ent/productinfo"
)

const maxCountryCount = 5

type gameCountryRepo struct {
	data *Data
	log  *log.Helper
}

func (r *gameCountryRepo) CreateHKD(ctx context.Context, datas []*biz.AdsenseChannelCountryData) error {
	bulk := make([]*ent.GameCountryHKDPartitionCreate, 0)
	tx, err := r.data.db.Tx(ctx)
	if err != nil {
		return err
	}
	for i, data := range datas {
		bulk = append(bulk, tx.GameCountryHKDPartition.Create().SetDate(data.Date).SetChannel(data.Channel).SetEstimatedEarnings(data.EstimatedEarnings).
			SetPageViews(data.PageViews).SetPageViewsRpm(data.PageViewsRpm).SetChannelID(data.ChannelID).
			SetImpressions(data.IMPRESSIONS).SetImpressionsRpm(data.ImpressionsRpm).
			SetAdRequestsCoverage(data.AdRequestsCoverage).SetClicks(data.CLICKS).
			SetAdRequests(data.AdRequests).SetImpressionsCtr(data.ImpressionsCtr).
			SetActiveViewViewability(data.ActiveViewViewability).
			SetMatchedAdRequests(data.MatchedAdRequests).SetCountry(data.Country).
			SetCostPerClick(data.CostPerClick))
		if (i+1)%4000 == 0 || i == len(datas)-1 {
			err := tx.GameCountryHKDPartition.CreateBulk(bulk...).OnConflict(sql.ConflictColumns("date", "channel", "channel_id", "country")).UpdateNewValues().Exec(ctx)
			// _, err := tx.Channel.createBulk(bulk...).Save(ctx)
			if err != nil {
				return rollback(tx, err)
			}
			bulk = bulk[:0]
		}
	}

	return tx.Commit()
}

func (r *gameCountryRepo) GetAppCountrySummary(ctx context.Context) ([]*biz.AppCountrySummary, error) {
	type gameCountry struct {
		AppID   string `json:"app_id"`
		Country string `json:"country"`
	}
	var v []gameCountry
	err := r.data.db.GameCountryPartition.Query().Modify(func(s *sql.Selector) {
		t1 := sql.Table(productinfo.Table)
		t2 := sql.Table(gamecountrypartition.Table)
		t := sql.Select(productinfo.FieldAppID, gamecountrypartition.FieldCountry).AppendSelectExprAs(sql.RowNumber().PartitionBy(productinfo.FieldAppID).
			OrderBy(sql.Desc(sql.Sum(gamecountrypartition.FieldPageViews))), "row_number").
			GroupBy(productinfo.FieldAppID, gamecountrypartition.FieldCountry).From(t2).Join(t1).On(t1.C(productinfo.FieldDataAdChannelName), t2.C(gamecountrypartition.FieldChannel)).
			Where(sql.EQ(productinfo.FieldState, 2)).
			As("t")
		s.Select(productinfo.FieldAppID, gamecountrypartition.FieldCountry).From(t).Where(sql.LTE("row_number", maxCountryCount))
	}).Scan(ctx, &v)
	if err != nil {
		return nil, err
	}
	m := make([]*biz.AppCountrySummary, 0)
	for _, v := range v {
		m = append(m, &biz.AppCountrySummary{
			AppID:   v.AppID,
			Country: v.Country,
		})
	}
	return m, nil
}

func (r *gameCountryRepo) Create(ctx context.Context, datas []*biz.AdsenseChannelCountryData) error {
	bulk := make([]*ent.GameCountryPartitionCreate, 0)
	tx, err := r.data.db.Tx(ctx)
	if err != nil {
		return err
	}
	for i, data := range datas {
		bulk = append(bulk, tx.GameCountryPartition.Create().SetDate(data.Date).SetChannel(data.Channel).SetEstimatedEarnings(data.EstimatedEarnings).
			SetPageViews(data.PageViews).SetPageViewsRpm(data.PageViewsRpm).SetChannelID(data.ChannelID).
			SetImpressions(data.IMPRESSIONS).SetImpressionsRpm(data.ImpressionsRpm).
			SetAdRequestsCoverage(data.AdRequestsCoverage).SetClicks(data.CLICKS).
			SetAdRequests(data.AdRequests).SetImpressionsCtr(data.ImpressionsCtr).
			SetActiveViewViewability(data.ActiveViewViewability).
			SetMatchedAdRequests(data.MatchedAdRequests).SetCountry(data.Country).
			SetCostPerClick(data.CostPerClick))
		if (i+1)%4000 == 0 || i == len(datas)-1 {
			err := tx.GameCountryPartition.CreateBulk(bulk...).OnConflict(sql.ConflictColumns("date", "channel", "channel_id", "country")).UpdateNewValues().Exec(ctx)
			// _, err := tx.Channel.createBulk(bulk...).Save(ctx)
			if err != nil {
				return rollback(tx, err)
			}
			bulk = bulk[:0]
		}
	}

	return tx.Commit()
}

func NewGameCountryRepo(data *Data, logger log.Logger) biz.AdSenseGameCountryRepo {
	return &gameCountryRepo{
		data,
		log.NewHelper(log.With(logger, "module", "data/game")),
	}
}
