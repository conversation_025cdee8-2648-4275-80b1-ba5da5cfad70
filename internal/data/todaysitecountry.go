package data

import (
	"context"
	"time"

	"github.com/go-kratos/kratos/v2/log"

	"git.minigame.vip/minicloud/service/adsense-bot/internal/biz"
	"git.minigame.vip/minicloud/service/adsense-bot/internal/data/ent"
	"git.minigame.vip/minicloud/service/adsense-bot/internal/data/ent/todaysitecountry"
)

type todaySiteCountryRepo struct {
	data *Data
	log  *log.Helper
}

func (r *todaySiteCountryRepo) Create(ctx context.Context, data *biz.AdsenseSiteCountryTodayData) error {
	cstSh, _ := time.LoadLocation("Asia/Shanghai")
	_, err := r.data.db.TodaySiteCountry.Create().SetDate(data.Date).SetSite(data.Site).
		SetCountry(data.Country).SetEstimatedEarnings(data.EstimatedEarnings).
		SetPageViews(data.PageViews).SetPageViewsRpm(data.PageViewsRpm).
		SetImpressions(data.IMPRESSIONS).SetImpressionsRpm(data.ImpressionsRpm).
		SetAdRequestsCoverage(data.AdRequestsCoverage).SetClicks(data.CLICKS).
		SetAdRequests(data.AdRequests).SetImpressionsCtr(data.ImpressionsCtr).
		SetActiveViewViewability(data.ActiveViewViewability).
		SetMatchedAdRequests(data.MatchedAdRequests).
		SetCostPerClick(data.CostPerClick).Save(ctx)
	if err != nil {
		return err
	}
	if data.Date.Hour() > 1 {
		lastHourData, err := r.data.db.TodaySiteCountry.Query().Where(
			todaysitecountry.Site(data.Site),
			todaysitecountry.Country(data.Country),
			todaysitecountry.DateLT(data.Date),
			todaysitecountry.DateGTE(time.Date(data.Date.Year(), data.Date.Month(), data.Date.Day(), data.Date.Hour()-1, 0, 0, 0, cstSh))).Only(ctx)
		if err != nil {
			if ent.IsNotFound(err) {
				_, err = r.data.db.HourSiteCountry.Create().SetDate(time.Date(data.Date.Year(), data.Date.Month(), data.Date.Day(), data.Date.Hour()-1, 0, 0, 0, cstSh)).SetSite(data.Site).SetCountry(data.Country).
					SetEstimatedEarnings(data.EstimatedEarnings).
					SetPageViews(data.PageViews).SetPageViewsRpm(data.PageViewsRpm).
					SetImpressions(data.IMPRESSIONS).SetImpressionsRpm(data.ImpressionsRpm).
					SetAdRequestsCoverage(data.AdRequestsCoverage).SetClicks(data.CLICKS).
					SetAdRequests(data.AdRequests).SetImpressionsCtr(data.ImpressionsCtr).
					SetActiveViewViewability(data.ActiveViewViewability).
					SetMatchedAdRequests(data.MatchedAdRequests).
					SetCostPerClick(data.CostPerClick).Save(ctx)
				if err != nil {
					return err
				}
				return nil
			}
			return err
		}
		PageViewsRpm := 0.0
		if data.PageViews-lastHourData.PageViews > 0 {
			PageViewsRpm = (data.EstimatedEarnings - lastHourData.EstimatedEarnings) * 1000.0 / float64(data.PageViews-lastHourData.PageViews)
		}
		ImpressionsRpm := 0.0
		if data.IMPRESSIONS-lastHourData.Impressions > 0 {
			ImpressionsRpm = (data.EstimatedEarnings - lastHourData.EstimatedEarnings) * 1000 / float64(data.IMPRESSIONS-lastHourData.Impressions)
		}
		ImpressionsCtr := 0.0
		if data.IMPRESSIONS-lastHourData.Impressions > 0 {
			ImpressionsCtr = float64(data.CLICKS-lastHourData.Clicks) / float64(data.IMPRESSIONS-lastHourData.Impressions)
		}
		AdRequestsCoverage := 0.0
		if data.AdRequests-lastHourData.AdRequests > 0 {
			AdRequestsCoverage = float64(data.MatchedAdRequests-lastHourData.MatchedAdRequests) / float64(data.AdRequests-lastHourData.AdRequests)
		}
		CostPerClick := 0.0
		if data.CLICKS-lastHourData.Clicks > 0 {
			CostPerClick = (data.EstimatedEarnings - lastHourData.EstimatedEarnings) / float64(data.CLICKS-lastHourData.Clicks)
		}
		_, err = r.data.db.HourSiteCountry.Create().SetDate(time.Date(data.Date.Year(), data.Date.Month(), data.Date.Day(), data.Date.Hour()-1, 0, 0, 0, cstSh)).SetSite(data.Site).SetCountry(data.Country).
			SetEstimatedEarnings(data.EstimatedEarnings - lastHourData.EstimatedEarnings).
			SetPageViews(data.PageViews - lastHourData.PageViews).SetPageViewsRpm(PageViewsRpm).
			SetImpressions(data.IMPRESSIONS - lastHourData.Impressions).SetImpressionsRpm(ImpressionsRpm).
			SetAdRequestsCoverage(AdRequestsCoverage).SetClicks(data.CLICKS - lastHourData.Clicks).
			SetAdRequests(data.AdRequests - lastHourData.AdRequests).SetImpressionsCtr(ImpressionsCtr).
			SetActiveViewViewability(data.ActiveViewViewability).
			SetMatchedAdRequests(data.MatchedAdRequests - lastHourData.MatchedAdRequests).
			SetCostPerClick(CostPerClick).Save(ctx)
		if err != nil {
			return err
		}
	} else if data.Date.Hour() == 0 {
		lastHourData, err := r.data.db.TodaySiteCountry.Query().Where(
			todaysitecountry.Site(data.Site),
			todaysitecountry.Country(data.Country),
			todaysitecountry.DateLT(time.Date(data.Date.Year(), data.Date.Month(), data.Date.Day(), 0, 0, 0, 0, cstSh)),
			todaysitecountry.DateGTE(time.Date(data.Date.Year(), data.Date.Month(), data.Date.Day()-1, 23, 0, 0, 0, cstSh))).Only(ctx)
		if err != nil {
			if ent.IsNotFound(err) {
				_, err = r.data.db.HourSiteCountry.Create().SetDate(time.Date(data.Date.Year(), data.Date.Month(), data.Date.Day()-1, 23, 00, 0, 0, cstSh)).SetSite(data.Site).SetCountry(data.Country).
					SetEstimatedEarnings(data.EstimatedEarnings).
					SetPageViews(data.PageViews).SetPageViewsRpm(data.PageViewsRpm).
					SetImpressions(data.IMPRESSIONS).SetImpressionsRpm(data.ImpressionsRpm).
					SetAdRequestsCoverage(data.AdRequestsCoverage).SetClicks(data.CLICKS).
					SetAdRequests(data.AdRequests).SetImpressionsCtr(data.ImpressionsCtr).
					SetActiveViewViewability(data.ActiveViewViewability).
					SetMatchedAdRequests(data.MatchedAdRequests).
					SetCostPerClick(data.CostPerClick).Save(ctx)
				if err != nil {
					return err
				}
				return nil
			}
			return err
		}
		PageViewsRpm := 0.0
		if data.PageViews-lastHourData.PageViews > 0 {
			PageViewsRpm = (data.EstimatedEarnings - lastHourData.EstimatedEarnings) * 1000.0 / float64(data.PageViews-lastHourData.PageViews)
		}
		ImpressionsRpm := 0.0
		if data.IMPRESSIONS-lastHourData.Impressions > 0 {
			ImpressionsRpm = (data.EstimatedEarnings - lastHourData.EstimatedEarnings) * 1000 / float64(data.IMPRESSIONS-lastHourData.Impressions)
		}
		ImpressionsCtr := 0.0
		if data.IMPRESSIONS-lastHourData.Impressions > 0 {
			ImpressionsCtr = float64(data.CLICKS-lastHourData.Clicks) / float64(data.IMPRESSIONS-lastHourData.Impressions)
		}
		AdRequestsCoverage := 0.0
		if data.AdRequests-lastHourData.AdRequests > 0 {
			AdRequestsCoverage = float64(data.MatchedAdRequests-lastHourData.MatchedAdRequests) / float64(data.AdRequests-lastHourData.AdRequests)
		}
		CostPerClick := 0.0
		if data.CLICKS-lastHourData.Clicks > 0 {
			CostPerClick = (data.EstimatedEarnings - lastHourData.EstimatedEarnings) / float64(data.CLICKS-lastHourData.Clicks)
		}
		_, err = r.data.db.HourSiteCountry.Create().SetDate(time.Date(data.Date.Year(), data.Date.Month(), data.Date.Day()-1, 23, 00, 0, 0, cstSh)).SetSite(data.Site).SetCountry(data.Country).
			SetEstimatedEarnings(data.EstimatedEarnings - lastHourData.EstimatedEarnings).
			SetPageViews(data.PageViews - lastHourData.PageViews).SetPageViewsRpm(PageViewsRpm).
			SetImpressions(data.IMPRESSIONS - lastHourData.Impressions).SetImpressionsRpm(ImpressionsRpm).
			SetAdRequestsCoverage(AdRequestsCoverage).SetClicks(data.CLICKS - lastHourData.Clicks).
			SetAdRequests(data.AdRequests - lastHourData.AdRequests).SetImpressionsCtr(ImpressionsCtr).
			SetActiveViewViewability(data.ActiveViewViewability).
			SetMatchedAdRequests(data.MatchedAdRequests - lastHourData.MatchedAdRequests).
			SetCostPerClick(CostPerClick).Save(ctx)
		if err != nil {
			return err
		}
	} else if data.Date.Hour() == 1 {
		_, err = r.data.db.HourSiteCountry.Create().SetDate(time.Date(data.Date.Year(), data.Date.Month(), data.Date.Day(), data.Date.Hour()-1, 0, 0, 0, cstSh)).SetSite(data.Site).SetCountry(data.Country).
			SetEstimatedEarnings(data.EstimatedEarnings).
			SetPageViews(data.PageViews).SetPageViewsRpm(data.PageViewsRpm).
			SetImpressions(data.IMPRESSIONS).SetImpressionsRpm(data.ImpressionsRpm).
			SetAdRequestsCoverage(data.AdRequestsCoverage).SetClicks(data.CLICKS).
			SetAdRequests(data.AdRequests).SetImpressionsCtr(data.ImpressionsCtr).
			SetActiveViewViewability(data.ActiveViewViewability).
			SetMatchedAdRequests(data.MatchedAdRequests).
			SetCostPerClick(data.CostPerClick).Save(ctx)
		if err != nil {
			return err
		}
	}
	return nil
}

func NewTodaySiteCountryRepo(data *Data, logger log.Logger) biz.AdSenseSiteCountryTodayRepo {
	return &todaySiteCountryRepo{
		data,
		log.NewHelper(log.With(logger, "module", "data/site/country")),
	}
}
