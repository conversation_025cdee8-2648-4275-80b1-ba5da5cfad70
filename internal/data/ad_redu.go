package data

import (
	"context"

	"github.com/go-kratos/kratos/v2/log"

	"git.minigame.vip/minicloud/service/adsense-bot/internal/biz"
	"git.minigame.vip/minicloud/service/adsense-bot/internal/data/ent"
)

type adReductionRepo struct {
	data *Data
	log  *log.Helper
}

func (r *adReductionRepo) Save(ctx context.Context, data []*biz.AdReduction) error {
	bulk := make([]*ent.AdReductionCreate, 0)
	for _, d := range data {
		bulk = append(bulk, r.data.db.AdReduction.Create().SetDate(d.Date).SetSite(d.Site).
			SetReductionDate(d.ReductionDate).SetDeltaDay(d.DeltaDay).SetReductionType(d.ReductionType).
			SetReductionRateR(d.ReductionRateR).SetReductionRateI(d.ReductionRateI).
			SetReductionRateC(d.ReductionRateC).SetReductionRateE(d.ReductionRateE).
			SetReductionChangeR(d.ReductionChangeR).SetReductionChangeI(d.ReductionChangeI).
			SetReductionChangeC(d.ReductionChangeC).SetReductionChangeE(d.ReductionChangeE))
	}

	_, err := r.data.db.AdReduction.CreateBulk(bulk...).Save(ctx)
	if err != nil {
		return err
	}
	return nil
}

func NewAdReductionRepo(data *Data, logger log.Logger) biz.AdReductionRepo {
	return &adReductionRepo{
		data,
		log.NewHelper(log.With(logger, "module", "data/ad_reduction")),
	}
}
