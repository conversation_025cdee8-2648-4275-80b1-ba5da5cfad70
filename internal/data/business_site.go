package data

import (
	"context"
	"time"

	entsql "entgo.io/ent/dialect/sql"
	"github.com/go-kratos/kratos/v2/log"

	"git.minigame.vip/minicloud/service/adsense-bot/internal/biz"
	"git.minigame.vip/minicloud/service/adsense-bot/internal/data/ent"
	"git.minigame.vip/minicloud/service/adsense-bot/internal/data/ent/cooperationsiteurlchannel"
	"git.minigame.vip/minicloud/service/adsense-bot/internal/data/ent/sitebusiness"
)

type businessSiteRepo struct {
	data *Data
	log  *log.Helper
}

func (r *businessSiteRepo) Create(ctx context.Context, datas []*biz.BusinessSiteData) error {
	bulk := make([]*ent.SiteBusinessCreate, 0)

	tx, err := r.data.db.Tx(ctx)
	if err != nil {
		return err
	}

	for i, data := range datas {
		bulk = append(bulk, tx.SiteBusiness.Create().SetDate(data.Date).SetSite(data.Site).SetEstimatedEarnings(data.EstimatedEarnings).
			SetPageViews(data.PageViews).SetPageViewsRpm(data.PageViewsRpm).
			SetImpressions(data.IMPRESSIONS).SetImpressionsRpm(data.ImpressionsRpm).
			SetAdRequestsCoverage(data.AdRequestsCoverage).SetClicks(data.CLICKS).
			SetAdRequests(data.AdRequests).SetImpressionsCtr(data.ImpressionsCtr).
			SetActiveViewViewability(data.ActiveViewViewability).
			SetMatchedAdRequests(data.MatchedAdRequests).
			SetCostPerClick(data.CostPerClick).SetImpressionsPerPageView(data.ImpressionsPerPageView))

		if (i+1)%4000 == 0 || i == len(datas)-1 {
			err = tx.SiteBusiness.CreateBulk(bulk...).
				OnConflict(entsql.ConflictColumns("date", "site")).
				UpdateNewValues().Exec(ctx)
			// _, err := tx.SiteAdFormat.createBulk(bulk...).Save(ctx)
			if err != nil {
				return rollback(tx, err)
			}
			bulk = bulk[:0]
		}
	}

	return tx.Commit()
}

// CleanupStoppedSiteData 清理已下架网站的历史数据
// 删除在 stopping_time 之后的 site_businesses 数据
func (r *businessSiteRepo) CleanupStoppedSiteData(ctx context.Context) error {

	// 查询所有有 stopping_time 的网站
	stoppedSites, err := r.data.db.CooperationSiteUrlChannel.Query().
		Where(
			cooperationsiteurlchannel.StoppingTimeNEQ(time.Time{}),
			cooperationsiteurlchannel.URLChannelEQ(""), // 只处理网站（url_channel 为空）
			cooperationsiteurlchannel.DeletedAtIsNil(), // 未删除的记录
		).
		All(ctx)
	if err != nil {
		r.log.WithContext(ctx).Errorf("查询已下架网站失败: %v", err)
		return err
	}

	if len(stoppedSites) == 0 {
		return nil
	}

	for _, site := range stoppedSites {
		// 删除在 stopping_time 之后的数据
		_, err := r.data.db.SiteBusiness.Delete().
			Where(
				sitebusiness.SiteEQ(site.Site),
				sitebusiness.DateGTE(site.StoppingTime),
			).Exec(ctx)

		if err != nil {
			r.log.WithContext(ctx).Errorf("清理网站 %s 的历史数据失败: %v", site.Site, err)
			return err
		}

	}

	return nil
}

func NewBusinessSiteRepo(data *Data, logger log.Logger) biz.BusinessSiteRepo {
	return &businessSiteRepo{
		data: data,
		log:  log.NewHelper(log.With(logger, "module", "data/site/business")),
	}
}
