package data

import (
	"context"

	"github.com/go-kratos/kratos/v2/log"

	"git.minigame.vip/minicloud/service/adsense-bot/internal/biz"
	"git.minigame.vip/minicloud/service/adsense-bot/internal/data/ent"
)

type gamePurchaseSummaryRepo struct {
	data *Data
	log  *log.Helper
}

// NewGamePurchaseSummaryRepo 创建游戏内购汇总数据仓库
func NewGamePurchaseSummaryRepo(data *Data, logger log.Logger) biz.GamePurchaseSummaryRepo {
	return &gamePurchaseSummaryRepo{
		data: data,
		log:  log.NewHelper(log.With(logger, "module", "data/game_purchase_summary")),
	}
}

// Create 批量创建游戏内购汇总记录
func (r *gamePurchaseSummaryRepo) Create(ctx context.Context, summaries []*biz.GamePurchaseSummaryData) error {
	if len(summaries) == 0 {
		return nil
	}

	// 开启事务
	tx, err := r.data.db.Tx(ctx)
	if err != nil {
		r.log.WithContext(ctx).Errorf("开启事务失败: %v", err)
		return err
	}

	const batchSize = 1000
	bulk := make([]*ent.GamePurchaseSummaryCreate, 0, batchSize)

	for i, summary := range summaries {
		create := tx.GamePurchaseSummary.Create().
			SetDate(summary.Date).
			SetDomain(summary.Domain).
			SetAppID(summary.GameAppid).
			SetPayAmount(summary.PayAmount).
			SetPayTimes(summary.PayTimes).
			SetPayUsers(summary.PayUsers).
			SetPayoutAmount(summary.PayoutAmount).
			SetCreateAt(summary.CreateTime).
			SetUpdateAt(summary.UpdateTime)

		bulk = append(bulk, create)

		if (i+1)%batchSize == 0 || i == len(summaries)-1 {
			err := r.executeBatch(ctx, tx, bulk)
			if err != nil {
				return rollbackTx(tx, err)
			}
			bulk = bulk[:0] // 清空切片
		}
	}

	if err := tx.Commit(); err != nil {
		r.log.WithContext(ctx).Errorf("提交事务失败: %v", err)
		return err
	}

	return nil
}

// Delete 删除所有游戏内购汇总数据
func (r *gamePurchaseSummaryRepo) Delete(ctx context.Context) error {
	_, err := r.data.db.GamePurchaseSummary.Delete().Exec(ctx)
	if err != nil {
		r.log.WithContext(ctx).Errorf("删除内购汇总数据失败: %v", err)
		return err
	}
	return nil
}

// executeBatch 执行批量插入
func (r *gamePurchaseSummaryRepo) executeBatch(ctx context.Context, tx *ent.Tx, bulk []*ent.GamePurchaseSummaryCreate) error {
	if len(bulk) == 0 {
		return nil
	}

	err := tx.GamePurchaseSummary.CreateBulk(bulk...).Exec(ctx)
	if err != nil {
		r.log.WithContext(ctx).Errorf("批量插入失败: %v", err)
		return err
	}

	return nil
}

// rollbackTx 回滚事务的辅助函数
func rollbackTx(tx *ent.Tx, err error) error {
	if rerr := tx.Rollback(); rerr != nil {
		return rerr
	}
	return err
}
