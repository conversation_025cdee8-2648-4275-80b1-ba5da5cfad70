package data

import (
	"context"
	"time"

	"entgo.io/ent/dialect/sql"
	"github.com/go-kratos/kratos/v2/log"

	"git.minigame.vip/minicloud/service/adsense-bot/internal/biz"
	"git.minigame.vip/minicloud/service/adsense-bot/internal/data/ent"
	"git.minigame.vip/minicloud/service/adsense-bot/internal/data/ent/cooperation"
)

type cooperationRepo struct {
	data *Data
	log  *log.Helper
}

func (r *cooperationRepo) DeleteUnusedCooperation(ctx context.Context, c []string) error {
	err := r.data.db.Cooperation.Update().Where(cooperation.NameNotIn(c...), cooperation.DeletedAtIsNil()).
		SetDeletedAt(time.Now().Unix()).Exec(ctx)
	if err != nil {
		return err
	}
	return nil
}

func (r *cooperationRepo) CreateOrUpdate(ctx context.Context, datas []biz.CooperationValue) error {
	bulk := make([]*ent.CooperationCreate, 0)

	for di, data := range datas {
		bulk = append(bulk, r.data.db.Cooperation.Create().SetName(data.Name).SetSiteCount(data.SiteCount).
			SetURLChannelCount(data.UrlChannelCount))
		if (di+1)%4000 == 0 || di == len(datas)-1 {
			err := r.data.db.Cooperation.CreateBulk(bulk...).OnConflict(sql.ConflictColumns("name")).UpdateNewValues().Exec(ctx)

			// _, err := tx.SiteCountry.createBulk(bulk...).Save(ctx)
			if err != nil {
				return err
			}
			bulk = bulk[:0]
		}
	}

	return nil
}

func NewCooperationRepo(data *Data, logger log.Logger) biz.CooperationRepo {
	return &cooperationRepo{
		data,
		log.NewHelper(log.With(logger, "module", "data/cooperation")),
	}
}
