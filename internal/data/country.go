package data

import (
	"context"

	"github.com/go-kratos/kratos/v2/log"

	"git.minigame.vip/minicloud/service/adsense-bot/internal/biz"
)

type countryRepo struct {
	data *Data
	log  *log.Helper
}

func (r *countryRepo) GetCountries(ctx context.Context) (map[string]string, error) {
	res := make(map[string]string)
	data, err := r.data.db.Country.Query().All(ctx)
	if err != nil {
		return nil, err
	}
	for _, v := range data {
		res[v.Code] = v.Name
	}
	return res, nil
}

// NewCountryRepo .
func NewCountryRepo(data *Data, logger log.Logger) biz.CountryRepo {
	return &countryRepo{
		data: data,
		log:  log.NewHelper(log.With(logger, "module", "adsense-bot/data/country")),
	}
}
