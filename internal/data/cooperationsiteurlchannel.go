package data

import (
	"context"

	"entgo.io/ent/dialect/sql"
	"github.com/go-kratos/kratos/v2/log"

	"git.minigame.vip/minicloud/service/adsense-bot/internal/biz"
	"git.minigame.vip/minicloud/service/adsense-bot/internal/data/ent"
	"git.minigame.vip/minicloud/service/adsense-bot/internal/data/ent/cooperationsiteurlchannel"
)

type cooperationSiteUrlChannelRepo struct {
	data *Data
	log  *log.Helper
}

func (r *cooperationSiteUrlChannelRepo) List(ctx context.Context) ([]biz.ICooperationSiteUrlChannel, error) {
	all, err := r.data.db.CooperationSiteUrlChannel.Query().All(ctx)
	if err != nil {
		return nil, err
	}
	var list []biz.ICooperationSiteUrlChannel
	for _, item := range all {
		list = append(list,
			biz.NewCooperationSiteUrlChannel(item.ID,
				biz.CooperationSiteUrlChannelValue{
					Name:               item.Name,
					State:              item.State,
					Site:               item.Site,
					UrlChannel:         item.URLChannel,
					CooperationChannel: item.CooperationChannel,
					Manager:            item.Manager,
					GameCount:          item.GameCount,
					CreatedAt:          item.CreateTime.Unix(),
				}))
	}
	return list, nil
}

func (r *cooperationSiteUrlChannelRepo) CreateOrUpdate(ctx context.Context, datas []biz.CooperationSiteUrlChannelValue) error {
	bulk := make([]*ent.CooperationSiteUrlChannelCreate, 0)

	for di, data := range datas {
		bulk = append(bulk, r.data.db.CooperationSiteUrlChannel.Create().SetCooperationChannel(data.CooperationChannel).SetURLChannel(data.UrlChannel).
			SetSite(data.Site).SetName(data.Name))
		if (di+1)%4000 == 0 || di == len(datas)-1 {
			err := r.data.db.CooperationSiteUrlChannel.CreateBulk(bulk...).OnConflict(sql.ConflictColumns("site", "url_channel")).UpdateNewValues().Exec(ctx)

			// _, err := tx.SiteCountry.createBulk(bulk...).Save(ctx)
			if err != nil {
				return err
			}
			bulk = bulk[:0]
		}
	}

	return nil
}

func (r *cooperationSiteUrlChannelRepo) ListCooperationSite(ctx context.Context) ([]biz.ICooperationSiteUrlChannel, error) {
	all, err := r.data.db.CooperationSiteUrlChannel.Query().Where(cooperationsiteurlchannel.Or(cooperationsiteurlchannel.URLChannelIsNil(), cooperationsiteurlchannel.URLChannelEQ(""))).All(ctx)
	if err != nil {
		return nil, err
	}
	var list []biz.ICooperationSiteUrlChannel
	for _, item := range all {
		list = append(list,
			biz.NewCooperationSiteUrlChannel(item.ID,
				biz.CooperationSiteUrlChannelValue{
					Name:               item.Name,
					State:              item.State,
					Site:               item.Site,
					UrlChannel:         item.URLChannel,
					CooperationChannel: item.CooperationChannel,
					Manager:            item.Manager,
					GameCount:          item.GameCount,
					CreatedAt:          item.CreateTime.Unix(),
				}))
	}
	return list, nil
}

func NewCooperationSiteUrlChannelRepo(data *Data, logger log.Logger) biz.CooperationSiteUrlChannelRepo {
	return &cooperationSiteUrlChannelRepo{
		data,
		log.NewHelper(log.With(logger, "module", "data/cooperationSiteUrlChannel")),
	}
}
