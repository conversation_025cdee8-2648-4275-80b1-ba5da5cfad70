package data

import (
	"context"
	"strconv"

	"entgo.io/ent/dialect/sql"
	"github.com/go-kratos/kratos/v2/log"

	"git.minigame.vip/minicloud/service/adsense-bot/internal/biz"
	"git.minigame.vip/minicloud/service/adsense-bot/internal/data/ent"
	"git.minigame.vip/minicloud/service/adsense-bot/pkg/urlutil"
)

type urlChannelAdFormatRepo struct {
	data *Data
	log  *log.Helper
}

func (r *urlChannelAdFormatRepo) CreateHKD(ctx context.Context, datas []*biz.AdsenseURLChannelAdFormatData) error {
	bulkInfo := make([]*ent.UrlChannelAdFormatInfoHKDCreate, 0)
	tx, err := r.data.db.Tx(ctx)
	if err != nil {
		return err
	}
	for i, data := range datas {

		infos := urlutil.FormatURL("https://" + data.UrlChannel)
		isRoot, _ := strconv.ParseBool(infos[5])
		bulkInfo = append(bulkInfo, tx.UrlChannelAdFormatInfoHKD.Create().SetAdFormat(data.AdFormat).
			SetDate(data.Date).SetURLChannel(data.UrlChannel).SetEstimatedEarnings(data.EstimatedEarnings).
			SetPageViews(data.PageViews).SetPageViewsRpm(data.PageViewsRpm).
			SetImpressions(data.IMPRESSIONS).SetImpressionsRpm(data.ImpressionsRpm).
			SetAdRequestsCoverage(data.AdRequestsCoverage).SetClicks(data.CLICKS).
			SetAdRequests(data.AdRequests).SetImpressionsCtr(data.ImpressionsCtr).
			SetActiveViewViewability(data.ActiveViewViewability).
			SetMatchedAdRequests(data.MatchedAdRequests).
			SetSite(infos[0]).SetSubChannel(infos[2]).SetPageType(infos[3]).SetAppID(infos[4]).
			SetIsRoot(isRoot).
			SetCostPerClick(data.CostPerClick))
		if (i+1)%2000 == 0 || i == len(datas)-1 {
			err = tx.UrlChannelAdFormatInfoHKD.CreateBulk(bulkInfo...).OnConflict(sql.ConflictColumns("date", "url_channel", "ad_format")).UpdateNewValues().Exec(ctx)
			if err != nil {
				return rollback(tx, err)
			}
			bulkInfo = bulkInfo[:0]
		}
	}

	return tx.Commit()
}

func (r *urlChannelAdFormatRepo) Create(ctx context.Context, datas []*biz.AdsenseURLChannelAdFormatData) error {
	bulk := make([]*ent.UrlChannelAdFormatCreate, 0)
	bulkInfo := make([]*ent.UrlChannelAdFormatInfoCreate, 0)
	tx, err := r.data.db.Tx(ctx)
	if err != nil {
		return err
	}
	for i, data := range datas {
		bulk = append(bulk, tx.UrlChannelAdFormat.Create().SetDate(data.Date).SetURLChannel(data.UrlChannel).
			SetEstimatedEarnings(data.EstimatedEarnings).
			SetPageViews(data.PageViews).SetPageViewsRpm(data.PageViewsRpm).
			SetAdFormat(data.AdFormat).
			SetImpressions(data.IMPRESSIONS).SetImpressionsRpm(data.ImpressionsRpm).
			SetAdRequestsCoverage(data.AdRequestsCoverage).SetClicks(data.CLICKS).
			SetAdRequests(data.AdRequests).SetImpressionsCtr(data.ImpressionsCtr).
			SetActiveViewViewability(data.ActiveViewViewability).
			SetMatchedAdRequests(data.MatchedAdRequests).
			SetCostPerClick(data.CostPerClick))
		infos := urlutil.FormatURL("https://" + data.UrlChannel)
		isRoot, _ := strconv.ParseBool(infos[5])
		bulkInfo = append(bulkInfo, tx.UrlChannelAdFormatInfo.Create().SetAdFormat(data.AdFormat).
			SetDate(data.Date).SetURLChannel(data.UrlChannel).SetEstimatedEarnings(data.EstimatedEarnings).
			SetPageViews(data.PageViews).SetPageViewsRpm(data.PageViewsRpm).
			SetImpressions(data.IMPRESSIONS).SetImpressionsRpm(data.ImpressionsRpm).
			SetAdRequestsCoverage(data.AdRequestsCoverage).SetClicks(data.CLICKS).
			SetAdRequests(data.AdRequests).SetImpressionsCtr(data.ImpressionsCtr).
			SetActiveViewViewability(data.ActiveViewViewability).
			SetMatchedAdRequests(data.MatchedAdRequests).
			SetSite(infos[0]).SetSubChannel(infos[2]).SetPageType(infos[3]).SetAppID(infos[4]).
			SetIsRoot(isRoot).
			SetCostPerClick(data.CostPerClick))
		if (i+1)%2000 == 0 || i == len(datas)-1 {
			err = tx.UrlChannelAdFormat.CreateBulk(bulk...).OnConflict(sql.ConflictColumns("date", "url_channel", "ad_format")).UpdateNewValues().Exec(ctx)

			// _, err := tx.UrlChannelAdFormat.createBulk(bulk...).Save(ctx)
			if err != nil {
				return rollback(tx, err)
			}
			err = tx.UrlChannelAdFormatInfo.CreateBulk(bulkInfo...).OnConflict(sql.ConflictColumns("date", "url_channel", "ad_format")).UpdateNewValues().Exec(ctx)

			// _, err = tx.UrlChannelAdFormatInfo.createBulk(bulkInfo...).Save(ctx)
			if err != nil {
				return rollback(tx, err)
			}
			bulk = bulk[:0]
			bulkInfo = bulkInfo[:0]
		}
	}

	return tx.Commit()
}

func NewUrlChannelAdFormatRepo(data *Data, logger log.Logger) biz.AdSenseURLChannelAdFormatRepo {
	return &urlChannelAdFormatRepo{
		data,
		log.NewHelper(log.With(logger, "module", "data/urlChannel/adFormat")),
	}
}
