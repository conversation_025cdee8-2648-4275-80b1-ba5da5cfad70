package data

import (
	"context"

	"entgo.io/ent/dialect/sql"
	"github.com/go-kratos/kratos/v2/log"

	"git.minigame.vip/minicloud/service/adsense-bot/internal/biz"
	"git.minigame.vip/minicloud/service/adsense-bot/internal/data/ent"
	"git.minigame.vip/minicloud/service/adsense-bot/pkg/urlutil"
)

type pageURLAdFormatRepo struct {
	data *Data
	log  *log.Helper
}

func (r *pageURLAdFormatRepo) CreateHKD(ctx context.Context, datas []*biz.AdsensePageURLAdFormatData) error {
	bulkInfo := make([]*ent.PageUrlAdFormatInfoHKDCreate, 0)
	tx, err := r.data.db.Tx(ctx)
	if err != nil {
		return err
	}
	for i, data := range datas {

		infos := urlutil.FormatURL(data.PageURL)
		bulkInfo = append(bulkInfo, tx.PageUrlAdFormatInfoHKD.Create().SetAdFormat(data.AdFormat).
			SetDate(data.Date).SetPageURL(data.PageURL).SetEstimatedEarnings(data.EstimatedEarnings).
			SetPageViews(data.PageViews).SetPageViewsRpm(data.PageViewsRpm).
			SetImpressions(data.IMPRESSIONS).SetImpressionsRpm(data.ImpressionsRpm).
			SetAdRequestsCoverage(data.AdRequestsCoverage).SetClicks(data.CLICKS).
			SetAdRequests(data.AdRequests).SetImpressionsCtr(data.ImpressionsCtr).
			SetActiveViewViewability(data.ActiveViewViewability).
			SetMatchedAdRequests(data.MatchedAdRequests).
			SetSite(infos[0]).SetFrom(infos[1]).SetSubChannel(infos[2]).SetPageType(infos[3]).SetAppID(infos[4]).
			SetCostPerClick(data.CostPerClick))
		if (i+1)%2000 == 0 || i == len(datas)-1 {
			err := tx.PageUrlAdFormatInfoHKD.CreateBulk(bulkInfo...).OnConflict(sql.ConflictColumns("date", "page_url", "ad_format")).UpdateNewValues().Exec(ctx)

			// _, err := tx.PageUrl.createBulk(bulk...).Save(ctx)
			if err != nil {
				return rollback(tx, err)
			}

			bulkInfo = bulkInfo[:0]
		}
	}

	return tx.Commit()
}

func (r *pageURLAdFormatRepo) Create(ctx context.Context, datas []*biz.AdsensePageURLAdFormatData) error {
	bulk := make([]*ent.PageUrlAdFormatCreate, 0)
	bulkInfo := make([]*ent.PageUrlAdFormatInfoCreate, 0)
	tx, err := r.data.db.Tx(ctx)
	if err != nil {
		return err
	}
	for i, data := range datas {
		bulk = append(bulk, tx.PageUrlAdFormat.Create().SetAdFormat(data.AdFormat).
			SetDate(data.Date).SetPageURL(data.PageURL).SetEstimatedEarnings(data.EstimatedEarnings).
			SetPageViews(data.PageViews).SetPageViewsRpm(data.PageViewsRpm).
			SetImpressions(data.IMPRESSIONS).SetImpressionsRpm(data.ImpressionsRpm).
			SetAdRequestsCoverage(data.AdRequestsCoverage).SetClicks(data.CLICKS).
			SetAdRequests(data.AdRequests).SetImpressionsCtr(data.ImpressionsCtr).
			SetActiveViewViewability(data.ActiveViewViewability).
			SetMatchedAdRequests(data.MatchedAdRequests).
			SetCostPerClick(data.CostPerClick))
		infos := urlutil.FormatURL(data.PageURL)
		bulkInfo = append(bulkInfo, tx.PageUrlAdFormatInfo.Create().SetAdFormat(data.AdFormat).
			SetDate(data.Date).SetPageURL(data.PageURL).SetEstimatedEarnings(data.EstimatedEarnings).
			SetPageViews(data.PageViews).SetPageViewsRpm(data.PageViewsRpm).
			SetImpressions(data.IMPRESSIONS).SetImpressionsRpm(data.ImpressionsRpm).
			SetAdRequestsCoverage(data.AdRequestsCoverage).SetClicks(data.CLICKS).
			SetAdRequests(data.AdRequests).SetImpressionsCtr(data.ImpressionsCtr).
			SetActiveViewViewability(data.ActiveViewViewability).
			SetMatchedAdRequests(data.MatchedAdRequests).
			SetSite(infos[0]).SetFrom(infos[1]).SetSubChannel(infos[2]).SetPageType(infos[3]).SetAppID(infos[4]).
			SetCostPerClick(data.CostPerClick))
		if (i+1)%2000 == 0 || i == len(datas)-1 {
			err := tx.PageUrlAdFormatInfo.CreateBulk(bulkInfo...).OnConflict(sql.ConflictColumns("date", "page_url", "ad_format")).UpdateNewValues().Exec(ctx)

			// _, err := tx.PageUrl.createBulk(bulk...).Save(ctx)
			if err != nil {
				return rollback(tx, err)
			}

			err = tx.PageUrlAdFormat.CreateBulk(bulk...).OnConflict(sql.ConflictColumns("date", "page_url", "ad_format")).UpdateNewValues().Exec(ctx)
			if err != nil {
				return rollback(tx, err)
			}

			bulk = bulk[:0]
			bulkInfo = bulkInfo[:0]
		}
	}

	return tx.Commit()
}

func NewPageURLAdFormatRepo(data *Data, logger log.Logger) biz.AdSensePageURLAdFormatRepo {
	return &pageURLAdFormatRepo{
		data,
		log.NewHelper(log.With(logger, "module", "data/pageURL/adformat")),
	}
}
