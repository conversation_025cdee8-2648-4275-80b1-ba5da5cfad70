package data

import (
	"context"
	"database/sql"
	"errors"
	"fmt"
	"strings"
	"time"

	entsql "entgo.io/ent/dialect/sql"
	"github.com/go-kratos/kratos/v2/log"
	analyticsdata "google.golang.org/api/analyticsdata/v1beta"
	"google.golang.org/api/googleapi"

	"git.minigame.vip/minicloud/service/adsense-bot/internal/biz"
	"git.minigame.vip/minicloud/service/adsense-bot/internal/data/ent"
	"git.minigame.vip/minicloud/service/adsense-bot/internal/data/ent/analyticssite"
	"git.minigame.vip/minicloud/service/adsense-bot/internal/data/ent/analyticssitemonth"
	"git.minigame.vip/minicloud/service/adsense-bot/pkg/cast"
)

const (
	// Analytics Dimensions
	DimensionHostName              = "hostName"
	DimensionDate                  = "date"
	DimensionLanguage              = "language"
	DimensionBrowser               = "browser"
	DimensionYearMonth             = "yearMonth"
	DimensionUnifiedPagePathScreen = "unifiedPagePathScreen"
	DimensionEventName             = "eventName"

	// Custom Event Dimensions
	DimensionCustomEventUA       = "customEvent:mini_ua"
	DimensionCustomEventRes      = "customEvent:mini_res"
	DimensionCustomEventDpi      = "customEvent:mini_dpi"
	DimensionCustomEventAudioFP  = "customEvent:mini_audioFP"
	DimensionCustomEventCanvasFP = "customEvent:mini_canvasFP"
	DimensionCustomEventFontFP   = "customEvent:mini_fontFP"
	DimensionCustomEventTz       = "customEvent:mini_tz"
	DimensionCustomEventVisRes   = "customEvent:mini_visRes"
	DimensionCustomEventGlMod    = "customEvent:mini_glMod"
	DimensionCustomEventMiniLang = "customEvent:mini_lang"

	// Analytics Metrics
	MetricActiveUsers            = "activeUsers"
	MetricAverageSessionDuration = "averageSessionDuration"
	MetricNewUsers               = "newUsers"
	MetricScreenPageViews        = "screenPageViews"
	MetricTotalUsers             = "totalUsers"
	MetricUserEngagementDuration = "userEngagementDuration"
	MetricScreenPageViewsPerUser = "screenPageViewsPerUser"
	MetricEventCount             = "eventCount"

	// Metric indices for Query response (order matters!)
	MetricIndexActiveUsers            = 0
	MetricIndexAverageSessionDuration = 1
	MetricIndexNewUsers               = 2
	MetricIndexScreenPageViews        = 3
	MetricIndexTotalUsers             = 4
	MetricIndexUserEngagementDuration = 5
	MetricIndexScreenPageViewsPerUser = 6

	// Dimension indices for Query response
	DimensionIndexHostName = 0
	DimensionIndexDate     = 1

	// Standard Dimension Request Types (available in all GA properties)
	RequestTypeStandardBrowser  = "browser"
	RequestTypeStandardLanguage = "language"

	// Custom Dimension Request Types (may not be available in all GA properties)
	RequestTypeUA               = "ua"
	RequestTypeDeviceResolution = "device_resolution"
	RequestTypeDpi              = "dpi"
	RequestTypeAudioFp          = "audio_fp"
	RequestTypeCanvasFp         = "canvas_fp"
	RequestTypeFontFp           = "font_fp"
	RequestTypeTz               = "tz"
	RequestTypeVisRes           = "vis_res"
	RequestTypeGlMod            = "gl_mod"
	RequestTypeMiniLang         = "mini_lang"

	// Deprecated: Use RequestTypeStandardBrowser instead
	RequestTypeBrowser = RequestTypeStandardBrowser
	// Deprecated: Use RequestTypeStandardLanguage instead
	RequestTypeLanguage = RequestTypeStandardLanguage

	// Standard Dimension Request Names
	RequestNameStandardBrowserData  = "standard_browser_data"
	RequestNameStandardLanguageData = "standard_language_data"

	// Custom Dimension Request Names
	RequestNameUAData           = "ua"
	RequestNameDeviceResolution = "device_resolution"
	RequestNameDpi              = "dpi"
	RequestNameAudioFp          = "audio_fp"
	RequestNameCanvasFp         = "canvas_fp"
	RequestNameFontFp           = "font_fp"
	RequestNameTz               = "tz"
	RequestNameVisRes           = "vis_res"
	RequestNameGlMod            = "gl_mod"
	RequestNameMiniLang         = "mini_lang"

	// Deprecated: Use RequestNameStandardBrowserData instead
	RequestNameBrowserData = RequestNameStandardBrowserData
	// Deprecated: Use RequestNameStandardLanguageData instead
	RequestNameLanguageData = RequestNameStandardLanguageData

	// Other constants
	limit = 250000

	// MaxBatchRequestSize Google Analytics Data API BatchRunReports 最大请求数限制
	MaxBatchRequestSize = 5

	// Base Data Request Constants
	RequestNameBaseData     = "base_data"
	RequestNameBaseDataDesc = "基础分析数据"
)

var (
	ErrorCustomDefinitionsNotConfigured = errors.New("custom definitions not configured")
)

// DimensionDataMapping 维度数据映射配置
type DimensionDataMapping struct {
	RequestName string
	FieldSetter func(*biz.AnalyticsSiteData, []*biz.ActiveUsersData)
}

// dimensionMappings 维度数据映射配置列表
var dimensionMappings = []DimensionDataMapping{
	{RequestNameStandardBrowserData, func(data *biz.AnalyticsSiteData, values []*biz.ActiveUsersData) { data.Browsers = values }},
	{RequestNameStandardLanguageData, func(data *biz.AnalyticsSiteData, values []*biz.ActiveUsersData) { data.Languages = values }},
	{RequestNameUAData, func(data *biz.AnalyticsSiteData, values []*biz.ActiveUsersData) { data.Ua = values }},
	{RequestNameDeviceResolution, func(data *biz.AnalyticsSiteData, values []*biz.ActiveUsersData) { data.DeviceResolution = values }},
	{RequestNameDpi, func(data *biz.AnalyticsSiteData, values []*biz.ActiveUsersData) { data.Dpi = values }},
	{RequestNameAudioFp, func(data *biz.AnalyticsSiteData, values []*biz.ActiveUsersData) { data.AudioFp = values }},
	{RequestNameCanvasFp, func(data *biz.AnalyticsSiteData, values []*biz.ActiveUsersData) { data.CanvasFp = values }},
	{RequestNameFontFp, func(data *biz.AnalyticsSiteData, values []*biz.ActiveUsersData) { data.FontFp = values }},
	{RequestNameTz, func(data *biz.AnalyticsSiteData, values []*biz.ActiveUsersData) { data.Tz = values }},
	{RequestNameVisRes, func(data *biz.AnalyticsSiteData, values []*biz.ActiveUsersData) { data.VisRes = values }},
	{RequestNameGlMod, func(data *biz.AnalyticsSiteData, values []*biz.ActiveUsersData) { data.GlMod = values }},
	{RequestNameMiniLang, func(data *biz.AnalyticsSiteData, values []*biz.ActiveUsersData) { data.MiniLang = values }},
}

type analyticsSiteRepo struct {
	data *Data
	log  *log.Helper
}

func (r *analyticsSiteRepo) GetVisitPageCountGaData(ctx context.Context, start time.Time, end time.Time) (map[string]int, error) {
	all, err := r.data.db.AnalyticsSiteMonth.Query().Where(analyticssitemonth.Month(fmt.Sprintf("%d%02d", end.Year(), end.Month()))).All(ctx)
	if err != nil {
		return nil, err
	}
	m := make(map[string]int)
	for _, v := range all {
		m[v.Site] = v.VisitPageCount
	}
	return m, nil
}

func (r *analyticsSiteRepo) GetSiteGaData(ctx context.Context, start time.Time, end time.Time) ([]*biz.AnalyticsSiteDataResponse, error) {
	var all []*biz.AnalyticsSiteDataResponse
	err := r.data.db.AnalyticsSite.Query().Where(analyticssite.DateGTE(start), analyticssite.DateLTE(end)).GroupBy(analyticssite.FieldSite).Aggregate(func(selector *entsql.Selector) string {
		t := entsql.Table(analyticssite.Table)
		return entsql.As(entsql.Avg(t.C(analyticssite.FieldAverageSessionDuration)), analyticssite.FieldAverageSessionDuration)
	}, func(selector *entsql.Selector) string {
		t := entsql.Table(analyticssite.Table)
		return entsql.As(entsql.Avg(t.C(analyticssite.FieldAverageEngagementTimePerActiveUser)), analyticssite.FieldAverageEngagementTimePerActiveUser)
	}, func(selector *entsql.Selector) string {
		t := entsql.Table(analyticssite.Table)
		return entsql.As(entsql.Avg(t.C(analyticssite.FieldScreenPageViewsPerUser)), analyticssite.FieldScreenPageViewsPerUser)
	}, func(selector *entsql.Selector) string {
		t := entsql.Table(analyticssite.Table)
		return entsql.As(entsql.Sum(t.C(analyticssite.FieldPageViews)), analyticssite.FieldPageViews)
	}, func(selector *entsql.Selector) string {
		t := entsql.Table(analyticssite.Table)
		return entsql.As(entsql.Sum(t.C(analyticssite.FieldGamePageViews)), analyticssite.FieldGamePageViews)
	}, func(selector *entsql.Selector) string {
		t := entsql.Table(analyticssite.Table)
		return entsql.As(entsql.Sum(t.C(analyticssite.FieldGameTotalUsers)), analyticssite.FieldGameTotalUsers)
	}, func(selector *entsql.Selector) string {
		t := entsql.Table(analyticssite.Table)
		return entsql.As(entsql.Avg(t.C(analyticssite.FieldGameAverageSessionDuration)), analyticssite.FieldGameAverageSessionDuration)
	}).Scan(ctx, &all)
	if err != nil {
		return nil, err
	}
	browserDataMap, err := r.getActiveUsersData(ctx, start, end)
	if err != nil {
		r.log.Errorf("GetActiveUsersData error: %v", err)
		return nil, err
	}
	for _, data := range all {
		if _, ok := browserDataMap[data.Site]; ok {
			data.BrowserMap = browserDataMap[data.Site]
		} else {
			data.BrowserMap = make(map[string]int)
		}
	}
	return all, nil
}

func (r *analyticsSiteRepo) Delete(ctx context.Context, start, end time.Time) error {
	_, err := r.data.db.AnalyticsSite.Delete().Where(
		analyticssite.DateGTE(start),
		analyticssite.DateLTE(end),
	).Exec(ctx)
	if err != nil {
		return err
	}
	return nil
}

// generateSiteDateKey 生成站点和日期的组合键
func generateSiteDateKey(site, date string) string {
	return site + "_" + date
}

// parseAnalyticsDate 解析 Analytics 日期格式
func parseAnalyticsDate(dateStr string) (time.Time, error) {
	return time.Parse("20060102", dateStr)
}

// calculateUserEngagementDuration 计算用户参与时长
func calculateUserEngagementDuration(userEngagementDuration float64, activeUsers int) float64 {
	if activeUsers == 0 {
		return 0.0
	}
	return userEngagementDuration / float64(activeUsers)
}

// GamePageData 游戏页数据结构
type GamePageData struct {
	PageViews              int     `json:"page_views"`
	TotalUsers             int     `json:"total_users"`
	AverageSessionDuration float64 `json:"average_session_duration"`
}

// SdkStartEventData SDK启动事件数据结构
type SdkStartEventData struct {
	EventCount int `json:"event_count"`
	TotalUsers int `json:"total_users"`
}

// getGamePageData 获取游戏页数据
func (r *analyticsSiteRepo) getGamePageData(ctx context.Context, propertiesID, start, end string) (map[string]map[string]*GamePageData, error) {
	resp, err := r.data.gs.analytics.Properties.RunReport(fmt.Sprintf("properties/%s", propertiesID), &analyticsdata.RunReportRequest{
		DateRanges: []*analyticsdata.DateRange{{EndDate: end, StartDate: start}},
		DimensionFilter: &analyticsdata.FilterExpression{
			Filter: &analyticsdata.Filter{
				FieldName: DimensionUnifiedPagePathScreen,
				StringFilter: &analyticsdata.StringFilter{
					MatchType: "FULL_REGEXP",
					Value:     "^(/[a-z]{2})?(/pc)?/game/[\\w-]+/(?:play(?:/[a-z]+)?|info(?:/recommended)?|offplay|match/\\d+)$",
				},
			},
		},
		Dimensions: []*analyticsdata.Dimension{
			{Name: DimensionHostName},
			{Name: DimensionDate},
		},
		Metrics: []*analyticsdata.Metric{
			{Name: MetricScreenPageViews},
			{Name: MetricTotalUsers},
			{Name: MetricAverageSessionDuration},
		},
		Limit: limit,
	}).Context(ctx).Do()
	if err != nil {
		return nil, err
	}

	// 构建游戏页数据映射：site -> date -> GamePageData
	gamePageMap := make(map[string]map[string]*GamePageData)
	for _, row := range resp.Rows {
		if len(row.DimensionValues) < 2 || len(row.MetricValues) < 3 {
			continue
		}

		site := row.DimensionValues[0].Value
		date := row.DimensionValues[1].Value

		if gamePageMap[site] == nil {
			gamePageMap[site] = make(map[string]*GamePageData)
		}

		gamePageMap[site][date] = &GamePageData{
			PageViews:              cast.StringToInt(row.MetricValues[0].Value),
			TotalUsers:             cast.StringToInt(row.MetricValues[1].Value),
			AverageSessionDuration: cast.StringToFloat(row.MetricValues[2].Value),
		}
	}
	return gamePageMap, nil
}

// getSdkStartEventData 获取SDK启动事件数据
func (r *analyticsSiteRepo) getSdkStartEventData(ctx context.Context, propertiesID, start, end string) (map[string]map[string]*SdkStartEventData, error) {
	resp, err := r.data.gs.analytics.Properties.RunReport(fmt.Sprintf("properties/%s", propertiesID), &analyticsdata.RunReportRequest{
		DateRanges: []*analyticsdata.DateRange{{EndDate: end, StartDate: start}},
		DimensionFilter: &analyticsdata.FilterExpression{
			Filter: &analyticsdata.Filter{
				FieldName: DimensionEventName,
				StringFilter: &analyticsdata.StringFilter{
					MatchType: "EXACT",
					Value:     "sdk_start",
				},
			},
		},
		Dimensions: []*analyticsdata.Dimension{
			{Name: DimensionHostName},
			{Name: DimensionDate},
		},
		Metrics: []*analyticsdata.Metric{
			{Name: MetricEventCount},
			{Name: MetricTotalUsers},
		},
		Limit: limit,
	}).Context(ctx).Do()
	if err != nil {
		return nil, err
	}

	// 构建SDK启动事件数据映射：site -> date -> SdkStartEventData
	sdkStartEventMap := make(map[string]map[string]*SdkStartEventData)
	for _, row := range resp.Rows {
		if len(row.DimensionValues) < 2 || len(row.MetricValues) < 2 {
			continue
		}

		site := row.DimensionValues[0].Value
		date := row.DimensionValues[1].Value

		if sdkStartEventMap[site] == nil {
			sdkStartEventMap[site] = make(map[string]*SdkStartEventData)
		}

		sdkStartEventMap[site][date] = &SdkStartEventData{
			EventCount: cast.StringToInt(row.MetricValues[0].Value),
			TotalUsers: cast.StringToInt(row.MetricValues[1].Value),
		}
	}
	return sdkStartEventMap, nil
}

func (r *analyticsSiteRepo) Query(ctx context.Context, propertiesID, start, end string, queryBaseData bool) ([]*biz.AnalyticsSiteData, error) {
	// 获取基础分析数据
	baseResp, err := r.getBaseAnalyticsData(ctx, propertiesID, start, end)
	if err != nil {
		return nil, err
	}

	// 获取维度数据
	dimensionData, err := r.getAllDimensionData(ctx, propertiesID, start, end, queryBaseData)
	if err != nil {
		return nil, err
	}

	// 获取游戏页数据
	gamePageData, err := r.getGamePageData(ctx, propertiesID, start, end)
	if err != nil {
		return nil, err
	}

	// 获取SDK启动事件数据
	sdkStartEventData, err := r.getSdkStartEventData(ctx, propertiesID, start, end)
	if err != nil {
		return nil, err
	}

	// 构建分析数据列表
	return r.buildAnalyticsDataList(baseResp, dimensionData, gamePageData, sdkStartEventData, propertiesID, queryBaseData)
}

// getBaseAnalyticsData 获取基础分析数据
func (r *analyticsSiteRepo) getBaseAnalyticsData(ctx context.Context, propertiesID, start, end string) (*analyticsdata.RunReportResponse, error) {
	resp, err := r.data.gs.analytics.Properties.RunReport("properties/"+propertiesID, &analyticsdata.RunReportRequest{
		DateRanges: []*analyticsdata.DateRange{
			{
				EndDate:   end,
				StartDate: start,
			},
		},
		Limit:      limit,
		Dimensions: []*analyticsdata.Dimension{{Name: DimensionHostName}, {Name: DimensionDate}},
		Metrics: []*analyticsdata.Metric{
			{Name: MetricActiveUsers},
			{Name: MetricAverageSessionDuration},
			{Name: MetricNewUsers},
			{Name: MetricScreenPageViews},
			{Name: MetricTotalUsers},
			{Name: MetricUserEngagementDuration},
			{Name: MetricScreenPageViewsPerUser},
		},
	}).Context(ctx).Do()
	if err != nil {
		return nil, fmt.Errorf("failed to get analytics data for property %s: %w", propertiesID, err)
	}
	return resp, nil
}

// DimensionDataCollection 维度数据集合
type DimensionDataCollection struct {
	StandardData *BatchDimensionResult
	CustomData   *BatchDimensionResult
}

// getAllDimensionData 获取所有维度数据
func (r *analyticsSiteRepo) getAllDimensionData(ctx context.Context, propertiesID, start, end string, queryBaseData bool) (*DimensionDataCollection, error) {
	if queryBaseData {
		return &DimensionDataCollection{}, nil
	}

	// 获取标准维度数据（Browser 和 Language）
	standardData, err := r.makeStandardDimensionData(ctx, propertiesID, start, end)
	if err != nil {
		return nil, fmt.Errorf("failed to get standard dimension data: %w", err)
	}

	// 获取自定义维度数据
	customDimensionTypes := []string{
		RequestTypeUA,
		RequestTypeDeviceResolution,
		RequestTypeDpi,
		RequestTypeAudioFp,
		RequestTypeCanvasFp,
		RequestTypeFontFp,
		RequestTypeTz,
		RequestTypeVisRes,
		RequestTypeGlMod,
		RequestTypeMiniLang,
	}

	customData, err := r.makeSiteCustomDimensionData(ctx, propertiesID, start, end, customDimensionTypes)
	if err != nil && !errors.Is(err, ErrorCustomDefinitionsNotConfigured) {
		return nil, fmt.Errorf("failed to get custom dimension data: %w", err)
	}

	// 如果 GA 没有配置自定义维度，不报错，但记录警告
	if errors.Is(err, ErrorCustomDefinitionsNotConfigured) {
		r.log.Warnf("Custom definitions not configured for property %s, skip querying custom dimension data", propertiesID)
		customData = NewBatchDimensionResult() // 创建空结果
	}

	return &DimensionDataCollection{
		StandardData: standardData,
		CustomData:   customData,
	}, nil
}

// buildAnalyticsDataList 构建分析数据列表
func (r *analyticsSiteRepo) buildAnalyticsDataList(
	baseResp *analyticsdata.RunReportResponse,
	dimensionData *DimensionDataCollection,
	gamePageData map[string]map[string]*GamePageData,
	sdkStartEventData map[string]map[string]*SdkStartEventData,
	propertiesID string,
	queryBaseData bool,
) ([]*biz.AnalyticsSiteData, error) {
	all := make([]*biz.AnalyticsSiteData, 0, len(baseResp.Rows))

	for _, row := range baseResp.Rows {
		// 验证数据完整性
		if len(row.DimensionValues) < 2 || len(row.MetricValues) < 7 {
			r.log.Warnf("Incomplete data row: dimensions=%d, metrics=%d", len(row.DimensionValues), len(row.MetricValues))
			continue
		}

		// 解析日期
		site := row.DimensionValues[DimensionIndexHostName].Value
		dateStr := row.DimensionValues[DimensionIndexDate].Value
		date, err := parseAnalyticsDate(dateStr)
		if err != nil {
			return nil, fmt.Errorf("failed to parse date %s: %w", dateStr, err)
		}

		// 提取指标数据
		activeUsers := cast.StringToInt(row.MetricValues[MetricIndexActiveUsers].Value)
		userEngagementDuration := calculateUserEngagementDuration(
			cast.StringToFloat(row.MetricValues[MetricIndexUserEngagementDuration].Value),
			activeUsers,
		)

		// 构建分析数据对象
		analyticsData := &biz.AnalyticsSiteData{
			Date:                               date,
			PropertiesID:                       propertiesID,
			Site:                               site,
			PageViews:                          cast.StringToInt(row.MetricValues[MetricIndexScreenPageViews].Value),
			ActiveUsers:                        activeUsers,
			TotalUsers:                         cast.StringToInt(row.MetricValues[MetricIndexTotalUsers].Value),
			NewUsers:                           cast.StringToInt(row.MetricValues[MetricIndexNewUsers].Value),
			AverageSessionDuration:             cast.StringToFloat(row.MetricValues[MetricIndexAverageSessionDuration].Value),
			AverageEngagementTimePerActiveUser: userEngagementDuration,
			ScreenPageViewsPerUser:             cast.StringToFloat(row.MetricValues[MetricIndexScreenPageViewsPerUser].Value),
		}

		// 添加游戏页数据
		if gamePageData[site] != nil && gamePageData[site][dateStr] != nil {
			gameData := gamePageData[site][dateStr]
			analyticsData.GamePageViews = gameData.PageViews
			analyticsData.GameTotalUsers = gameData.TotalUsers
			analyticsData.GameAverageSessionDuration = gameData.AverageSessionDuration
		}

		// 添加SDK启动事件数据
		if sdkStartEventData[site] != nil && sdkStartEventData[site][dateStr] != nil {
			sdkStartData := sdkStartEventData[site][dateStr]
			analyticsData.SdkStartEventCount = sdkStartData.EventCount
			analyticsData.SdkStartTotalUsers = sdkStartData.TotalUsers
		}

		// 提取维度数据并设置到分析数据对象中
		if !queryBaseData {
			r.extractAndSetDimensionData(analyticsData, dimensionData, site, dateStr)
		}

		all = append(all, analyticsData)
	}
	return all, nil
}

// extractAndSetDimensionData 提取维度数据并设置到分析数据对象中
func (r *analyticsSiteRepo) extractAndSetDimensionData(
	analyticsData *biz.AnalyticsSiteData,
	dimensionData *DimensionDataCollection,
	site, dateStr string,
) {
	key := generateSiteDateKey(site, dateStr)

	// 处理标准维度数据
	if dimensionData.StandardData != nil {
		for _, mapping := range dimensionMappings {
			// 只处理标准维度
			if mapping.RequestName == RequestNameStandardBrowserData || mapping.RequestName == RequestNameStandardLanguageData {
				if data, err := dimensionData.StandardData.GetData(mapping.RequestName); err == nil {
					if siteData, exists := data[key]; exists {
						mapping.FieldSetter(analyticsData, siteData)
					}
				}
			}
		}
	}

	// 处理自定义维度数据
	if dimensionData.CustomData != nil {
		for _, mapping := range dimensionMappings {
			// 只处理自定义维度
			if mapping.RequestName != RequestNameStandardBrowserData && mapping.RequestName != RequestNameStandardLanguageData {
				if data, err := dimensionData.CustomData.GetData(mapping.RequestName); err == nil {
					if siteData, exists := data[key]; exists {
						mapping.FieldSetter(analyticsData, siteData)
					}
				}
			}
		}
	}
}

func (r *analyticsSiteRepo) Save(ctx context.Context, analyticsSiteData []*biz.AnalyticsSiteData) error {
	bulk := make([]*ent.AnalyticsSiteCreate, 0)
	tx, err := r.data.db.Tx(ctx)
	if err != nil {
		return err
	}

	for i, data := range analyticsSiteData {
		bulk = append(bulk, tx.AnalyticsSite.Create().SetDate(data.Date).
			SetPageViews(data.PageViews).SetActiveUsers(data.ActiveUsers).
			SetTotalUsers(data.TotalUsers).SetNewUsers(data.NewUsers).SetPropertiesID(data.PropertiesID).
			SetSite(data.Site).SetLanguages(data.Languages).SetUa(data.Ua).SetDeviceResolution(data.DeviceResolution).
			SetDpi(data.Dpi).SetAudioFp(data.AudioFp).SetCanvasFp(data.CanvasFp).SetFontFp(data.FontFp).SetTz(data.Tz).SetVisRes(data.VisRes).SetGlMod(data.GlMod).
			SetAverageSessionDuration(data.AverageSessionDuration).SetAverageEngagementTimePerActiveUser(data.AverageEngagementTimePerActiveUser).
			SetScreenPageViewsPerUser(data.ScreenPageViewsPerUser).SetBrowser(data.Browsers).SetVisitPageCount(data.VisitPageCount).SetLang(data.MiniLang).
			// 设置游戏页相关字段
			SetGamePageViews(data.GamePageViews).SetGameTotalUsers(data.GameTotalUsers).SetGameAverageSessionDuration(data.GameAverageSessionDuration).
			// 设置SDK启动事件相关字段
			SetSdkStartEventCount(data.SdkStartEventCount).SetSdkStartTotalUsers(data.SdkStartTotalUsers))
		if (i+1)%1000 == 0 || i == len(analyticsSiteData)-1 {
			err := tx.AnalyticsSite.CreateBulk(bulk...).OnConflict(entsql.ConflictColumns("date", "site", "properties_id")).UpdateNewValues().Exec(ctx)
			if err != nil {
				return rollback(tx, err)
			}
			bulk = bulk[:0]
		}
	}
	return tx.Commit()
}

func (r *analyticsSiteRepo) SetSiteVisitPageCount(ctx context.Context, propertiesID string, start string, end string) error {
	s, err := time.Parse(time.DateOnly, start)
	if err != nil {
		return err
	}

	browsersResp, err := r.data.gs.analytics.Properties.RunReport(fmt.Sprintf("properties/%s", propertiesID), &analyticsdata.RunReportRequest{
		DateRanges: []*analyticsdata.DateRange{
			{
				StartDate: time.Date(s.Year(), s.Month(), 1, 0, 0, 0, 0, time.UTC).Format(time.DateOnly),
				EndDate:   end,
			},
		},
		Dimensions: []*analyticsdata.Dimension{{Name: DimensionHostName}, {Name: DimensionYearMonth}, {Name: DimensionUnifiedPagePathScreen}},
		Metrics: []*analyticsdata.Metric{
			{Name: MetricScreenPageViews}, {Name: MetricActiveUsers}, {Name: MetricScreenPageViewsPerUser},
			{Name: MetricAverageSessionDuration},
		},
		Limit: limit,
	}).Context(ctx).Do()
	if err != nil {
		return err
	}
	// key1 为site key2 为yearMonth
	siteVisitPageCountMap := make(map[string]map[string]int)
	for _, row := range browsersResp.Rows {
		key1 := row.DimensionValues[0].Value
		key2 := row.DimensionValues[1].Value
		if _, ok := siteVisitPageCountMap[key1]; !ok {
			siteVisitPageCountMap[key1] = make(map[string]int)
		}
		siteVisitPageCountMap[key1][key2] += 1
	}
	tx, err := r.data.db.Tx(ctx)
	if err != nil {
		return err
	}
	bulk := make([]*ent.AnalyticsSiteMonthCreate, 0)
	for site, data := range siteVisitPageCountMap {
		for yearMonth, count := range data {
			bulk = append(bulk, tx.AnalyticsSiteMonth.Create().SetSite(site).SetMonth(yearMonth).SetVisitPageCount(count).SetPropertiesID(propertiesID))
		}
	}
	err = tx.AnalyticsSiteMonth.CreateBulk(bulk...).OnConflict(entsql.ConflictColumns("site", "month", "properties_id")).UpdateNewValues().Exec(ctx)
	if err != nil {
		return rollback(tx, err)
	}
	return tx.Commit()
}

// getActiveUsersData 获取浏览器数据，使用JSONB数组展开和聚合
func (r *analyticsSiteRepo) getActiveUsersData(ctx context.Context, start time.Time, end time.Time) (map[string]map[string]int, error) {
	// 构建结果map: site -> browser_name -> total_active_users
	results := make(map[string]map[string]int)

	// 构建SQL查询，使用jsonb_array_elements展开browser数组
	query := `
		SELECT
			site,
			element->>'name' AS browser_name,
			SUM((element->>'active_users')::integer) AS total_active_users
		FROM analytics_sites,
			 jsonb_array_elements(browser) AS element
		WHERE jsonb_typeof(browser) = 'array'
		  AND element->>'name' IS NOT NULL
		  AND element->>'name' != ''
		  AND date >= $1
		  AND date <= $2
		GROUP BY element->>'name', site
	`

	// 执行原生SQL查询
	rows, err := r.data.stddb.QueryContext(ctx, query, start, end)
	if err != nil {
		r.log.Errorf("GetActiveUsersData query error: %v", err)
		return nil, err
	}
	defer rows.Close()

	// 扫描结果并构建嵌套map
	for rows.Next() {
		var site string
		var browserName sql.NullString
		var totalActiveUsers int
		err := rows.Scan(&site, &browserName, &totalActiveUsers)
		if err != nil {
			r.log.Errorf("GetActiveUsersData scan error: %v", err)
			return nil, err
		}

		// 跳过无效的浏览器名称
		if !browserName.Valid || browserName.String == "" {
			r.log.Warnf("GetBrowserData: skipping invalid browser name for site %s", site)
			continue
		}

		// 如果站点不存在，初始化内层map
		if results[site] == nil {
			results[site] = make(map[string]int)
		}

		// 设置浏览器数据
		results[site][browserName.String] = totalActiveUsers
	}

	if err = rows.Err(); err != nil {
		r.log.Errorf("GetActiveUsersData rows error: %v", err)
		return nil, err
	}

	return results, nil
}

// makeStandardDimensionData 获取标准维度数据（Browser 和 Language）
// 这些维度在所有 GA 属性中都可用，不依赖自定义维度配置
func (r *analyticsSiteRepo) makeStandardDimensionData(ctx context.Context, propertiesID, start, end string) (*BatchDimensionResult, error) {
	builder := NewDimensionRequestBuilder()

	// 添加标准维度请求
	builder.AddRequest(DimensionRequestConfig{
		Name: RequestNameStandardBrowserData,
		Dimensions: []*analyticsdata.Dimension{
			{Name: DimensionHostName},
			{Name: DimensionDate},
			{Name: DimensionBrowser},
		},
		Metrics: []*analyticsdata.Metric{
			{Name: MetricActiveUsers},
		},
		Limit:       limit,
		Description: "标准浏览器维度数据",
	})

	builder.AddRequest(DimensionRequestConfig{
		Name: RequestNameStandardLanguageData,
		Dimensions: []*analyticsdata.Dimension{
			{Name: DimensionHostName},
			{Name: DimensionDate},
			{Name: DimensionLanguage},
		},
		Metrics: []*analyticsdata.Metric{
			{Name: MetricActiveUsers},
		},
		Limit:       limit,
		Description: "标准语言维度数据",
	})

	return r.executeBatchDimensionRequests(ctx, propertiesID, start, end, builder)
}

// makeSiteCustomDimensionData 获取自定义维度数据
// 这些维度可能在某些 GA 属性中不存在，需要配置自定义维度
// 由于 Google Analytics Data API BatchRunReports 最多支持5个请求，需要分批处理
func (r *analyticsSiteRepo) makeSiteCustomDimensionData(ctx context.Context, propertiesID, start, end string, requestTypes []string) (*BatchDimensionResult, error) {
	// 将请求类型分批，每批最多 MaxBatchRequestSize 个
	batches := splitRequestTypesIntoBatches(requestTypes)
	if len(batches) == 0 {
		return NewBatchDimensionResult(), nil
	}

	var batchResults []*BatchDimensionResult

	// 处理每个批次
	for i, batch := range batches {
		r.log.Infof("Processing custom dimension batch %d/%d with %d requests", i+1, len(batches), len(batch))

		builder := NewDimensionRequestBuilder()

		// 根据请求类型动态添加自定义维度请求
		for _, requestType := range batch {
			switch requestType {
			case RequestTypeUA:
				builder.AddUARequest()
			case RequestTypeDeviceResolution:
				builder.AddDeviceResolutionRequest()
			case RequestTypeDpi:
				builder.AddDpiRequest()
			case RequestTypeAudioFp:
				builder.AddAudioFpRequest()
			case RequestTypeCanvasFp:
				builder.AddCanvasFpRequest()
			case RequestTypeFontFp:
				builder.AddFontFpRequest()
			case RequestTypeTz:
				builder.AddTzRequest()
			case RequestTypeVisRes:
				builder.AddVisResRequest()
			case RequestTypeGlMod:
				builder.AddGlModRequest()
			case RequestTypeMiniLang:
				builder.AddMiniLangRequest()
			// 标准维度已移至 makeStandardDimensionData 方法处理
			case RequestTypeBrowser:
				r.log.Warnf("Browser dimension should be handled by makeStandardDimensionData, skipping: %s", requestType)
			case RequestTypeLanguage:
				r.log.Warnf("Language dimension should be handled by makeStandardDimensionData, skipping: %s", requestType)
			default:
				r.log.Warnf("Unknown custom dimension request type: %s", requestType)
			}
		}

		// 执行当前批次的请求
		batchResult, err := r.executeBatchDimensionRequests(ctx, propertiesID, start, end, builder)
		if err != nil {
			return nil, fmt.Errorf("failed to execute batch %d: %w", i+1, err)
		}

		batchResults = append(batchResults, batchResult)
	}

	// 合并所有批次的结果
	mergedResult := mergeBatchDimensionResults(batchResults)
	r.log.Infof("Completed custom dimension data processing: %s", mergedResult.GetSummary())

	return mergedResult, nil
}

// executeBatchDimensionRequestsV2 执行批量维度请求
func (r *analyticsSiteRepo) executeBatchDimensionRequests(
	ctx context.Context,
	propertiesID, start, end string,
	builder *DimensionRequestBuilder,
) (*BatchDimensionResult, error) {
	startTime := time.Now()

	// 构建请求
	requests := builder.BuildRequests(start, end)
	configs := builder.GetConfigs()

	if len(requests) == 0 {
		return nil, fmt.Errorf("no requests to execute")
	}

	// 创建批量请求
	batchRequest := &analyticsdata.BatchRunReportsRequest{
		Requests: requests,
	}

	// 调用 BatchRunReports
	batchResp, err := r.data.gs.analytics.Properties.BatchRunReports(
		fmt.Sprintf("properties/%s", propertiesID),
		batchRequest,
	).Context(ctx).Do()
	if err != nil {
		var gErr *googleapi.Error
		if errors.As(err, &gErr) {
			if strings.HasSuffix(gErr.Message, ". For a list of valid dimensions and metrics, see https://developers.google.com/analytics/devguides/reporting/data/v1/api-schema ") {
				return nil, ErrorCustomDefinitionsNotConfigured
			}
		}

		return nil, fmt.Errorf("failed to get batch reports for property %s: %w", propertiesID, err)
	}

	// 处理响应
	processor := NewDimensionDataProcessor(r.log)
	result := processor.ProcessBatchResponse(batchResp, configs)
	result.ProcessTime = time.Since(startTime)

	return result, nil
}

// splitRequestTypesIntoBatches 将请求类型分批，每批最多包含 MaxBatchRequestSize 个请求
func splitRequestTypesIntoBatches(requestTypes []string) [][]string {
	if len(requestTypes) == 0 {
		return nil
	}

	var batches [][]string
	for i := 0; i < len(requestTypes); i += MaxBatchRequestSize {
		end := i + MaxBatchRequestSize
		if end > len(requestTypes) {
			end = len(requestTypes)
		}
		batches = append(batches, requestTypes[i:end])
	}

	return batches
}

// mergeBatchDimensionResults 合并多个批次的结果
func mergeBatchDimensionResults(results []*BatchDimensionResult) *BatchDimensionResult {
	if len(results) == 0 {
		return NewBatchDimensionResult()
	}

	if len(results) == 1 {
		return results[0]
	}

	merged := NewBatchDimensionResult()
	var totalProcessTime time.Duration

	for _, result := range results {
		// 合并结果
		for name, dimensionResult := range result.Results {
			merged.Results[name] = dimensionResult
		}

		// 累加统计信息
		merged.TotalRows += result.TotalRows
		merged.SuccessCount += result.SuccessCount
		merged.ErrorCount += result.ErrorCount
		totalProcessTime += result.ProcessTime
	}

	merged.ProcessTime = totalProcessTime
	return merged
}

// NewAnalyticsSiteRepo .
func NewAnalyticsSiteRepo(data *Data, logger log.Logger) biz.AnalyticsSiteRepo {
	return &analyticsSiteRepo{
		data: data,
		log:  log.NewHelper(log.With(logger, "module", "adsense-bot/data/analytics_site")),
	}
}
