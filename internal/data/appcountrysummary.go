package data

import (
	"context"
	"time"

	"github.com/go-kratos/kratos/v2/log"

	"git.minigame.vip/minicloud/service/adsense-bot/internal/biz"
	"git.minigame.vip/minicloud/service/adsense-bot/internal/data/ent"
	"git.minigame.vip/minicloud/service/adsense-bot/internal/data/ent/appcountrysummary"
)

type appCountrySummaryRepo struct {
	data *Data
	log  *log.Helper
}

func (r *appCountrySummaryRepo) Delete(ctx context.Context) error {
	now := time.Now()
	_, err := r.data.db.AppCountrySummary.Delete().Where(appcountrysummary.UpdateTimeNEQ(now)).Exec(ctx)
	if err != nil {
		return err
	}
	return nil
}

func (r *appCountrySummaryRepo) Save(ctx context.Context, appCountrySummary []*biz.AppCountrySummary) error {
	bulk := make([]*ent.AppCountrySummaryCreate, 0)
	tx, err := r.data.db.Tx(ctx)
	if err != nil {
		return err
	}
	for i, data := range appCountrySummary {
		bulk = append(bulk, tx.AppCountrySummary.Create().SetAppID(data.AppID).SetCountry(data.Country))
		if (i+1)%4000 == 0 || i == len(appCountrySummary)-1 {
			err := tx.AppCountrySummary.CreateBulk(bulk...).Exec(ctx)
			// _, err := tx.Channel.createBulk(bulk...).Save(ctx)
			if err != nil {
				return rollback(tx, err)
			}
			bulk = bulk[:0]
		}
	}

	return tx.Commit()
}

func NewAppCountrySummaryRepo(data *Data, logger log.Logger) biz.AppCountrySummaryRepo {
	return &appCountrySummaryRepo{
		data,
		log.NewHelper(log.With(logger, "module", "data/appCountrySummaryRepo")),
	}
}
