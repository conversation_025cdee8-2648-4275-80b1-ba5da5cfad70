package data

import (
	"context"

	"entgo.io/ent/dialect/sql"
	"github.com/go-kratos/kratos/v2/log"

	"git.minigame.vip/minicloud/service/adsense-bot/internal/biz"
	"git.minigame.vip/minicloud/service/adsense-bot/internal/data/ent"
)

type customChannelRepo struct {
	data *Data
	log  *log.Helper
}

func (r *customChannelRepo) CreateOrUpdate(ctx context.Context, channels []*biz.AdsenseCustomChannel) error {
	bulk := make([]*ent.CustomChannelsCreate, 0)
	tx, err := r.data.db.Tx(ctx)
	if err != nil {
		return err
	}
	for i, data := range channels {
		bulk = append(bulk, tx.CustomChannels.Create().SetName(data.Name).SetActive(data.Active).
			SetReportingDimensionID(data.ReportingDimensionId).SetDisplayName(data.DisplayName))
		if (i+1)%4000 == 0 || i == len(channels)-1 {
			err := tx.CustomChannels.CreateBulk(bulk...).OnConflict(sql.ConflictColumns("name", "reporting_dimension_id", "display_name")).UpdateNewValues().Exec(ctx)
			// _, err := tx.Channel.createBulk(bulk...).Save(ctx)
			if err != nil {
				return rollback(tx, err)
			}
			bulk = bulk[:0]
		}
	}

	return tx.Commit()
}

func NewCustomChannelRepo(data *Data, logger log.Logger) biz.AdsenseCustomChannelRepo {
	return &customChannelRepo{
		data,
		log.NewHelper(log.With(logger, "module", "data/repo/custom_channel")),
	}
}
