package data

import (
	"context"
	"time"

	"entgo.io/ent/dialect/sql"

	"git.minigame.vip/minicloud/service/adsense-bot/internal/biz"
	"git.minigame.vip/minicloud/service/adsense-bot/internal/data/ent"
	"git.minigame.vip/minicloud/service/adsense-bot/internal/data/ent/admanagersite"

	"github.com/go-kratos/kratos/v2/log"
)

type adManagerSiteRepo struct {
	data *Data
	log  *log.Helper
}

func (r *adManagerSiteRepo) Query(ctx context.Context, start time.Time, end time.Time) ([]*biz.AdManagerSite, error) {
	all, err := r.data.db.AdManagerSite.Query().Where(admanagersite.DateGTE(start), admanagersite.DateLTE(end)).All(ctx)
	if err != nil {
		return nil, err
	}
	res := make([]*biz.AdManagerSite, 0, len(all))
	for _, v := range all {
		res = append(res, &biz.AdManagerSite{
			Date:                  v.Date,
			ChildNetworkCode:      v.ChildNetworkCode,
			Site:                  v.Site,
			ChildNetworkID:        v.ChildNetworkID,
			AdExchangeImpressions: v.AdExchangeImpressions,
			AdExchangeClicks:      v.AdExchangeClicks,
			AdExchangeCtr:         v.AdExchangeCtr,
			AdExchangeRevenue:     v.AdExchangeRevenue,
			AdExchangeAverageEcpm: v.AdExchangeAverageEcpm,
			AdExchangeAdRequests:  v.AdExchangeAdRequests,
			AdExchangeMatchRate:   v.AdExchangeMatchRate,
		})
	}
	return res, nil
}

func (r *adManagerSiteRepo) Save(ctx context.Context, datas []*biz.AdManagerSite) error {
	bulk := make([]*ent.AdManagerSiteCreate, 0)
	tx, err := r.data.db.Tx(ctx)
	if err != nil {
		return err
	}
	//s, e := date.GetDateRange("LAST_7_DAYS", time.Now())
	//_, err = tx.Site.Delete().Where(site.DateGTE(s), site.DateLTE(e)).Exec(ctx)
	//if err != nil {
	//	return rollback(tx, err)
	//}
	for i, data := range datas {
		bulk = append(bulk, tx.AdManagerSite.Create().SetDate(data.Date).SetSite(data.Site).
			SetChildNetworkCode(data.ChildNetworkCode).SetChildNetworkID(data.ChildNetworkID).
			SetAdExchangeImpressions(data.AdExchangeImpressions).SetAdExchangeClicks(data.AdExchangeClicks).
			SetAdExchangeCtr(data.AdExchangeCtr).SetAdExchangeRevenue(data.AdExchangeRevenue).
			SetAdExchangeAverageEcpm(data.AdExchangeAverageEcpm).SetAdExchangeAdRequests(data.AdExchangeAdRequests).
			SetAdExchangeMatchRate(data.AdExchangeMatchRate),
		)
		if (i+1)%4000 == 0 || i == len(datas)-1 {
			err := tx.AdManagerSite.CreateBulk(bulk...).
				OnConflict(sql.ConflictColumns("date", "child_network_code", "site", "child_network_id")).UpdateNewValues().Exec(ctx)
			if err != nil {
				return rollback(tx, err)
			}
			bulk = bulk[:0]
		}
	}

	return tx.Commit()
}

func NewAdManagerSiteRepo(data *Data, logger log.Logger) biz.AdManagerSiteRepo {
	return &adManagerSiteRepo{
		data,
		log.NewHelper(log.With(logger, "module", "data/ad_manager")),
	}
}
