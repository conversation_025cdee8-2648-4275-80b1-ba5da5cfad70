package data

import (
	"context"
	"fmt"

	"entgo.io/ent/dialect/sql"
	"github.com/go-kratos/kratos/v2/log"

	"git.minigame.vip/minicloud/service/adsense-bot/internal/biz"
	"git.minigame.vip/minicloud/service/adsense-bot/internal/data/ent"
)

type siteCountryRepo struct {
	data *Data
	log  *log.Helper
}

func (r *siteCountryRepo) CreateHKDData(ctx context.Context, datas []*biz.AdsenseSiteCountryData) error {
	bulk := make([]*ent.SiteCountryHKDCreate, 0)
	tx, err := r.data.db.Tx(ctx)
	if err != nil {
		return err
	}
	//s, e := date.GetDateRange("LAST_7_DAYS", time.Now())
	//_, err = tx.SiteCountry.Delete().Where(sitecountry.DateGTE(s), sitecountry.DateLTE(e)).Exec(ctx)
	//if err != nil {
	//	return rollback(tx, err)
	//}
	for di, data := range datas {
		bulk = append(bulk, tx.SiteCountryHKD.Create().SetDate(data.Date).SetSite(data.Site).
			SetCountry(data.Country).SetEstimatedEarnings(data.EstimatedEarnings).
			SetPageViews(data.PageViews).SetPageViewsRpm(data.PageViewsRpm).
			SetImpressions(data.IMPRESSIONS).SetImpressionsRpm(data.ImpressionsRpm).
			SetAdRequestsCoverage(data.AdRequestsCoverage).SetClicks(data.CLICKS).
			SetAdRequests(data.AdRequests).SetImpressionsCtr(data.ImpressionsCtr).
			SetActiveViewViewability(data.ActiveViewViewability).
			SetMatchedAdRequests(data.MatchedAdRequests).
			SetCostPerClick(data.CostPerClick))
		// 3999 7999
		if (di+1)%4000 == 0 || di == len(datas)-1 {
			err = tx.SiteCountryHKD.CreateBulk(bulk...).OnConflict(sql.ConflictColumns("date", "site", "country")).UpdateNewValues().Exec(ctx)

			// _, err := tx.SiteCountry.createBulk(bulk...).Save(ctx)
			if err != nil {
				return rollback(tx, err)
			}
			bulk = bulk[:0]
		}
	}
	err = tx.Commit()
	if err != nil {
		return err
	}
	return nil
}

func (r *siteCountryRepo) Create(ctx context.Context, datas []*biz.AdsenseSiteCountryData) error {
	bulk := make([]*ent.SiteCountryCreate, 0)
	tx, err := r.data.db.Tx(ctx)
	if err != nil {
		return err
	}
	//s, e := date.GetDateRange("LAST_7_DAYS", time.Now())
	//_, err = tx.SiteCountry.Delete().Where(sitecountry.DateGTE(s), sitecountry.DateLTE(e)).Exec(ctx)
	//if err != nil {
	//	return rollback(tx, err)
	//}
	for di, data := range datas {
		bulk = append(bulk, tx.SiteCountry.Create().SetDate(data.Date).SetSite(data.Site).
			SetCountry(data.Country).SetEstimatedEarnings(data.EstimatedEarnings).
			SetPageViews(data.PageViews).SetPageViewsRpm(data.PageViewsRpm).
			SetImpressions(data.IMPRESSIONS).SetImpressionsRpm(data.ImpressionsRpm).
			SetAdRequestsCoverage(data.AdRequestsCoverage).SetClicks(data.CLICKS).
			SetAdRequests(data.AdRequests).SetImpressionsCtr(data.ImpressionsCtr).
			SetActiveViewViewability(data.ActiveViewViewability).
			SetMatchedAdRequests(data.MatchedAdRequests).
			SetCostPerClick(data.CostPerClick))
		// 3999 7999
		if (di+1)%4000 == 0 || di == len(datas)-1 {
			err = tx.SiteCountry.CreateBulk(bulk...).OnConflict(sql.ConflictColumns("date", "site", "country")).UpdateNewValues().Exec(ctx)

			// _, err := tx.SiteCountry.createBulk(bulk...).Save(ctx)
			if err != nil {
				return rollback(tx, err)
			}
			bulk = bulk[:0]
		}
	}
	err = tx.Commit()
	if err != nil {
		return err
	}
	return nil
}

func NewSiteCountryRepo(data *Data, logger log.Logger) biz.AdSenseSiteCountryRepo {
	return &siteCountryRepo{
		data,
		log.NewHelper(log.With(logger, "module", "data/site/country")),
	}
}
func rollback(tx *ent.Tx, err error) error {
	if rerr := tx.Rollback(); rerr != nil {
		err = fmt.Errorf("%w: %v", err, rerr)
	}
	return err
}
