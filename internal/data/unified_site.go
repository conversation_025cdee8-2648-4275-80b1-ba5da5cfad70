package data

import (
	"context"

	"entgo.io/ent/dialect/sql"
	"github.com/go-kratos/kratos/v2/log"

	"git.minigame.vip/minicloud/service/adsense-bot/internal/biz"
	"git.minigame.vip/minicloud/service/adsense-bot/internal/data/ent"
)

type unifiedSiteRepo struct {
	data *Data
	log  *log.Helper
}

func (r *unifiedSiteRepo) Create(ctx context.Context, datas []*biz.AdsenseSiteData) error {
	bulk := make([]*ent.UnifiedSiteCreate, 0)
	tx, err := r.data.db.Tx(ctx)
	if err != nil {
		return err
	}

	for i, data := range datas {
		bulk = append(bulk, tx.UnifiedSite.Create().SetDate(data.Date).SetSite(data.Site).
			SetEstimatedEarnings(data.EstimatedEarnings).
			SetImpressions(data.IMPRESSIONS).SetImpressionsRpm(data.ImpressionsRpm).
			SetClicks(data.CLICKS).
			SetAdRequests(data.AdRequests).SetImpressionsCtr(data.ImpressionsCtr))
		if (i+1)%4000 == 0 || i == len(datas)-1 {
			err := tx.UnifiedSite.CreateBulk(bulk...).OnConflict(sql.ConflictColumns("date", "site")).UpdateNewValues().Exec(ctx)

			if err != nil {
				return rollback(tx, err)
			}
			bulk = bulk[:0]
		}
	}

	return tx.Commit()
}

func NewUnifiedSiteRepo(data *Data, logger log.Logger) biz.UnifiedSiteRepo {
	return &unifiedSiteRepo{
		data,
		log.NewHelper(log.With(logger, "module", "data/unified_site")),
	}
}
