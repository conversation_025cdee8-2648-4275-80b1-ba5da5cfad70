package data

import (
	"context"
	"encoding/json"
	"errors"
	"fmt"
	"io"
	"math"
	"net/http"
	"strconv"
	"strings"
	"time"

	"entgo.io/ent/dialect/sql"
	"github.com/go-kratos/kratos/v2/log"

	"git.minigame.vip/minicloud/service/adsense-bot/internal/biz"
	"git.minigame.vip/minicloud/service/adsense-bot/internal/conf"
	"git.minigame.vip/minicloud/service/adsense-bot/internal/data/ent"
	"git.minigame.vip/minicloud/service/adsense-bot/internal/data/ent/siteadformat"
	"git.minigame.vip/minicloud/service/adsense-bot/internal/data/ent/sitecountryadformatpartition"
)

type tgAdRepo struct {
	data        *Data
	log         *log.Helper
	tgAdDataUrl string
}

var (
	cClient = http.Client{Transport: &http.Transport{
		MaxIdleConns:        100,              // 最大空闲连接数
		MaxIdleConnsPerHost: 10,               // 每个主机的最大空闲连接数
		IdleConnTimeout:     30 * time.Second, // 空闲连接的超时时间
	}}
)

type getReportingReply struct {
	Code    int    `json:"code"`
	Message string `json:"message"`
	Data    struct {
		List []struct {
			Acc         string  `json:"acc"`
			CountryCode string  `json:"countryCode"`
			Cpm         float64 `json:"cpm"`
			CreateDate  string  `json:"createDate"`
			Day         string  `json:"day"`
			Earned      float64 `json:"earned"`
			Impressions string  `json:"impressions"`
			ProjectId   string  `json:"projectId"`
			Site        string  `json:"site"`
			UnitId      int     `json:"unitId"`
			UpdateDate  string  `json:"updateDate"`
		} `json:"list"`
		Total int `json:"total"`
	} `json:"data"`
	Ts  int    `json:"ts"`
	Tid string `json:"tid"`
}

func (t *tgAdRepo) getTgAdData(ctx context.Context, countryMap map[string]string) ([]*biz.AdsenseData, error) {
	var allData []*biz.AdsenseData
	tgAdSiteCountryAdFormatMap := make(map[string]*biz.AdsenseData)
	cstSh, _ := time.LoadLocation("Asia/Shanghai")

	currentPage := 1
	totalPages := 1 // 初始设为1，第一次请求后会更新
	unKnownRegionList := make([]struct {
		Acc         string  `json:"acc"`
		CountryCode string  `json:"countryCode"`
		Cpm         float64 `json:"cpm"`
		CreateDate  string  `json:"createDate"`
		Day         string  `json:"day"`
		Earned      float64 `json:"earned"`
		Impressions string  `json:"impressions"`
		ProjectId   string  `json:"projectId"`
		Site        string  `json:"site"`
		UnitId      int     `json:"unitId"`
		UpdateDate  string  `json:"updateDate"`
	}, 0)
	// 分页循环获取所有数据
	for currentPage <= totalPages {
		url := fmt.Sprintf("%s/v1/televssvc/api/reporting?page=%d", t.tgAdDataUrl, currentPage)
		method := http.MethodGet

		req, err := http.NewRequestWithContext(ctx, method, url, nil)
		if err != nil {
			return nil, err
		}
		res, err := cClient.Do(req)
		if err != nil {
			return nil, err
		}

		// 使用defer语句需要注意：每次循环创建一个新的res，需要在循环内关闭
		// 创建一个局部变量以避免延迟关闭的问题
		bodyCloseFunc := res.Body.Close
		defer func() {
			if bodyCloseFunc != nil {
				bodyCloseFunc()
			}
		}()

		if res.StatusCode != http.StatusOK {
			return nil, errors.New("get tg ad data failed,code is " + strconv.Itoa(res.StatusCode))
		}

		var resp getReportingReply
		body, err := io.ReadAll(res.Body)
		bodyCloseFunc = nil // 已读取完毕，可以关闭
		res.Body.Close()

		if err != nil {
			return nil, err
		}
		if err := json.Unmarshal(body, &resp); err != nil {
			return nil, err
		}

		// 计算总页数：仅在第一页时计算
		if currentPage == 1 && len(resp.Data.List) > 0 {
			pageSize := len(resp.Data.List) // 根据第一页返回的数据量确定每页大小
			totalPages = int(math.Ceil(float64(resp.Data.Total) / float64(pageSize)))

			// 如果计算出的总页数为0（发生在Total=0的情况），则将其设为1
			if totalPages == 0 {
				totalPages = 1
			}

			t.log.Info(fmt.Sprintf("Total records: %d, Page size: %d, Total pages: %d",
				resp.Data.Total, pageSize, totalPages))
		}

		// 处理当前页数据

		//pageData := make([]*biz.AdsenseData, 0)

		for _, v := range resp.Data.List {
			impressions, err := strconv.Atoi(v.Impressions)
			if err != nil {
				return nil, errors.New("impressions convert failed, impressions is " + v.Impressions)
			}
			day, err := strconv.ParseInt(v.Day, 10, 64)
			if err != nil {
				return nil, errors.New("day convert failed, day is " + v.Day)
			}
			if v.Site == "zombie.televs.com" {
				v.Site = "televs.com"
			}
			date := time.Unix(day, 0)
			country, ok := countryMap[strings.ToUpper(v.CountryCode)]
			if !ok {
				t.log.Errorf("get tg ad data failed, countryCode is " + v.CountryCode)

				if v.CountryCode == "none" || v.CountryCode == "*" {
					unKnownRegionList = append(unKnownRegionList, v)
					continue
					//country = "Unknown Region"
				} else {
					country = v.CountryCode
				}
			}

			key := fmt.Sprintf("%s_%s_%s_%s", date, v.Site, v.Acc, country)

			if _, ok := tgAdSiteCountryAdFormatMap[key]; !ok {
				tgAdSiteCountryAdFormatMap[key] = &biz.AdsenseData{
					Date:              time.Date(date.Year(), date.Month(), date.Day(), 0, 0, 0, 0, cstSh),
					AdFormat:          strings.ToUpper(v.Acc),
					Country:           country,
					Site:              v.Site,
					EstimatedEarnings: v.Earned,
					IMPRESSIONS:       impressions,
					ImpressionsRpm:    v.Cpm,
				}
			} else {
				tgAdSiteCountryAdFormatMap[key].EstimatedEarnings += v.Earned
				tgAdSiteCountryAdFormatMap[key].IMPRESSIONS += impressions
			}
			//pageData = append(pageData, &biz.AdsenseData{
			//	Date:              time.Date(date.Year(), date.Month(), date.Day(), 0, 0, 0, 0, cstSh),
			//	AdFormat:          strings.ToUpper(v.Acc),
			//	Country:           country,
			//	Site:              v.Site,
			//	EstimatedEarnings: v.Earned,
			//	IMPRESSIONS:       impressions,
			//	ImpressionsRpm:    v.Cpm,
			//})
		}

		// 将当前页数据添加到总结果中
		//allData = append(allData, pageData...)

		// 如果当前页没有数据，说明已经获取完所有数据，退出循环
		if len(resp.Data.List) == 0 {
			break
		}

		// 进入下一页
		currentPage++
	}
	region, err := makeUnKnownRegion(unKnownRegionList)
	if err != nil {
		return nil, err
	}
	for _, v := range tgAdSiteCountryAdFormatMap {
		if v.IMPRESSIONS == 0 {
			v.ImpressionsRpm = 0
		} else {
			v.ImpressionsRpm = v.EstimatedEarnings /
				float64(v.IMPRESSIONS) * 1000
		}
		allData = append(allData, v)
	}
	allData = append(allData, region...)

	return allData, nil
}

func makeUnKnownRegion(list []struct {
	Acc         string  `json:"acc"`
	CountryCode string  `json:"countryCode"`
	Cpm         float64 `json:"cpm"`
	CreateDate  string  `json:"createDate"`
	Day         string  `json:"day"`
	Earned      float64 `json:"earned"`
	Impressions string  `json:"impressions"`
	ProjectId   string  `json:"projectId"`
	Site        string  `json:"site"`
	UnitId      int     `json:"unitId"`
	UpdateDate  string  `json:"updateDate"`
}) ([]*biz.AdsenseData, error) {
	allData := make([]*biz.AdsenseData, 0)
	m := make(map[string]*biz.AdsenseData)
	cstSh, _ := time.LoadLocation("Asia/Shanghai")

	for _, v := range list {
		impressions, err := strconv.Atoi(v.Impressions)
		if err != nil {
			return nil, errors.New("impressions convert failed, impressions is " + v.Impressions)
		}
		day, err := strconv.ParseInt(v.Day, 10, 64)
		if err != nil {
			return nil, errors.New("day convert failed, day is " + v.Day)
		}
		date := time.Unix(day, 0)
		country := "Unknown Region"
		key := fmt.Sprintf("%s_%s_%s", date, v.Site, v.Acc)
		if _, ok := m[key]; !ok {
			m[key] = &biz.AdsenseData{
				Date:              time.Date(date.Year(), date.Month(), date.Day(), 0, 0, 0, 0, cstSh),
				AdFormat:          strings.ToUpper(v.Acc),
				Country:           country,
				Site:              v.Site,
				EstimatedEarnings: v.Earned,
				IMPRESSIONS:       impressions,
				ImpressionsRpm:    v.Cpm,
			}
		} else {
			m[key].EstimatedEarnings += v.Earned
			m[key].IMPRESSIONS += impressions
			if m[key].IMPRESSIONS == 0 {
				m[key].ImpressionsRpm = 0
			} else {
				m[key].ImpressionsRpm = m[key].EstimatedEarnings / float64(m[key].IMPRESSIONS) * 1000
			}
		}
	}
	for _, v := range m {
		allData = append(allData, v)
	}
	return allData, nil
}
func (t *tgAdRepo) Save(ctx context.Context, countryMap map[string]string) error {
	siteCountryAdFormatData, err := t.getTgAdData(ctx, countryMap)
	if err != nil {
		return err
	}
	siteAdformatData, err := t.getMergeData(siteCountryAdFormatData)
	if err != nil {
		return err
	}

	return t.save(ctx, siteCountryAdFormatData, siteAdformatData)
}

func (t *tgAdRepo) save(ctx context.Context, datas, siteAdformatData []*biz.AdsenseData) error {
	bulk := make([]*ent.SiteCountryAdFormatPartitionCreate, 0)
	tx, err := t.data.db.Tx(ctx)
	if err != nil {
		return err
	}
	_, err = tx.SiteCountryAdFormatPartition.Delete().Where(sitecountryadformatpartition.AdFormat("GIGA")).Exec(ctx)
	if err != nil {
		return rollback(tx, err)
	}

	for i, data := range datas {
		bulk = append(bulk, tx.SiteCountryAdFormatPartition.Create().
			SetDate(data.Date).
			SetSite(data.Site).
			SetCountry(data.Country).
			SetEstimatedEarnings(data.EstimatedEarnings).
			SetPageViews(data.PageViews).
			SetPageViewsRpm(data.PageViewsRpm).
			SetAdFormat(data.AdFormat).
			SetImpressions(data.IMPRESSIONS).
			SetImpressionsRpm(data.ImpressionsRpm).
			SetAdRequestsCoverage(data.AdRequestsCoverage).
			SetClicks(data.CLICKS).
			SetAdRequests(data.AdRequests).
			SetImpressionsCtr(data.ImpressionsCtr).
			SetActiveViewViewability(data.ActiveViewViewability).
			SetMatchedAdRequests(data.MatchedAdRequests).
			SetCostPerClick(data.CostPerClick))
		if (i+1)%3500 == 0 || i == len(datas)-1 {
			err = tx.SiteCountryAdFormatPartition.CreateBulk(bulk...).OnConflict(sql.ConflictColumns("date", "site", "country", "ad_format")).UpdateNewValues().Exec(ctx)
			if err != nil {
				return rollback(tx, err)
			}
			bulk = bulk[:0]
		}
	}
	//err = t.saveTgSite(ctx, siteData, tx)
	//if err != nil {
	//	return err
	//}
	//err = t.saveTgSiteCountry(ctx, siteCountryData, tx)
	//if err != nil {
	//	return err
	//}
	err = t.saveTgSiteAdFormat(ctx, siteAdformatData, tx)
	if err != nil {
		return rollback(tx, err)
	}
	return tx.Commit()
}

func (t *tgAdRepo) saveTgSiteCountry(ctx context.Context, siteData []*biz.AdsenseData, tx *ent.Tx) error {
	siteBulk := make([]*ent.SiteCountryCreate, 0)

	for i, data := range siteData {
		siteBulk = append(siteBulk, tx.SiteCountry.Create().
			SetDate(data.Date).
			SetSite(data.Site).
			SetEstimatedEarnings(data.EstimatedEarnings).
			SetPageViews(data.PageViews).
			SetPageViewsRpm(data.PageViewsRpm).
			SetCountry(data.Country).
			SetImpressions(data.IMPRESSIONS).
			SetImpressionsRpm(data.ImpressionsRpm).
			SetAdRequestsCoverage(data.AdRequestsCoverage).
			SetClicks(data.CLICKS).
			SetAdRequests(data.AdRequests).
			SetImpressionsCtr(data.ImpressionsCtr).
			SetActiveViewViewability(data.ActiveViewViewability).
			SetMatchedAdRequests(data.MatchedAdRequests).
			SetCostPerClick(data.CostPerClick))
		if (i+1)%3500 == 0 || i == len(siteData)-1 {
			err := tx.SiteCountry.CreateBulk(siteBulk...).OnConflict(sql.ConflictColumns("date", "site", "country")).UpdateNewValues().Exec(ctx)
			if err != nil {
				return rollback(tx, err)
			}
			siteBulk = siteBulk[:0]
		}
	}
	return nil
}

func (t *tgAdRepo) saveTgSite(ctx context.Context, siteData []*biz.AdsenseData, tx *ent.Tx) error {
	siteBulk := make([]*ent.SiteCreate, 0)

	for i, data := range siteData {
		siteBulk = append(siteBulk, tx.Site.Create().
			SetDate(data.Date).
			SetSite(data.Site).
			SetEstimatedEarnings(data.EstimatedEarnings).
			SetPageViews(data.PageViews).
			SetPageViewsRpm(data.PageViewsRpm).
			SetImpressions(data.IMPRESSIONS).
			SetImpressionsRpm(data.ImpressionsRpm).
			SetAdRequestsCoverage(data.AdRequestsCoverage).
			SetClicks(data.CLICKS).
			SetAdRequests(data.AdRequests).
			SetImpressionsCtr(data.ImpressionsCtr).
			SetActiveViewViewability(data.ActiveViewViewability).
			SetMatchedAdRequests(data.MatchedAdRequests).
			SetCostPerClick(data.CostPerClick))
		if (i+1)%3500 == 0 || i == len(siteData)-1 {
			err := tx.Site.CreateBulk(siteBulk...).OnConflict(sql.ConflictColumns("date", "site")).UpdateNewValues().Exec(ctx)
			if err != nil {
				return rollback(tx, err)
			}
			siteBulk = siteBulk[:0]
		}
	}
	return nil
}

func (t *tgAdRepo) getMergeData(data []*biz.AdsenseData) ([]*biz.AdsenseData, error) {
	//siteMap := make(map[string]*biz.AdsenseData)
	//siteCountryMap := make(map[string]*biz.AdsenseData)
	siteAdFormatMap := make(map[string]*biz.AdsenseData)
	for _, v := range data {
		//if _, ok := siteMap[v.Site]; !ok {
		//	siteMap[v.Site] = &biz.AdsenseData{
		//		Date:              v.Date,
		//		Site:              v.Site,
		//		EstimatedEarnings: v.EstimatedEarnings,
		//		IMPRESSIONS:       v.IMPRESSIONS,
		//		ImpressionsRpm:    v.ImpressionsRpm,
		//	}
		//} else {
		//	siteMap[v.Site].EstimatedEarnings += v.EstimatedEarnings
		//	siteMap[v.Site].IMPRESSIONS += v.IMPRESSIONS
		//	if siteMap[v.Site].IMPRESSIONS == 0 {
		//		siteMap[v.Site].ImpressionsRpm = 0
		//	} else {
		//		siteMap[v.Site].ImpressionsRpm = siteMap[v.Site].EstimatedEarnings / float64(siteMap[v.Site].IMPRESSIONS) * 1000
		//	}
		//}
		//if _, ok := siteCountryMap[v.Site+"_"+v.Country]; !ok {
		//	siteCountryMap[v.Site+"_"+v.Country] = &biz.AdsenseData{
		//		Date:              v.Date,
		//		Site:              v.Site,
		//		Country:           v.Country,
		//		EstimatedEarnings: v.EstimatedEarnings,
		//		IMPRESSIONS:       v.IMPRESSIONS,
		//		ImpressionsRpm:    v.ImpressionsRpm,
		//	}
		//} else {
		//	siteCountryMap[v.Site+"_"+v.Country].EstimatedEarnings += v.EstimatedEarnings
		//	siteCountryMap[v.Site+"_"+v.Country].IMPRESSIONS += v.IMPRESSIONS
		//	if siteCountryMap[v.Site+"_"+v.Country].IMPRESSIONS == 0 {
		//		siteCountryMap[v.Site+"_"+v.Country].ImpressionsRpm = 0
		//	} else {
		//		siteCountryMap[v.Site+"_"+v.Country].ImpressionsRpm = siteCountryMap[v.Site+"_"+v.Country].EstimatedEarnings / float64(siteCountryMap[v.Site+"_"+v.Country].IMPRESSIONS) * 1000
		//	}
		//}
		key := fmt.Sprintf("%s_%s_%s", v.Site, v.AdFormat, v.Date)
		if _, ok := siteAdFormatMap[key]; !ok {
			siteAdFormatMap[key] = &biz.AdsenseData{
				Date:              v.Date,
				Site:              v.Site,
				AdFormat:          v.AdFormat,
				EstimatedEarnings: v.EstimatedEarnings,
				IMPRESSIONS:       v.IMPRESSIONS,
				ImpressionsRpm:    v.ImpressionsRpm,
			}
		} else {
			siteAdFormatMap[key].EstimatedEarnings += v.EstimatedEarnings
			siteAdFormatMap[key].IMPRESSIONS += v.IMPRESSIONS
			if siteAdFormatMap[key].IMPRESSIONS == 0 {
				siteAdFormatMap[key].ImpressionsRpm = 0
			} else {
				siteAdFormatMap[key].ImpressionsRpm = siteAdFormatMap[key].EstimatedEarnings / float64(siteAdFormatMap[key].IMPRESSIONS) * 1000
			}
		}
	}
	//site := make([]*biz.AdsenseData, 0)
	//siteCountry := make([]*biz.AdsenseData, 0)
	siteAdFormat := make([]*biz.AdsenseData, 0)
	//for _, v := range siteMap {
	//	site = append(site, v)
	//}
	//for _, v := range siteCountryMap {
	//	siteCountry = append(siteCountry, v)
	//}
	for _, v := range siteAdFormatMap {
		siteAdFormat = append(siteAdFormat, v)
	}
	return siteAdFormat, nil

}

func (t *tgAdRepo) saveTgSiteAdFormat(ctx context.Context, siteData []*biz.AdsenseData, tx *ent.Tx) error {
	siteBulk := make([]*ent.SiteAdFormatCreate, 0)
	_, err := tx.SiteAdFormat.Delete().Where(siteadformat.AdFormat("GIGA")).Exec(ctx)
	if err != nil {
		return err
	}
	for i, data := range siteData {
		siteBulk = append(siteBulk, tx.SiteAdFormat.Create().
			SetDate(data.Date).
			SetSite(data.Site).
			SetAdFormat(data.AdFormat).
			SetEstimatedEarnings(data.EstimatedEarnings).
			SetPageViews(data.PageViews).
			SetPageViewsRpm(data.PageViewsRpm).
			SetImpressions(data.IMPRESSIONS).
			SetImpressionsRpm(data.ImpressionsRpm).
			SetAdRequestsCoverage(data.AdRequestsCoverage).
			SetClicks(data.CLICKS).
			SetAdRequests(data.AdRequests).
			SetImpressionsCtr(data.ImpressionsCtr).
			SetActiveViewViewability(data.ActiveViewViewability).
			SetMatchedAdRequests(data.MatchedAdRequests).
			SetCostPerClick(data.CostPerClick))

		if (i+1)%3500 == 0 || i == len(siteData)-1 {
			err := tx.SiteAdFormat.CreateBulk(siteBulk...).OnConflict(sql.ConflictColumns("date", "site", "ad_format")).UpdateNewValues().Exec(ctx)
			if err != nil {
				return err
			}
			siteBulk = siteBulk[:0]
		}
	}
	return nil
}

// NewTgAdRepo .
func NewTgAdRepo(tgAdConf *conf.TgAd, data *Data, logger log.Logger) biz.TgAdRepo {
	return &tgAdRepo{
		data:        data,
		tgAdDataUrl: tgAdConf.GetUrl(),
		log:         log.NewHelper(log.With(logger, "module", "adsense-bot/data/tg_ad")),
	}
}
