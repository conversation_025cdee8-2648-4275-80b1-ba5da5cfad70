package data

import (
	"context"
	"encoding/csv"
	"fmt"
	"io"
	"net/http"
	"net/url"
	"strconv"

	"git.minigame.vip/minicloud/service/adsense-bot/internal/biz"
	"git.minigame.vip/minicloud/service/adsense-bot/internal/conf"
	"git.minigame.vip/minicloud/service/adsense-bot/pkg/date"

	"github.com/go-kratos/kratos/v2/log"
	"google.golang.org/api/adsense/v2"
	"google.golang.org/api/googleapi"
)

type adsenseRepo struct {
	data          *Data
	log           *log.Helper
	adsenseConfig []*conf.GoogleConfig
}

func (r *adsenseRepo) Generate(ctx context.Context, metrics, orderBy, dimensions, filters []string, dateRange, currencyCode string) (*biz.Adsense, error) {
	//metrics := []string{"ESTIMATED_EARNINGS", "PAGE_VIEWS", "PAGE_VIEWS_RPM", "IMPRESSIONS", "IMPRESSIONS_RPM", "AD_REQUESTS_COVERAGE", "CLICKS", "AD_REQUESTS", "IMPRESSIONS_CTR", "ACTIVE_VIEW_VIEWABILITY"}
	do, err := r.data.gs.adsense.Accounts.Reports.Generate(fmt.Sprintf("accounts/%s", r.adsenseConfig[0].PubID)).
		Metrics(metrics...).
		Dimensions(dimensions...).
		CurrencyCode(currencyCode).
		//StartDateYear(int64(r.adsense.StartYear)).StartDateMonth(int64(r.adsense.StartMonth)).StartDateDay(int64(r.adsense.StartDay)).
		//EndDateMonth(int64(r.adsense.GetEndMonth())).EndDateYear(int64(r.adsense.EndYear)).EndDateDay(int64(r.adsense.EndDay)).
		//OrderBy("-AD_REQUESTS_COVERAGE").
		OrderBy(orderBy...).
		Filters(filters...).
		DateRange(dateRange).Context(ctx).Do()
	if err != nil {
		if gErr, ok := err.(*googleapi.Error); ok {
			r.log.WithContext(ctx).Errorf("Status code: %v", gErr.Code)
			r.log.WithContext(ctx).Errorf("err message :%s ", gErr.Message)
			r.log.WithContext(ctx).Errorf("err details :%v ", gErr.Details)
			r.log.WithContext(ctx).Errorf("err body :%v ", gErr.Body)
		}
		return nil, err
	}
	//r.log.WithContext(ctx).Infof("generate :%v", do)

	r.log.WithContext(ctx).Infof("status code : %d", do.HTTPStatusCode)
	r.log.WithContext(ctx).Infof("total matches : %d", do.TotalMatchedRows)
	//body, err := do.MarshalJSON()
	//if err != nil {
	//	return nil, err
	//}
	//r.log.WithContext(ctx).Infof("response :%s", string(body))
	return r.toAdsense(do), nil
}

func (r *adsenseRepo) GenerateCsvByCustomDateRange(ctx context.Context, metrics, orderBy, dimensions, filters []string, currencyCode string, dateRange *date.Range) (*biz.Adsense, error) {
	URL, err := url.Parse(fmt.Sprintf("https://adsense.googleapis.com/v2/accounts/%s/reports:generateCsv", r.adsenseConfig[0].PubID))
	if err != nil {
		return nil, err
	}
	query := URL.Query()
	for _, metric := range metrics {
		query.Add("metrics", metric)
	}
	for _, dimension := range dimensions {
		query.Add("dimensions", dimension)
	}
	for _, order := range orderBy {
		query.Add("orderBy", order)
	}
	for _, filter := range filters {
		query.Add("filters", filter)
	}

	// 设置自定义日期范围
	query.Set("dateRange", "CUSTOM")
	query.Set("currencyCode", currencyCode)

	// 设置开始日期
	startDate := dateRange.StartTime
	query.Set("startDate.day", fmt.Sprintf("%d", startDate.Day()))
	query.Set("startDate.month", fmt.Sprintf("%d", startDate.Month()))
	query.Set("startDate.year", fmt.Sprintf("%d", startDate.Year()))

	// 设置结束日期
	endDate := dateRange.EndTime
	query.Set("endDate.day", fmt.Sprintf("%d", endDate.Day()))
	query.Set("endDate.month", fmt.Sprintf("%d", endDate.Month()))
	query.Set("endDate.year", fmt.Sprintf("%d", endDate.Year()))

	URL.RawQuery = query.Encode()
	r.log.Info(URL.String())
	resp, err := r.data.gs.client.Get(URL.String())
	if err != nil {
		return nil, err
	}
	defer resp.Body.Close()
	if resp.StatusCode != http.StatusOK {
		bodyData, err := io.ReadAll(resp.Body)
		if err != nil {
			return nil, err
		}
		return nil, fmt.Errorf("status code : %d,body: %s", resp.StatusCode, string(bodyData))
	}
	r.log.WithContext(ctx).Infof("status code : %d", resp.StatusCode)

	toAdsense, err := r.csvToAdsense(resp.Body)
	if err != nil {
		return nil, err
	}
	r.log.WithContext(ctx).Infof("total matches : %d", len(toAdsense.Data))

	return toAdsense, nil
}

func (r *adsenseRepo) GenerateCsv(ctx context.Context, metrics, orderBy, dimensions, filters []string, dateRange, currencyCode string) (*biz.Adsense, error) {
	URL, err := url.Parse(fmt.Sprintf("https://adsense.googleapis.com/v2/accounts/%s/reports:generateCsv", r.adsenseConfig[0].PubID))
	if err != nil {
		return nil, err
	}
	query := URL.Query()
	for _, metric := range metrics {
		query.Add("metrics", metric)
	}
	for _, dimension := range dimensions {
		query.Add("dimensions", dimension)
	}
	for _, order := range orderBy {
		query.Add("orderBy", order)
	}
	for _, filter := range filters {
		query.Add("filters", filter)
	}
	query.Set("dateRange", dateRange)
	query.Set("currencyCode", currencyCode)

	URL.RawQuery = query.Encode()
	r.log.Info(URL.String())
	resp, err := r.data.gs.client.Get(URL.String())
	if err != nil {
		return nil, err
	}
	defer resp.Body.Close()
	if resp.StatusCode != http.StatusOK {
		bodyData, err := io.ReadAll(resp.Body)
		if err != nil {
			return nil, err
		}
		return nil, fmt.Errorf("status code : %d,body: %s", resp.StatusCode, string(bodyData))
	}
	r.log.WithContext(ctx).Infof("status code : %d", resp.StatusCode)

	toAdsense, err := r.csvToAdsense(resp.Body)
	if err != nil {
		return nil, err
	}
	r.log.WithContext(ctx).Infof("total matches : %d", len(toAdsense.Data))

	return toAdsense, nil
}

func (r *adsenseRepo) toAdsense(do *adsense.ReportResult) *biz.Adsense {
	if do == nil {
		return &biz.Adsense{
			Averages: struct {
				Cells []struct {
					Value string `json:"value,omitempty"`
				} `json:"cells"`
			}{},
			EndDate: struct {
				Day   int `json:"day"`
				Month int `json:"month"`
				Year  int `json:"year"`
			}{},
			Headers: nil,
			//Rows:    nil,
			StartDate: struct {
				Day   int `json:"day"`
				Month int `json:"month"`
				Year  int `json:"year"`
			}{},
			TotalMatchedRows: "",
			Totals: struct {
				Cells []struct {
					Value string `json:"value,omitempty"`
				} `json:"cells"`
			}{},
			Data: make([][]string, 0),
		}
	}
	data := make([][]string, len(do.Rows))
	for j, row := range do.Rows {
		d := make([]string, len(row.Cells))
		for i, cell := range row.Cells {
			//rows[j].Cells[i].Value = cell.Value
			d[i] = cell.Value
			//rows[i].Cells[len(rows[i].Cells)-1].Value = cell.Value
		}
		data[j] = d
	}
	return &biz.Adsense{
		//Averages: struct {
		//	Cells []struct {
		//		Value string `json:"value,omitempty"`
		//	} `json:"cells"`
		//}{
		//	cells,
		//},
		EndDate: struct {
			Day   int `json:"day"`
			Month int `json:"month"`
			Year  int `json:"year"`
		}{
			Day:   int(do.EndDate.Day),
			Month: int(do.EndDate.Month),
			Year:  int(do.EndDate.Year),
		},
		//Headers: headers,
		//Rows: rows,
		StartDate: struct {
			Day   int `json:"day"`
			Month int `json:"month"`
			Year  int `json:"year"`
		}{
			Day:   int(do.StartDate.Day),
			Month: int(do.StartDate.Month),
			Year:  int(do.StartDate.Year),
		},
		TotalMatchedRows: strconv.FormatInt(do.TotalMatchedRows, 10),
		Data:             data,
		//Totals: struct {
		//	Cells []struct {
		//		Value string `json:"value,omitempty"`
		//	} `json:"cells"`
		//}{totalCells},
	}
}

func (r *adsenseRepo) ListUrlChannels(ctx context.Context) ([]*biz.AdsenseUrlChannel, error) {
	parent := fmt.Sprintf("accounts/%s/adclients/ca-%s", r.adsenseConfig[0].PubID, r.adsenseConfig[0].PubID)
	do, err := r.data.gs.adsense.Accounts.Adclients.Urlchannels.
		List(parent).Context(ctx).Do()
	if err != nil {
		return nil, err
	}
	res := make([]*biz.AdsenseUrlChannel, 0)
	for _, ch := range do.UrlChannels {
		res = append(res, &biz.AdsenseUrlChannel{
			Name:                 ch.Name,
			ReportingDimensionId: ch.ReportingDimensionId,
			UriPattern:           ch.UriPattern,
		})
	}
	// do.
	for {
		if do.NextPageToken == "" {
			break
		}
		do, err = r.data.gs.adsense.Accounts.Adclients.Urlchannels.List(parent).PageToken(do.NextPageToken).Do()
		if err != nil {
			return nil, err
		}
		for _, ch := range do.UrlChannels {
			res = append(res, &biz.AdsenseUrlChannel{
				Name:                 ch.Name,
				ReportingDimensionId: ch.ReportingDimensionId,
				UriPattern:           ch.UriPattern,
			})
		}
	}
	return res, nil
}

func (r *adsenseRepo) ListCustomChannels(ctx context.Context) ([]*biz.AdsenseCustomChannel, error) {
	parent := fmt.Sprintf("accounts/%s/adclients/ca-%s", r.adsenseConfig[0].PubID, r.adsenseConfig[0].PubID)

	do, err := r.data.gs.adsense.Accounts.Adclients.Customchannels.List(parent).Context(ctx).Do()
	if err != nil {
		return nil, err
	}
	res := make([]*biz.AdsenseCustomChannel, 0)
	for _, ch := range do.CustomChannels {
		res = append(res, &biz.AdsenseCustomChannel{
			Name:                 ch.Name,
			ReportingDimensionId: ch.ReportingDimensionId,
			DisplayName:          ch.DisplayName,
			Active:               ch.Active,
		})
	}
	// do.
	for {
		if do.NextPageToken == "" {
			break
		}
		do, err = r.data.gs.adsense.Accounts.Adclients.Customchannels.List(parent).PageToken(do.NextPageToken).Do()
		if err != nil {
			return nil, err
		}
		for _, ch := range do.CustomChannels {
			res = append(res, &biz.AdsenseCustomChannel{
				Name:                 ch.Name,
				ReportingDimensionId: ch.ReportingDimensionId,
				DisplayName:          ch.DisplayName,
				Active:               ch.Active,
			})
		}
	}
	return res, nil
}

func (r *adsenseRepo) csvToAdsense(adsenseData io.Reader) (*biz.Adsense, error) {
	reader := csv.NewReader(adsenseData)

	// 读取所有记录
	records, err := reader.ReadAll()
	if err != nil {
		return nil, err
	}

	return &biz.Adsense{
		Data: records[1:],
	}, nil
}

// NewAdsenseRepo .
func NewAdsenseRepo(googleConf *conf.Google, data *Data, logger log.Logger) biz.AdsenseRepo {
	return &adsenseRepo{
		data:          data,
		adsenseConfig: googleConf.GetConfigs(),
		log:           log.NewHelper(log.With(logger, "module", "adsense-bot/data/adsense")),
	}
}
