package data

import (
	"context"
	"time"

	"entgo.io/ent/dialect/sql"
	"github.com/go-kratos/kratos/v2/log"

	"git.minigame.vip/minicloud/service/adsense-bot/internal/biz"
	"git.minigame.vip/minicloud/service/adsense-bot/internal/data/ent"
	"git.minigame.vip/minicloud/service/adsense-bot/internal/data/ent/channeladformat"
	"git.minigame.vip/minicloud/service/adsense-bot/internal/data/ent/channeladformathkd"
)

var (
	AggField = []string{"page_views", "impressions", "clicks", "ad_requests", "matched_ad_requests"}
)

type channelAdFormatRepo struct {
	data *Data
	log  *log.Helper
}

func (r *channelAdFormatRepo) ListHKD(ctx context.Context, s time.Time, e time.Time) ([]*biz.AdsenseChannelAdFormatData, error) {
	var agg []string
	for _, field := range AggField {
		agg = append(agg, sql.As(sql.Sum(field), field))
	}
	agg = append(agg, sql.As("CAST(ROUND(SUM(estimated_earnings)::numeric, 2) AS numeric(10, 2))", "estimated_earnings"))
	agg = append(agg, channeladformathkd.FieldChannel)
	agg = append(agg, channeladformathkd.FieldChannelID)
	agg = append(agg, channeladformathkd.FieldDate)
	agg = append(agg, sql.As(" CAST(COALESCE(SUM(estimated_earnings) / NULLIF(SUM(clicks)*1.0, 0.0), 0)AS numeric(10,4)) ", "cost_per_click"))
	agg = append(agg, sql.As(" CAST(COALESCE(SUM(clicks) / NULLIF(SUM(impressions)*1.0, 0.0), 0)AS numeric(10,4))", "impressions_ctr"))
	agg = append(agg, sql.As(" CAST(COALESCE(SUM(matched_ad_requests) / NULLIF(SUM(ad_requests)*1.0, 0.0), 0)AS numeric(10,4))", "ad_requests_coverage"))
	agg = append(agg, sql.As(" CAST(COALESCE(SUM(estimated_earnings) / NULLIF(SUM(impressions)*1.0, 0.0)*1000, 0)AS numeric(10,4))", "impressions_rpm"))
	//agg = append(agg, sql.As(" CAST(COALESCE(SUM(estimated_earnings) / NULLIF(SUM(page_views)*1.0, 0.0)*1000, 0)AS numeric(10,4))", "page_views_rpm"))
	//agg = append(agg, SumField...)
	agg = append(agg, sql.As("SUM(0.0)", "active_view_viewability"))
	//agg = append(agg, sql.As(" CAST(COALESCE(SUM(impressions) / NULLIF(SUM(page_views)*1.0, 0.0), 0)AS numeric(10,4))", "impressions_per_page_view"))

	var all []*biz.AdsenseChannelAdFormatData
	err := r.data.db.ChannelAdFormatHKD.Query().Where(channeladformathkd.DateGTE(s), channeladformathkd.DateLTE(e),
		channeladformathkd.AdFormatIn("MANUAL_INTERSTITIAL", "MANUAL_REWARDED")).Modify(func(s *sql.Selector) {
		s.Select(agg...).
			GroupBy(channeladformathkd.FieldChannel, channeladformathkd.FieldChannelID, channeladformathkd.FieldDate)
	}).
		Scan(ctx, &all)
	if err != nil {
		return nil, err
	}
	return all, nil
}

func (r *channelAdFormatRepo) CreateHKD(ctx context.Context, datas []*biz.AdsenseChannelAdFormatData) error {
	bulk := make([]*ent.ChannelAdFormatHKDCreate, 0)
	tx, err := r.data.db.Tx(ctx)
	if err != nil {
		return err
	}
	for i, data := range datas {
		bulk = append(bulk, tx.ChannelAdFormatHKD.Create().SetDate(data.Date).SetChannel(data.Channel).
			SetAdFormat(data.AdFormat).SetEstimatedEarnings(data.EstimatedEarnings).
			SetChannelID(data.ChannelID).
			SetPageViews(data.PageViews).SetPageViewsRpm(data.PageViewsRpm).
			SetImpressions(data.IMPRESSIONS).SetImpressionsRpm(data.ImpressionsRpm).
			SetAdRequestsCoverage(data.AdRequestsCoverage).SetClicks(data.CLICKS).
			SetAdRequests(data.AdRequests).SetImpressionsCtr(data.ImpressionsCtr).
			SetActiveViewViewability(data.ActiveViewViewability).
			SetMatchedAdRequests(data.MatchedAdRequests).
			SetCostPerClick(data.CostPerClick))
		if (i+1)%4000 == 0 || i == len(datas)-1 {
			err := tx.ChannelAdFormatHKD.CreateBulk(bulk...).OnConflict(sql.ConflictColumns("date", "channel", "channel_id", "ad_format")).UpdateNewValues().Exec(ctx)

			// _, err := tx.ChannelAdFormat.createBulk(bulk...).Save(ctx)
			if err != nil {
				return rollback(tx, err)
			}
			bulk = bulk[:0]
		}
	}
	return tx.Commit()
}

func (r *channelAdFormatRepo) List(ctx context.Context, s, e time.Time) ([]*biz.AdsenseChannelAdFormatData, error) {
	var agg []string
	for _, field := range AggField {
		agg = append(agg, sql.As(sql.Sum(field), field))
	}
	agg = append(agg, sql.As("CAST(ROUND(SUM(estimated_earnings)::numeric, 2) AS numeric(10, 2))", "estimated_earnings"))
	agg = append(agg, channeladformat.FieldChannel)
	agg = append(agg, channeladformat.FieldChannelID)
	agg = append(agg, channeladformat.FieldDate)
	agg = append(agg, sql.As(" CAST(COALESCE(SUM(estimated_earnings) / NULLIF(SUM(clicks)*1.0, 0.0), 0)AS numeric(10,4)) ", "cost_per_click"))
	agg = append(agg, sql.As(" CAST(COALESCE(SUM(clicks) / NULLIF(SUM(impressions)*1.0, 0.0), 0)AS numeric(10,4))", "impressions_ctr"))
	agg = append(agg, sql.As(" CAST(COALESCE(SUM(matched_ad_requests) / NULLIF(SUM(ad_requests)*1.0, 0.0), 0)AS numeric(10,4))", "ad_requests_coverage"))
	agg = append(agg, sql.As(" CAST(COALESCE(SUM(estimated_earnings) / NULLIF(SUM(impressions)*1.0, 0.0)*1000, 0)AS numeric(10,4))", "impressions_rpm"))
	//agg = append(agg, sql.As(" CAST(COALESCE(SUM(estimated_earnings) / NULLIF(SUM(page_views)*1.0, 0.0)*1000, 0)AS numeric(10,4))", "page_views_rpm"))
	//agg = append(agg, SumField...)
	agg = append(agg, sql.As("SUM(0.0)", "active_view_viewability"))
	//agg = append(agg, sql.As(" CAST(COALESCE(SUM(impressions) / NULLIF(SUM(page_views)*1.0, 0.0), 0)AS numeric(10,4))", "impressions_per_page_view"))

	var all []*biz.AdsenseChannelAdFormatData
	err := r.data.db.ChannelAdFormat.Query().Where(channeladformat.DateGTE(s), channeladformat.DateLTE(e),
		channeladformat.AdFormatIn("MANUAL_INTERSTITIAL", "MANUAL_REWARDED")).Modify(func(s *sql.Selector) {
		s.Select(agg...).
			GroupBy(channeladformat.FieldChannel, channeladformat.FieldChannelID, channeladformat.FieldDate)
	}).
		Scan(ctx, &all)
	if err != nil {
		return nil, err
	}
	return all, nil
}

func (r *channelAdFormatRepo) Create(ctx context.Context, datas []*biz.AdsenseChannelAdFormatData) error {
	bulk := make([]*ent.ChannelAdFormatCreate, 0)
	tx, err := r.data.db.Tx(ctx)
	if err != nil {
		return err
	}
	for i, data := range datas {
		bulk = append(bulk, tx.ChannelAdFormat.Create().SetDate(data.Date).SetChannel(data.Channel).
			SetAdFormat(data.AdFormat).SetEstimatedEarnings(data.EstimatedEarnings).
			SetChannelID(data.ChannelID).
			SetPageViews(data.PageViews).SetPageViewsRpm(data.PageViewsRpm).
			SetImpressions(data.IMPRESSIONS).SetImpressionsRpm(data.ImpressionsRpm).
			SetAdRequestsCoverage(data.AdRequestsCoverage).SetClicks(data.CLICKS).
			SetAdRequests(data.AdRequests).SetImpressionsCtr(data.ImpressionsCtr).
			SetActiveViewViewability(data.ActiveViewViewability).
			SetMatchedAdRequests(data.MatchedAdRequests).
			SetCostPerClick(data.CostPerClick))
		if (i+1)%4000 == 0 || i == len(datas)-1 {
			err := tx.ChannelAdFormat.CreateBulk(bulk...).OnConflict(sql.ConflictColumns("date", "channel", "channel_id", "ad_format")).UpdateNewValues().Exec(ctx)

			// _, err := tx.ChannelAdFormat.createBulk(bulk...).Save(ctx)
			if err != nil {
				return rollback(tx, err)
			}
			bulk = bulk[:0]
		}
	}
	return tx.Commit()
}

func NewChannelAdFormatRepo(data *Data, logger log.Logger) biz.AdSenseChannelAdFormatRepo {
	return &channelAdFormatRepo{
		data,
		log.NewHelper(log.With(logger, "module", "data/channel/adFormat")),
	}
}
