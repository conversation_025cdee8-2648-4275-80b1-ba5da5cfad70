package data

import (
	"context"
	"time"

	"entgo.io/ent/dialect/sql"
	"github.com/go-kratos/kratos/v2/log"

	"git.minigame.vip/minicloud/service/adsense-bot/internal/biz"
	"git.minigame.vip/minicloud/service/adsense-bot/internal/data/ent"
	"git.minigame.vip/minicloud/service/adsense-bot/internal/data/ent/channelcountryadformathkdpartition"
	"git.minigame.vip/minicloud/service/adsense-bot/internal/data/ent/channelcountryadformatpartition"
)

type channelCountryAdFormatPartitionRepo struct {
	data *Data
	log  *log.Helper
}

func (r *channelCountryAdFormatPartitionRepo) ListHKD(ctx context.Context, start time.Time, end time.Time) ([]*biz.AdsenseChannelCountryAdFormatData, error) {
	var agg []string
	for _, field := range AggField {
		agg = append(agg, sql.As(sql.Sum(field), field))
	}
	agg = append(agg, sql.As("CAST(ROUND(SUM(estimated_earnings)::numeric, 2) AS numeric(10, 2))", "estimated_earnings"))
	agg = append(agg, channelcountryadformathkdpartition.FieldChannel)
	agg = append(agg, channelcountryadformathkdpartition.FieldChannelID)
	agg = append(agg, channelcountryadformathkdpartition.FieldDate)
	agg = append(agg, channelcountryadformathkdpartition.FieldCountry)
	agg = append(agg, sql.As(" CAST(COALESCE(SUM(estimated_earnings) / NULLIF(SUM(clicks)*1.0, 0.0), 0)AS numeric(10,4)) ", "cost_per_click"))
	agg = append(agg, sql.As(" CAST(COALESCE(SUM(clicks) / NULLIF(SUM(impressions)*1.0, 0.0), 0)AS numeric(10,4))", "impressions_ctr"))
	agg = append(agg, sql.As(" CAST(COALESCE(SUM(matched_ad_requests) / NULLIF(SUM(ad_requests)*1.0, 0.0), 0)AS numeric(10,4))", "ad_requests_coverage"))
	agg = append(agg, sql.As(" CAST(COALESCE(SUM(estimated_earnings) / NULLIF(SUM(impressions)*1.0, 0.0)*1000, 0)AS numeric(10,4))", "impressions_rpm"))
	//agg = append(agg, sql.As(" CAST(COALESCE(SUM(estimated_earnings) / NULLIF(SUM(page_views)*1.0, 0.0)*1000, 0)AS numeric(10,4))", "page_views_rpm"))
	//agg = append(agg, SumField...)
	agg = append(agg, sql.As("SUM(0.0)", "active_view_viewability"))

	//agg = append(agg, sql.As(" CAST(COALESCE(SUM(impressions) / NULLIF(SUM(page_views)*1.0, 0.0), 0)AS numeric(10,4))", "impressions_per_page_view"))

	var all []*biz.AdsenseChannelCountryAdFormatData
	err := r.data.db.ChannelCountryAdFormatHKDPartition.Query().Where(channelcountryadformathkdpartition.DateGTE(start), channelcountryadformathkdpartition.DateLTE(end),
		channelcountryadformathkdpartition.AdFormatIn("MANUAL_INTERSTITIAL", "MANUAL_REWARDED")).Modify(func(s *sql.Selector) {
		s.Select(agg...).
			GroupBy(channelcountryadformathkdpartition.FieldChannel,
				channelcountryadformathkdpartition.FieldChannelID, channelcountryadformathkdpartition.FieldCountry,
				channelcountryadformathkdpartition.FieldDate)
	}).
		Scan(ctx, &all)
	if err != nil {
		return nil, err
	}
	return all, nil
}

func (r *channelCountryAdFormatPartitionRepo) CreateHKD(ctx context.Context, datas []*biz.AdsenseChannelCountryAdFormatData) error {
	bulk := make([]*ent.ChannelCountryAdFormatHKDPartitionCreate, 0)
	tx, err := r.data.db.Tx(ctx)
	if err != nil {
		return err
	}
	for i, data := range datas {
		bulk = append(bulk, tx.ChannelCountryAdFormatHKDPartition.Create().SetDate(data.Date).SetChannel(data.Channel).
			SetCountry(data.Country).SetEstimatedEarnings(data.EstimatedEarnings).SetAdFormat(data.AdFormat).
			SetPageViews(data.PageViews).SetPageViewsRpm(data.PageViewsRpm).SetChannelID(data.ChannelID).
			SetImpressions(data.IMPRESSIONS).SetImpressionsRpm(data.ImpressionsRpm).
			SetAdRequestsCoverage(data.AdRequestsCoverage).SetClicks(data.CLICKS).
			SetAdRequests(data.AdRequests).SetImpressionsCtr(data.ImpressionsCtr).
			SetActiveViewViewability(data.ActiveViewViewability).
			SetMatchedAdRequests(data.MatchedAdRequests).
			SetCostPerClick(data.CostPerClick))
		if (i+1)%2000 == 0 || i == len(datas)-1 {
			err := tx.ChannelCountryAdFormatHKDPartition.CreateBulk(bulk...).OnConflict(sql.ConflictColumns("date", "channel", "channel_id", "ad_format", "country")).UpdateNewValues().Exec(ctx)
			if err != nil {
				return rollback(tx, err)
			}
			bulk = bulk[:0]
		}
	}

	return tx.Commit()
}

func (r *channelCountryAdFormatPartitionRepo) List(ctx context.Context, start time.Time, end time.Time) ([]*biz.AdsenseChannelCountryAdFormatData, error) {
	var agg []string
	for _, field := range AggField {
		agg = append(agg, sql.As(sql.Sum(field), field))
	}
	agg = append(agg, sql.As("CAST(ROUND(SUM(estimated_earnings)::numeric, 2) AS numeric(10, 2))", "estimated_earnings"))
	agg = append(agg, channelcountryadformatpartition.FieldChannel)
	agg = append(agg, channelcountryadformatpartition.FieldChannelID)
	agg = append(agg, channelcountryadformatpartition.FieldDate)
	agg = append(agg, channelcountryadformatpartition.FieldCountry)
	agg = append(agg, sql.As(" CAST(COALESCE(SUM(estimated_earnings) / NULLIF(SUM(clicks)*1.0, 0.0), 0)AS numeric(10,4)) ", "cost_per_click"))
	agg = append(agg, sql.As(" CAST(COALESCE(SUM(clicks) / NULLIF(SUM(impressions)*1.0, 0.0), 0)AS numeric(10,4))", "impressions_ctr"))
	agg = append(agg, sql.As(" CAST(COALESCE(SUM(matched_ad_requests) / NULLIF(SUM(ad_requests)*1.0, 0.0), 0)AS numeric(10,4))", "ad_requests_coverage"))
	agg = append(agg, sql.As(" CAST(COALESCE(SUM(estimated_earnings) / NULLIF(SUM(impressions)*1.0, 0.0)*1000, 0)AS numeric(10,4))", "impressions_rpm"))
	//agg = append(agg, sql.As(" CAST(COALESCE(SUM(estimated_earnings) / NULLIF(SUM(page_views)*1.0, 0.0)*1000, 0)AS numeric(10,4))", "page_views_rpm"))
	//agg = append(agg, SumField...)
	agg = append(agg, sql.As("SUM(0.0)", "active_view_viewability"))

	//agg = append(agg, sql.As(" CAST(COALESCE(SUM(impressions) / NULLIF(SUM(page_views)*1.0, 0.0), 0)AS numeric(10,4))", "impressions_per_page_view"))

	var all []*biz.AdsenseChannelCountryAdFormatData
	err := r.data.db.ChannelCountryAdFormatPartition.Query().Where(channelcountryadformatpartition.DateGTE(start), channelcountryadformatpartition.DateLTE(end),
		channelcountryadformatpartition.AdFormatIn("MANUAL_INTERSTITIAL", "MANUAL_REWARDED")).Modify(func(s *sql.Selector) {
		s.Select(agg...).
			GroupBy(channelcountryadformatpartition.FieldChannel, channelcountryadformatpartition.FieldChannelID, channelcountryadformatpartition.FieldCountry,
				channelcountryadformatpartition.FieldDate)
	}).
		Scan(ctx, &all)
	if err != nil {
		return nil, err
	}
	return all, nil
}

func (r *channelCountryAdFormatPartitionRepo) Create(ctx context.Context, datas []*biz.AdsenseChannelCountryAdFormatData) error {
	bulk := make([]*ent.ChannelCountryAdFormatPartitionCreate, 0)
	tx, err := r.data.db.Tx(ctx)
	if err != nil {
		return err
	}
	for i, data := range datas {
		bulk = append(bulk, tx.ChannelCountryAdFormatPartition.Create().SetDate(data.Date).SetChannel(data.Channel).
			SetCountry(data.Country).SetEstimatedEarnings(data.EstimatedEarnings).SetAdFormat(data.AdFormat).
			SetPageViews(data.PageViews).SetPageViewsRpm(data.PageViewsRpm).SetChannelID(data.ChannelID).
			SetImpressions(data.IMPRESSIONS).SetImpressionsRpm(data.ImpressionsRpm).
			SetAdRequestsCoverage(data.AdRequestsCoverage).SetClicks(data.CLICKS).
			SetAdRequests(data.AdRequests).SetImpressionsCtr(data.ImpressionsCtr).
			SetActiveViewViewability(data.ActiveViewViewability).
			SetMatchedAdRequests(data.MatchedAdRequests).
			SetCostPerClick(data.CostPerClick))
		if (i+1)%2000 == 0 || i == len(datas)-1 {
			err := tx.ChannelCountryAdFormatPartition.CreateBulk(bulk...).OnConflict(sql.ConflictColumns("date", "channel", "channel_id", "ad_format", "country")).UpdateNewValues().Exec(ctx)
			if err != nil {
				return rollback(tx, err)
			}
			bulk = bulk[:0]
		}
	}

	return tx.Commit()
}

// AutoMigrate 迁移数据： 总表的数据按月份写入到不同的分表中
func (r *channelCountryAdFormatPartitionRepo) AutoMigrate(_ context.Context, _ int) error {
	//sql := fmt.Sprintf(`
	//				INSERT INTO %s
	//		SELECT *
	//		FROM %s ;
	//			`, channelcountryadformatpartition.Table, channelcountryadformat.Table)
	//r.log.Debug(sql)
	//_, err := r.data.stddb.Exec(sql)
	//if err != nil {
	//	r.log.Debug(err.Error())
	//	return err
	//}
	//tableName := channelcountryadformatpartition.Table
	//sqlIndex := fmt.Sprintf(`CREATE INDEX "%s_ad_format" ON "public"."%s" USING btree (
	//"ad_format" COLLATE "pg_catalog"."default" "pg_catalog"."text_ops" ASC NULLS LAST
	//);
	//CREATE INDEX "%s_channel" ON "public"."%s" USING btree (
	//"channel" COLLATE "pg_catalog"."default" "pg_catalog"."text_ops" ASC NULLS LAST
	//);
	//CREATE INDEX "%s_channel_id" ON "public"."%s" USING btree (
	//"channel_id" COLLATE "pg_catalog"."default" "pg_catalog"."text_ops" ASC NULLS LAST
	//);
	//CREATE INDEX "%s_country" ON "public"."%s" USING btree (
	//"country" COLLATE "pg_catalog"."default" "pg_catalog"."text_ops" ASC NULLS LAST
	//);
	//CREATE INDEX "%s_date" ON "public"."%s" USING btree (
	//"date" "pg_catalog"."timestamptz_ops" ASC NULLS LAST
	//);
	//CREATE UNIQUE INDEX "%s_date_channel_channe_id_country_ad_format" ON "public"."%s" USING btree (
	//"date" "pg_catalog"."timestamptz_ops" ASC NULLS LAST,
	//"channel" COLLATE "pg_catalog"."default" "pg_catalog"."text_ops" ASC NULLS LAST,
	//"channel_id" COLLATE "pg_catalog"."default" "pg_catalog"."text_ops" ASC NULLS LAST,
	//"country" COLLATE "pg_catalog"."default" "pg_catalog"."text_ops" ASC NULLS LAST,
	//"ad_format" COLLATE "pg_catalog"."default" "pg_catalog"."text_ops" ASC NULLS LAST
	//);`, tableName, tableName, tableName, tableName, tableName, tableName, tableName, tableName, tableName, tableName, tableName, tableName)
	//r.log.Debug(sqlIndex)
	//_, err = r.data.stddb.Exec(sqlIndex)
	//if err != nil {
	//	r.log.Debug(err.Error())
	//}
	return nil
}

func NewChannelCountryAdFormatPartitionRepo(data *Data, logger log.Logger) biz.AdSenseChannelCountryAdFormatRepo {
	return &channelCountryAdFormatPartitionRepo{
		data,
		log.NewHelper(log.With(logger, "module", "data/channel-country-ad-format/country")),
	}
}
