package data

import (
	"context"
	"time"

	"entgo.io/ent/dialect/sql"
	"github.com/go-kratos/kratos/v2/log"

	"git.minigame.vip/minicloud/service/adsense-bot/internal/biz"
	"git.minigame.vip/minicloud/service/adsense-bot/internal/data/ent"
	"git.minigame.vip/minicloud/service/adsense-bot/internal/data/ent/channelcountry"
	"git.minigame.vip/minicloud/service/adsense-bot/internal/data/ent/channelcountryhkd"
)

type channelCountryRepo struct {
	data *Data
	log  *log.Helper
}

func (r *channelCountryRepo) ListHKD(ctx context.Context, start time.Time, end time.Time) ([]*biz.AdsenseChannelCountryData, error) {
	all, err := r.data.db.ChannelCountryHKD.Query().Where(channelcountryhkd.DateLTE(end), channelcountryhkd.DateGTE(start)).All(ctx)
	if err != nil {
		return nil, err
	}
	res := make([]*biz.AdsenseChannelCountryData, 0)
	for _, data := range all {
		res = append(res, &biz.AdsenseChannelCountryData{
			Date:                  data.Date,
			Channel:               data.Channel,
			ChannelID:             data.ChannelID,
			EstimatedEarnings:     data.EstimatedEarnings,
			PageViews:             data.PageViews,
			PageViewsRpm:          data.PageViewsRpm,
			IMPRESSIONS:           data.Impressions,
			ImpressionsRpm:        data.ImpressionsRpm,
			AdRequestsCoverage:    data.AdRequestsCoverage,
			CLICKS:                data.Clicks,
			AdRequests:            data.AdRequests,
			ImpressionsCtr:        data.ImpressionsCtr,
			ActiveViewViewability: data.ActiveViewViewability,
			CostPerClick:          data.CostPerClick,
			MatchedAdRequests:     data.MatchedAdRequests,
			Country:               data.Country,
		})
	}
	return res, nil
}

func (r *channelCountryRepo) CreateHKDData(ctx context.Context, datas []*biz.AdsenseChannelCountryData) error {
	bulk := make([]*ent.ChannelCountryHKDCreate, 0)
	tx, err := r.data.db.Tx(ctx)
	if err != nil {
		return err
	}
	for i, data := range datas {
		bulk = append(bulk, tx.ChannelCountryHKD.Create().SetDate(data.Date).SetChannel(data.Channel).
			SetCountry(data.Country).SetEstimatedEarnings(data.EstimatedEarnings).
			SetPageViews(data.PageViews).SetPageViewsRpm(data.PageViewsRpm).SetChannelID(data.ChannelID).
			SetImpressions(data.IMPRESSIONS).SetImpressionsRpm(data.ImpressionsRpm).
			SetAdRequestsCoverage(data.AdRequestsCoverage).SetClicks(data.CLICKS).
			SetAdRequests(data.AdRequests).SetImpressionsCtr(data.ImpressionsCtr).
			SetActiveViewViewability(data.ActiveViewViewability).
			SetMatchedAdRequests(data.MatchedAdRequests).
			SetCostPerClick(data.CostPerClick))
		if (i+1)%4000 == 0 || i == len(datas)-1 {
			err := tx.ChannelCountryHKD.CreateBulk(bulk...).OnConflict(sql.ConflictColumns("date", "channel", "channel_id", "country")).UpdateNewValues().Exec(ctx)
			if err != nil {
				return rollback(tx, err)
			}
			bulk = bulk[:0]
		}
	}

	return tx.Commit()
}

func (r *channelCountryRepo) List(ctx context.Context, start time.Time, end time.Time) ([]*biz.AdsenseChannelCountryData, error) {
	all, err := r.data.db.ChannelCountry.Query().Where(channelcountry.DateLTE(end), channelcountry.DateGTE(start)).All(ctx)
	if err != nil {
		return nil, err
	}
	res := make([]*biz.AdsenseChannelCountryData, 0)
	for _, data := range all {
		res = append(res, &biz.AdsenseChannelCountryData{
			Date:                  data.Date,
			Channel:               data.Channel,
			ChannelID:             data.ChannelID,
			EstimatedEarnings:     data.EstimatedEarnings,
			PageViews:             data.PageViews,
			PageViewsRpm:          data.PageViewsRpm,
			IMPRESSIONS:           data.Impressions,
			ImpressionsRpm:        data.ImpressionsRpm,
			AdRequestsCoverage:    data.AdRequestsCoverage,
			CLICKS:                data.Clicks,
			AdRequests:            data.AdRequests,
			ImpressionsCtr:        data.ImpressionsCtr,
			ActiveViewViewability: data.ActiveViewViewability,
			CostPerClick:          data.CostPerClick,
			MatchedAdRequests:     data.MatchedAdRequests,
			Country:               data.Country,
		})
	}
	return res, nil
}

func (r *channelCountryRepo) Create(ctx context.Context, datas []*biz.AdsenseChannelCountryData) error {
	bulk := make([]*ent.ChannelCountryCreate, 0)
	tx, err := r.data.db.Tx(ctx)
	if err != nil {
		return err
	}
	for i, data := range datas {
		bulk = append(bulk, tx.ChannelCountry.Create().SetDate(data.Date).SetChannel(data.Channel).
			SetCountry(data.Country).SetEstimatedEarnings(data.EstimatedEarnings).
			SetPageViews(data.PageViews).SetPageViewsRpm(data.PageViewsRpm).SetChannelID(data.ChannelID).
			SetImpressions(data.IMPRESSIONS).SetImpressionsRpm(data.ImpressionsRpm).
			SetAdRequestsCoverage(data.AdRequestsCoverage).SetClicks(data.CLICKS).
			SetAdRequests(data.AdRequests).SetImpressionsCtr(data.ImpressionsCtr).
			SetActiveViewViewability(data.ActiveViewViewability).
			SetMatchedAdRequests(data.MatchedAdRequests).
			SetCostPerClick(data.CostPerClick))
		if (i+1)%4000 == 0 || i == len(datas)-1 {
			err := tx.ChannelCountry.CreateBulk(bulk...).OnConflict(sql.ConflictColumns("date", "channel", "channel_id", "country")).UpdateNewValues().Exec(ctx)
			if err != nil {
				return rollback(tx, err)
			}
			bulk = bulk[:0]
		}
	}

	return tx.Commit()
}

func NewChannelCountryRepo(data *Data, logger log.Logger) biz.AdSenseChannelCountryRepo {
	return &channelCountryRepo{
		data,
		log.NewHelper(log.With(logger, "module", "data/channel/country")),
	}
}
