package data

import (
	"bytes"
	"context"
	"encoding/json"
	"io"
	"net/http"
	"net/url"

	"github.com/go-kratos/kratos/v2/log"

	"git.minigame.vip/minicloud/service/adsense-bot/internal/biz"
	"git.minigame.vip/minicloud/service/adsense-bot/internal/conf"
)

type botRepo struct {
	data *Data
	log  *log.Helper
	key  string
}

func (r *botRepo) send(ctx context.Context, msg *biz.Message) error {
	body, err := json.Marshal(msg)
	if err != nil {
		return err
	}
	client := &http.Client{}
	req, err := http.NewRequestWithContext(ctx, http.MethodPost, "https://qyapi.weixin.qq.com/cgi-bin/webhook/send?debug=1&key="+url.QueryEscape(r.key), bytes.NewReader(body))
	if err != nil {
		return err
	}
	r.log.Info("body", string(body))
	req.Header.Set("Content-Type", "application/json")

	res, err := client.Do(req)
	if err != nil {
		return err
	}
	defer res.Body.Close()
	r.log.Info("send message", "code", res.StatusCode)
	all, err := io.ReadAll(res.Body)
	if err != nil {
		return err
	}
	r.log.Info("response", "body", string(all))
	return nil
}
func (r *botRepo) SendText(ctx context.Context, text string) (err error) {
	return r.send(ctx, biz.NewTextMessage(text, []string{}, []string{}))
}

func (r *botRepo) SendMarkdown(ctx context.Context, markdown string) (err error) {
	return r.send(ctx, biz.NewMarkdownMessage(markdown))
}

func (r *botRepo) SendImage(ctx context.Context, image []byte) (err error) {
	return r.send(ctx, biz.NewImageMessage(image))
}

// NewBotRepo .
func NewBotRepo(bot *conf.Bot, data *Data, logger log.Logger) biz.BotRepo {
	return &botRepo{
		data: data,
		key:  bot.Key,
		log:  log.NewHelper(log.With(logger, "module", "adsense-bot/data/bot")),
	}
}
