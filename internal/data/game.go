package data

import (
	"context"

	"entgo.io/ent/dialect/sql"
	"github.com/go-kratos/kratos/v2/log"

	"git.minigame.vip/minicloud/service/adsense-bot/internal/biz"
	"git.minigame.vip/minicloud/service/adsense-bot/internal/data/ent"
)

type gameRepo struct {
	data *Data
	log  *log.Helper
}

func (r *gameRepo) CreateHKD(ctx context.Context, datas []*biz.AdsenseChannelData) error {
	bulk := make([]*ent.GameHKDCreate, 0)
	tx, err := r.data.db.Tx(ctx)
	if err != nil {
		return err
	}
	for i, data := range datas {
		bulk = append(bulk, tx.GameHKD.Create().SetDate(data.Date).SetChannel(data.Channel).SetEstimatedEarnings(data.EstimatedEarnings).
			SetPageViews(data.PageViews).SetPageViewsRpm(data.PageViewsRpm).SetChannelID(data.ChannelID).
			SetImpressions(data.IMPRESSIONS).SetImpressionsRpm(data.ImpressionsRpm).
			SetAdRequestsCoverage(data.AdRequestsCoverage).SetClicks(data.CLICKS).
			SetAdRequests(data.AdRequests).SetImpressionsCtr(data.ImpressionsCtr).
			SetActiveViewViewability(data.ActiveViewViewability).
			SetMatchedAdRequests(data.MatchedAdRequests).
			SetCostPerClick(data.CostPerClick))
		if (i+1)%4000 == 0 || i == len(datas)-1 {
			err := tx.GameHKD.CreateBulk(bulk...).OnConflict(sql.ConflictColumns("date", "channel", "channel_id")).UpdateNewValues().Exec(ctx)
			// _, err := tx.Channel.createBulk(bulk...).Save(ctx)
			if err != nil {
				return rollback(tx, err)
			}
			bulk = bulk[:0]
		}
	}

	return tx.Commit()
}

func (r *gameRepo) Create(ctx context.Context, datas []*biz.AdsenseChannelData) error {
	bulk := make([]*ent.GameCreate, 0)
	tx, err := r.data.db.Tx(ctx)
	if err != nil {
		return err
	}
	for i, data := range datas {
		bulk = append(bulk, tx.Game.Create().SetDate(data.Date).SetChannel(data.Channel).SetEstimatedEarnings(data.EstimatedEarnings).
			SetPageViews(data.PageViews).SetPageViewsRpm(data.PageViewsRpm).SetChannelID(data.ChannelID).
			SetImpressions(data.IMPRESSIONS).SetImpressionsRpm(data.ImpressionsRpm).
			SetAdRequestsCoverage(data.AdRequestsCoverage).SetClicks(data.CLICKS).
			SetAdRequests(data.AdRequests).SetImpressionsCtr(data.ImpressionsCtr).
			SetActiveViewViewability(data.ActiveViewViewability).
			SetMatchedAdRequests(data.MatchedAdRequests).
			SetCostPerClick(data.CostPerClick))
		if (i+1)%4000 == 0 || i == len(datas)-1 {
			err := tx.Game.CreateBulk(bulk...).OnConflict(sql.ConflictColumns("date", "channel", "channel_id")).UpdateNewValues().Exec(ctx)
			// _, err := tx.Channel.createBulk(bulk...).Save(ctx)
			if err != nil {
				return rollback(tx, err)
			}
			bulk = bulk[:0]
		}
	}

	return tx.Commit()
}

func NewGameRepo(data *Data, logger log.Logger) biz.AdSenseGameRepo {
	return &gameRepo{
		data,
		log.NewHelper(log.With(logger, "module", "data/game")),
	}
}
