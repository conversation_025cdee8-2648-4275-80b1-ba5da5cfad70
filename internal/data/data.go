package data

import (
	"context"
	"database/sql"
	"fmt"
	stdhttp "net/http"
	"strings"
	"time"

	entsql "entgo.io/ent/dialect/sql"
	"entgo.io/ent/dialect/sql/schema"
	"github.com/go-kratos/kratos/v2/log"
	"github.com/google/wire"
	_ "github.com/lib/pq"
	"golang.org/x/oauth2"
	"golang.org/x/oauth2/google"
	"google.golang.org/api/adsense/v2"
	analyticsadmin "google.golang.org/api/analyticsadmin/v1alpha"
	analyticsdata "google.golang.org/api/analyticsdata/v1beta"
	"google.golang.org/api/option"
	"google.golang.org/api/transport/http"

	"git.minigame.vip/minicloud/service/adsense-bot/internal/biz"
	"git.minigame.vip/minicloud/service/adsense-bot/internal/conf"
	"git.minigame.vip/minicloud/service/adsense-bot/internal/data/ent"
	"git.minigame.vip/minicloud/service/adsense-bot/internal/data/ent/channelcountryadformathkdpartition"
	"git.minigame.vip/minicloud/service/adsense-bot/internal/data/ent/channelcountryadformatpartition"
	"git.minigame.vip/minicloud/service/adsense-bot/internal/data/ent/gamecountryhkdpartition"
	"git.minigame.vip/minicloud/service/adsense-bot/internal/data/ent/gamecountrypartition"
	_ "git.minigame.vip/minicloud/service/adsense-bot/internal/data/ent/runtime"
	"git.minigame.vip/minicloud/service/adsense-bot/internal/data/ent/siteadformathistory"
	"git.minigame.vip/minicloud/service/adsense-bot/internal/data/ent/sitebusiness"
	"git.minigame.vip/minicloud/service/adsense-bot/internal/data/ent/sitecountryadformathkdpartition"
	"git.minigame.vip/minicloud/service/adsense-bot/internal/data/ent/sitecountryadformatpartition"
	"git.minigame.vip/minicloud/service/adsense-bot/internal/data/ent/urlchannelcountryadformatinfopartition"
	"git.minigame.vip/minicloud/service/adsense-bot/internal/data/ent/urlchannelcountryadformatinfopartitionhkd"
)

// ProviderSet is data providers.
var ProviderSet = wire.NewSet(NewData, NewBotRepo, NewGoogleService, NewAdsenseRepo, NewChannelCountryRepo, NewSiteRepo,
	NewSiteAdFormatRepo, NewUrlChannelCountryRepo, NewTodaySiteCountryRepo, NewSiteCountryAdFormatPartitionRepo, NewUrlChannelCountryAdFormatRepo,
	NewEntClient, NewSiteCountryRepo, NewPageURLRepo, NewTodaySiteRepo, NewChannelCountryAdFormatPartitionRepo, NewCooperationRepo, NewUnifiedSiteRepo, //
	NewUrlChannelRepo, NewChannelRepo, NewUrlChannelAdFormatRepo, NewGameRepo, NewCustomChannelRepo, NewCooperationSiteUrlChannelRepo, NewAnalyticsRepo,
	NewChannelAdFormatRepo, NewPageURLAdFormatRepo, NewShardingTablesRepo, NewGameCountryRepo, NewAppCountrySummaryRepo, NewAdManagerSiteRepo, NewAnalyticsSiteRepo,
	NewJobLogRepo, NewTgAdRepo, NewCountryRepo, NewAdReductionRepo, NewPurchaseRepo, NewGamePurchaseSummaryRepo, NewBusinessSiteRepo, NewDeploymentHistoryRepo, NewSiteAdUnitRepo)

type GoogleService struct {
	adsense        *adsense.Service
	client         *stdhttp.Client
	analytics      *analyticsdata.Service
	analyticsAdmin *analyticsadmin.Service
}

func NewGoogleService(googleConf *conf.Google, logger log.Logger) (*GoogleService, error) {
	ctx := context.Background()
	var config = &oauth2.Config{
		ClientID:     googleConf.Configs[0].ClientID,     // from https://console.developers.google.com/project/<your-project-id>/apiui/credential
		ClientSecret: googleConf.Configs[0].ClientSecret, // from https://console.developers.google.com/project/<your-project-id>/apiui/credential
		Endpoint:     google.Endpoint,
		Scopes:       []string{adsense.AdsenseReadonlyScope},
		RedirectURL:  "https://developers.google.com",
	}
	options := option.WithTokenSource(config.TokenSource(ctx, &oauth2.Token{RefreshToken: googleConf.Configs[0].Code}))
	if googleConf.GetConfigs()[0].UseSa {
		options = option.WithCredentialsFile("./configs/sa.json")
	}
	client, _, err := http.NewClient(ctx, option.WithScopes(adsense.AdsenseReadonlyScope), options)
	if err != nil {
		panic(err)
	}
	//token := newOAuthClient(ctx, config)
	adsenseService, err := adsense.NewService(ctx, option.WithScopes(adsense.AdsenseReadonlyScope), options)
	if err != nil {
		panic(err)
	}

	gaSaOption := option.WithCredentialsFile("./configs/service_account.json")

	analyticsService, err := analyticsdata.NewService(ctx, option.WithScopes(analyticsdata.AnalyticsReadonlyScope), gaSaOption)
	if err != nil {
		panic(err)
	}

	analyticsAdminService, err := analyticsadmin.NewService(ctx, option.WithScopes(analyticsadmin.AnalyticsReadonlyScope), gaSaOption)
	if err != nil {
		panic(err)
	}

	return &GoogleService{
		adsense:        adsenseService,
		client:         client,
		analytics:      analyticsService,
		analyticsAdmin: analyticsAdminService,
	}, nil
}

// Data .
type Data struct {
	log   *log.Helper
	db    *ent.Client
	stddb *sql.DB
	gs    *GoogleService
}

// NewData .
func NewData(c *conf.Data, db *ent.Client, gs *GoogleService, logger log.Logger) (*Data, func(), error) {
	cleanup := func() {
		log.NewHelper(logger).Info("closing the data resources")
	}
	user, host, port, dbname, pwd := decodeSource(c.GetDatabase().GetSource())

	stddb, err := sql.Open("postgres", fmt.Sprintf("postgres://%s:%s@%s:%s/%s?sslmode=disable", user, pwd, host, port, dbname))
	//stddb, err := sql.Open("postgres", "postgres://postgres:123456@localhost/minigameData?sslmode=disable")
	if err != nil {
		log.Fatal(err)
	}
	stddb.SetConnMaxLifetime(time.Hour)
	stddb.SetMaxOpenConns(30)

	return &Data{
		gs:    gs,
		db:    db,
		stddb: stddb,
		log:   log.NewHelper(log.With(logger, "module", "data")),
	}, cleanup, nil
}

func NewEntClient(conf *conf.Data, logger log.Logger) *ent.Client {
	hlog := log.NewHelper(log.With(logger, "module", "adsense-bot/data/ent"))

	// Create an ent.Driver from `db`.
	drv, err := entsql.Open(conf.Database.Driver, conf.Database.Source)
	if err != nil {
		panic(err)
	}
	db := drv.DB()
	db.SetConnMaxLifetime(time.Hour)
	db.SetMaxOpenConns(10)
	client := ent.NewClient(ent.Driver(drv))
	// Run the auto migration tool.
	//if err := client.Schema.Create(context.Background(), migrate.WithForeignKeys(false)); err != nil {
	//	hlog.Fatalf("failed creating schema resources: %v", err)
	//	panic(err)
	//}
	if err := client.Schema.Create(
		context.Background(),
		schema.WithHooks(func(next schema.Creator) schema.Creator {
			return schema.CreateFunc(func(ctx context.Context, tables ...*schema.Table) error {
				var dynamicTables []*schema.Table
				for _, v := range tables {
					if !isPartitionTable(v.Name) { // todo 待其他表拆分后，删除
						nv := &schema.Table{
							Name:        v.Name,
							Columns:     v.Columns,
							Indexes:     v.Indexes,
							PrimaryKey:  v.PrimaryKey,
							ForeignKeys: v.ForeignKeys,
							Annotation:  v.Annotation,
							Comment:     v.Comment,
						}
						dynamicTables = append(dynamicTables, nv)
					} else {
						log.Debug(v.Name)

						//todo create channelcountryadformatpartition
					}
				}
				return next.Create(ctx, dynamicTables...)
			})
		}),
	); err != nil {
		hlog.Fatalf("failed creating schema resources: %v", err)
	}
	return client
}

func isPartitionTable(name string) bool {
	return name == channelcountryadformatpartition.Table ||
		name == sitecountryadformatpartition.Table ||
		name == urlchannelcountryadformatinfopartition.Table ||
		name == gamecountrypartition.Table ||
		name == channelcountryadformathkdpartition.Table ||
		name == sitecountryadformathkdpartition.Table ||
		name == urlchannelcountryadformatinfopartitionhkd.Table ||
		name == gamecountryhkdpartition.Table || name == siteadformathistory.Table || name == sitebusiness.Table
}

func decodeSource(value string) (user, host, port, dbname, pwd string) {
	// 分割字符串为键值对
	pairs := strings.Split(value, " ")
	// 解析键值对
	result := make(map[string]string)
	for _, pair := range pairs {
		parts := strings.SplitN(pair, "=", 2)
		if len(parts) == 2 {
			key := strings.TrimSpace(parts[0])
			val := strings.TrimSpace(parts[1])
			result[key] = val
		}
	}
	return result["user"], result["host"], result["port"], result["dbname"], result["password"]
}

func (d *Data) SaveLog(ctx context.Context, jobLog *biz.JobLog) error {
	err := d.db.JobLog.Create().SetJobName(jobLog.Name).SetIsSuccess(jobLog.IsSuccess).SetMessage(jobLog.Message).
		SetJobTime(jobLog.Date).SetDateRange(jobLog.DateRange).SetCurrencyCode(jobLog.CurrencyCode).Exec(ctx)
	if err != nil {
		return err
	}
	return nil
}
