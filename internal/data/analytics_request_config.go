package data

import (
	"google.golang.org/api/analyticsdata/v1beta"
)

// DimensionRequestConfig 维度请求配置
type DimensionRequestConfig struct {
	Name        string                            // 请求名称，用于标识
	Dimensions  []*analyticsdata.Dimension        // 维度配置
	Metrics     []*analyticsdata.Metric           // 指标配置
	Filters     []*analyticsdata.FilterExpression // 过滤器（可选）
	OrderBy     []*analyticsdata.OrderBy          // 排序（可选）
	Limit       int64                             // 限制条数
	Description string                            // 描述信息
}

// DimensionRequestBuilder 请求构建器
type DimensionRequestBuilder struct {
	configs []DimensionRequestConfig
}

// NewDimensionRequestBuilder 创建新的请求构建器
func NewDimensionRequestBuilder() *DimensionRequestBuilder {
	return &DimensionRequestBuilder{
		configs: make([]DimensionRequestConfig, 0),
	}
}

// AddRequest 添加请求配置
func (b *DimensionRequestBuilder) AddRequest(config DimensionRequestConfig) *DimensionRequestBuilder {
	b.configs = append(b.configs, config)
	return b
}

// AddUARequest 添加UA数据请求
func (b *DimensionRequestBuilder) AddUARequest() *DimensionRequestBuilder {
	return b.AddRequest(DimensionRequestConfig{
		Name: RequestNameUAData,
		Dimensions: []*analyticsdata.Dimension{
			{Name: DimensionHostName},
			{Name: DimensionDate},
			{Name: DimensionCustomEventUA},
		},
		Metrics: []*analyticsdata.Metric{
			{Name: MetricActiveUsers},
		},
		Limit:       limit,
		Description: "用户代理数据",
	})
}

// AddDeviceResolutionRequest 添加设备分辨率请求
func (b *DimensionRequestBuilder) AddDeviceResolutionRequest() *DimensionRequestBuilder {
	return b.AddRequest(DimensionRequestConfig{
		Name: RequestNameDeviceResolution,
		Dimensions: []*analyticsdata.Dimension{
			{Name: DimensionHostName},
			{Name: DimensionDate},
			{Name: DimensionCustomEventRes},
		},
		Metrics: []*analyticsdata.Metric{
			{Name: MetricActiveUsers},
		},
		Limit:       limit,
		Description: "设备分辨率及密度数据",
	})
}

// AddBrowserRequest 添加浏览器数据请求
func (b *DimensionRequestBuilder) AddBrowserRequest() *DimensionRequestBuilder {
	return b.AddRequest(DimensionRequestConfig{
		Name: RequestNameBrowserData,
		Dimensions: []*analyticsdata.Dimension{
			{Name: DimensionHostName},
			{Name: DimensionDate},
			{Name: DimensionBrowser},
		},
		Metrics: []*analyticsdata.Metric{
			{Name: MetricActiveUsers},
		},
		Limit:       limit,
		Description: "浏览器数据",
	})
}

// AddLanguageRequest 添加语言数据请求
func (b *DimensionRequestBuilder) AddLanguageRequest() *DimensionRequestBuilder {
	return b.AddRequest(DimensionRequestConfig{
		Name: RequestNameLanguageData,
		Dimensions: []*analyticsdata.Dimension{
			{Name: DimensionHostName},
			{Name: DimensionDate},
			{Name: DimensionLanguage},
		},
		Metrics: []*analyticsdata.Metric{
			{Name: MetricActiveUsers},
		},
		Limit:       limit,
		Description: "语言数据",
	})
}

// AddDpiRequest 添加DPI数据请求
func (b *DimensionRequestBuilder) AddDpiRequest() *DimensionRequestBuilder {
	return b.AddRequest(DimensionRequestConfig{
		Name: RequestNameDpi,
		Dimensions: []*analyticsdata.Dimension{
			{Name: DimensionHostName},
			{Name: DimensionDate},
			{Name: DimensionCustomEventDpi},
		},
		Metrics: []*analyticsdata.Metric{
			{Name: MetricActiveUsers},
		},
		Limit:       limit,
		Description: "设备DPI数据",
	})
}

// AddAudioFpRequest 添加音频指纹数据请求
func (b *DimensionRequestBuilder) AddAudioFpRequest() *DimensionRequestBuilder {
	return b.AddRequest(DimensionRequestConfig{
		Name: RequestNameAudioFp,
		Dimensions: []*analyticsdata.Dimension{
			{Name: DimensionHostName},
			{Name: DimensionDate},
			{Name: DimensionCustomEventAudioFP},
		},
		Metrics: []*analyticsdata.Metric{
			{Name: MetricActiveUsers},
		},
		Limit:       limit,
		Description: "音频指纹数据",
	})
}

// AddCanvasFpRequest 添加Canvas指纹数据请求
func (b *DimensionRequestBuilder) AddCanvasFpRequest() *DimensionRequestBuilder {
	return b.AddRequest(DimensionRequestConfig{
		Name: RequestNameCanvasFp,
		Dimensions: []*analyticsdata.Dimension{
			{Name: DimensionHostName},
			{Name: DimensionDate},
			{Name: DimensionCustomEventCanvasFP},
		},
		Metrics: []*analyticsdata.Metric{
			{Name: MetricActiveUsers},
		},
		Limit:       limit,
		Description: "Canvas指纹数据",
	})
}

// AddFontFpRequest 添加字体指纹数据请求
func (b *DimensionRequestBuilder) AddFontFpRequest() *DimensionRequestBuilder {
	return b.AddRequest(DimensionRequestConfig{
		Name: RequestNameFontFp,
		Dimensions: []*analyticsdata.Dimension{
			{Name: DimensionHostName},
			{Name: DimensionDate},
			{Name: DimensionCustomEventFontFP},
		},
		Metrics: []*analyticsdata.Metric{
			{Name: MetricActiveUsers},
		},
		Limit:       limit,
		Description: "字体指纹数据",
	})
}

// AddTzRequest 添加时区数据请求
func (b *DimensionRequestBuilder) AddTzRequest() *DimensionRequestBuilder {
	return b.AddRequest(DimensionRequestConfig{
		Name: RequestNameTz,
		Dimensions: []*analyticsdata.Dimension{
			{Name: DimensionHostName},
			{Name: DimensionDate},
			{Name: DimensionCustomEventTz},
		},
		Metrics: []*analyticsdata.Metric{
			{Name: MetricActiveUsers},
		},
		Limit:       limit,
		Description: "时区数据",
	})
}

// AddVisResRequest 添加可见分辨率数据请求
func (b *DimensionRequestBuilder) AddVisResRequest() *DimensionRequestBuilder {
	return b.AddRequest(DimensionRequestConfig{
		Name: RequestNameVisRes,
		Dimensions: []*analyticsdata.Dimension{
			{Name: DimensionHostName},
			{Name: DimensionDate},
			{Name: DimensionCustomEventVisRes},
		},
		Metrics: []*analyticsdata.Metric{
			{Name: MetricActiveUsers},
		},
		Limit:       limit,
		Description: "可见分辨率数据",
	})
}

// AddGlModRequest 添加显卡类型数据请求
func (b *DimensionRequestBuilder) AddGlModRequest() *DimensionRequestBuilder {
	return b.AddRequest(DimensionRequestConfig{
		Name: RequestNameGlMod,
		Dimensions: []*analyticsdata.Dimension{
			{Name: DimensionHostName},
			{Name: DimensionDate},
			{Name: DimensionCustomEventGlMod},
		},
		Metrics: []*analyticsdata.Metric{
			{Name: MetricActiveUsers},
		},
		Limit:       limit,
		Description: "显卡类型数据",
	})
}

// AddMiniLangRequest 添加mini_lang数据请求
func (b *DimensionRequestBuilder) AddMiniLangRequest() *DimensionRequestBuilder {
	return b.AddRequest(DimensionRequestConfig{
		Name: RequestNameMiniLang,
		Dimensions: []*analyticsdata.Dimension{
			{Name: DimensionHostName},
			{Name: DimensionDate},
			{Name: DimensionCustomEventMiniLang},
		},
		Metrics: []*analyticsdata.Metric{
			{Name: MetricActiveUsers},
		},
		Limit:       limit,
		Description: "mini_lang数据",
	})
}

// AddCustomDimensionRequest 添加自定义维度请求
func (b *DimensionRequestBuilder) AddCustomDimensionRequest(name, dimensionName, description string, additionalMetrics ...*analyticsdata.Metric) *DimensionRequestBuilder {
	metrics := []*analyticsdata.Metric{
		{Name: MetricActiveUsers},
	}
	metrics = append(metrics, additionalMetrics...)

	return b.AddRequest(DimensionRequestConfig{
		Name: name,
		Dimensions: []*analyticsdata.Dimension{
			{Name: DimensionHostName},
			{Name: DimensionDate},
			{Name: dimensionName},
		},
		Metrics:     metrics,
		Limit:       limit,
		Description: description,
	})
}

// BuildRequests 构建批量请求
func (b *DimensionRequestBuilder) BuildRequests(start, end string) []*analyticsdata.RunReportRequest {
	requests := make([]*analyticsdata.RunReportRequest, 0, len(b.configs))

	for _, config := range b.configs {
		request := &analyticsdata.RunReportRequest{
			DateRanges: []*analyticsdata.DateRange{
				{
					EndDate:   end,
					StartDate: start,
				},
			},
			Dimensions: config.Dimensions,
			Metrics:    config.Metrics,
			Limit:      config.Limit,
		}

		if len(config.Filters) > 0 {
			request.DimensionFilter = &analyticsdata.FilterExpression{
				AndGroup: &analyticsdata.FilterExpressionList{
					Expressions: config.Filters,
				},
			}
		}

		if len(config.OrderBy) > 0 {
			request.OrderBys = config.OrderBy
		}

		requests = append(requests, request)
	}

	return requests
}

// GetConfigs 获取配置列表
func (b *DimensionRequestBuilder) GetConfigs() []DimensionRequestConfig {
	return b.configs
}
