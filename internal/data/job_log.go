package data

import (
	"context"

	"git.minigame.vip/minicloud/service/adsense-bot/internal/biz"

	"github.com/go-kratos/kratos/v2/log"
)

type jobLogRepo struct {
	data *Data
	log  *log.Helper
}

func (r *jobLogRepo) Create(ctx context.Context, jobLog *biz.JobLog) error {
	err := r.data.db.JobLog.Create().SetJobName(jobLog.Name).SetIsSuccess(jobLog.IsSuccess).
		SetMessage(jobLog.Message).SetJobTime(jobLog.Date).SetDateRange(jobLog.DateRange).SetCurrencyCode(jobLog.CurrencyCode).Exec(ctx)
	if err != nil {
		return err
	}
	return nil
}

func NewJobLogRepo(data *Data, logger log.Logger) biz.JobLogRepo {
	return &jobLogRepo{
		data,
		log.NewHelper(log.With(logger, "module", "data/channel")),
	}
}
