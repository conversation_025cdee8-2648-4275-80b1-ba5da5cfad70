package server

import (
	"github.com/go-kratos/kratos/v2/log"
	"github.com/go-kratos/kratos/v2/middleware/logging"
	"github.com/go-kratos/kratos/v2/middleware/ratelimit"
	"github.com/go-kratos/kratos/v2/middleware/recovery"
	"github.com/go-kratos/kratos/v2/transport/http"
	"github.com/rs/cors"

	jobv1 "git.minigame.vip/minicloud/service/adsense-bot/api/job/v1"
	"git.minigame.vip/minicloud/service/adsense-bot/extern/miniutils/audit"
	"git.minigame.vip/minicloud/service/adsense-bot/internal/conf"
	"git.minigame.vip/minicloud/service/adsense-bot/internal/service"
)

// NewHTTPServer new an HTTP server.
func NewHTTPServer(c *conf.Server, job *service.JobService, logger log.Logger, auditConf *conf.Audit, auth *conf.Auth) *http.Server {
	var opts = []http.ServerOption{
		http.Middleware(
			recovery.Recovery(),
			logging.Server(logger),
			ratelimit.Server(),
			audit.NewAuditMiddleware(auditConf.GetAddr(), auth.ApiKey, logger).
				AddOperation(
					jobv1.OperationJobAdminStartJob,
				).
				UseHttp().
				WithTls(auditConf.Tls).
				Build(),
		),
		http.Filter(cors.New(cors.Options{
			AllowedOrigins:         []string{"*"},
			AllowOriginRequestFunc: nil,
			AllowedMethods:         []string{"GET", "PUT", "POST", "HEAD", "DELETE"},
			AllowedHeaders:         []string{"*"},
			MaxAge:                 12 * 60 * 60,
			AllowCredentials:       true,
		}).Handler,
		),
	}
	if c.Http.Network != "" {
		opts = append(opts, http.Network(c.Http.Network))
	}
	if c.Http.Addr != "" {
		opts = append(opts, http.Address(c.Http.Addr))
	}
	if c.Http.Timeout != nil {
		opts = append(opts, http.Timeout(c.Http.Timeout.AsDuration()))
	}
	srv := http.NewServer(opts...)
	jobv1.RegisterJobAdminHTTPServer(srv, job)
	return srv
}
