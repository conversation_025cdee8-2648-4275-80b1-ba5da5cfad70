package server

import (
	"context"
	"fmt"
	"mime"
	"os"
	"time"

	"github.com/emersion/go-imap/v2"

	"github.com/emersion/go-imap/v2/imapclient"
	"github.com/emersion/go-message/charset"
	"github.com/go-kratos/kratos/v2/log"

	"git.minigame.vip/minicloud/service/adsense-bot/internal/conf"
	"git.minigame.vip/minicloud/service/adsense-bot/internal/service"
)

type IMAPClient struct {
	log *log.Helper

	imapClient *imapclient.Client
	idleCmd    *imapclient.IdleCommand
	imapConf   *conf.Imap
	lastSeq    uint32
	fetchFunc  func(ctx context.Context, number uint32) error
}

func NewIMAPClient(imapConfig *conf.Imap, jobService *service.JobService, logger log.Logger) *IMAPClient {
	helper := log.NewHelper(log.With(logger, "module", "server/imap"))

	return &IMAPClient{
		imapConf:  imapConfig,
		log:       helper,
		fetchFunc: jobService.Fetch,
	}
}
func (s *IMAPClient) retryFetch(ctx context.Context, num uint32, maxRetries int, backoff time.Duration) error {
	var lastErr error
	for attempt := 0; attempt < maxRetries; attempt++ {
		if err := s.Fetch(ctx, num); err != nil {
			lastErr = err
			s.log.Warnf("Fetch attempt %d/%d failed: %v", attempt+1, maxRetries, err)

			// Wait before next retry, using exponential backoff
			waitTime := backoff * time.Duration(attempt+1)
			select {
			case <-ctx.Done():
				return ctx.Err()
			case <-time.After(waitTime):
				continue
			}
		}
		return nil // Success
	}
	return fmt.Errorf("all retry attempts failed, last error: %v", lastErr)
}

func (s *IMAPClient) connect(ctx context.Context) error {
	options := &imapclient.Options{
		WordDecoder: &mime.WordDecoder{CharsetReader: charset.Reader},
		DebugWriter: os.Stdout,
		UnilateralDataHandler: &imapclient.UnilateralDataHandler{
			Expunge: func(seqNum uint32) {
				s.log.Infof("message %v has been expunged", seqNum)
			},
			Mailbox: func(data *imapclient.UnilateralDataMailbox) {
				s.log.Infof("Received mailbox update - Name: %v, Messages: %v, Recent: %v",
					data.Flags, data.NumMessages, data.PermanentFlags)
				if data.NumMessages == nil {
					s.log.Warnf("No messages count received in mailbox update")
					return
				}
				ctx, cancel := context.WithTimeout(ctx, 2*time.Minute)
				defer cancel()

				// Log start of fetch operation
				s.log.Infof("Starting fetch for %d messages", *data.NumMessages)

				// Perform fetch with error handling
				if err := s.retryFetch(ctx, *data.NumMessages, 3, 1*time.Second); err != nil {
					s.log.Errorf("Failed to fetch messages after retries: %v", err)
					return
				}

				s.log.Infof("Successfully completed fetch for %d messages", *data.NumMessages)
			},
			Fetch: func(msg *imapclient.FetchMessageData) {

			},
		},
	}

	client, err := imapclient.DialTLS(s.imapConf.GetAddress(), options)
	if err != nil {
		return fmt.Errorf("dial error: %v", err)
	}

	if err := client.WaitGreeting(); err != nil {
		client.Close()
		return fmt.Errorf("greeting error: %v", err)
	}

	if err := client.Login(s.imapConf.GetUsername(), s.imapConf.GetPassword()).Wait(); err != nil {
		client.Close()
		return fmt.Errorf("login error: %v", err)
	}

	if d, err := client.Select("INBOX", nil).Wait(); err != nil {
		client.Close()
		return fmt.Errorf("select inbox error: %v", err)
	} else {
		s.lastSeq = d.NumMessages
	}

	s.imapClient = client
	err = s.readUnReadMessages(ctx)
	if err != nil {
		return err
	}

	idleCmd, err := s.imapClient.Idle()
	if err != nil {
		return fmt.Errorf("idle error: %v", err)
	}
	s.idleCmd = idleCmd

	return nil
}

func (s *IMAPClient) reconnectionLoop(ctx context.Context) {
	ticker := time.NewTicker(15 * time.Minute)
	defer ticker.Stop()

	for {
		select {
		case <-ctx.Done():
			return
		case <-ticker.C:
			s.log.Info("Performing scheduled IMAP reconnection")

			// Close existing connections
			if s.imapClient != nil {
				s.imapClient.Close()
			}
			if s.idleCmd != nil {
				s.idleCmd.Close()
			}

			// Reconnect
			if err := s.connect(ctx); err != nil {
				s.log.Errorf("Reconnection failed: %v", err)
				continue
			}

			s.log.Info("IMAP reconnection successful")
		}
	}
}
func (s *IMAPClient) Start(ctx context.Context) error {
	if s.imapConf.GetIsOpen() {
		options := &imapclient.Options{
			WordDecoder: &mime.WordDecoder{CharsetReader: charset.Reader},
			DebugWriter: os.Stdout,
			UnilateralDataHandler: &imapclient.UnilateralDataHandler{
				Expunge: func(seqNum uint32) {
					s.log.Infof("message %v has been expunged", seqNum)
				},
				Mailbox: func(data *imapclient.UnilateralDataMailbox) {
					s.log.Infof("Received mailbox update - Name: %v, Messages: %v, Recent: %v",
						data.Flags, data.NumMessages, data.PermanentFlags)
					if data.NumMessages == nil {
						s.log.Warnf("No messages count received in mailbox update")
						return
					}
					ctx, cancel := context.WithTimeout(ctx, 2*time.Minute)
					defer cancel()

					// Log start of fetch operation
					s.log.Infof("Starting fetch for %d messages", *data.NumMessages)

					// Perform fetch with error handling
					if err := s.retryFetch(ctx, *data.NumMessages, 3, 1*time.Second); err != nil {
						s.log.Errorf("Failed to fetch messages after retries: %v", err)
						return
					}

					s.log.Infof("Successfully completed fetch for %d messages", *data.NumMessages)
				},
				Fetch: func(msg *imapclient.FetchMessageData) {

				},
			},
		}

		client, err := imapclient.DialTLS(s.imapConf.GetAddress(), options)
		if err != nil {
			panic(err)
		}
		err = client.WaitGreeting()
		if err != nil {
			panic(err)
		}
		if err := client.Login(s.imapConf.GetUsername(), s.imapConf.GetPassword()).Wait(); err != nil {
			client.Close()
			panic(err)
		}

		if d, err := client.Select("INBOX", nil).Wait(); err != nil {
			client.Close()
			panic(err)
		} else {
			s.lastSeq = d.NumMessages
		}
		s.imapClient = client
		idleCmd, err := s.imapClient.Idle()
		if err != nil {
			panic(err)
		}
		s.idleCmd = idleCmd

		//if err := s.connect(ctx); err != nil {
		//	panic(err)
		//}

		// Start reconnection goroutine
		go s.reconnectionLoop(ctx)
	}

	return nil
}

func (s *IMAPClient) Stop(c context.Context) error {
	//if err := s.imapClient.Close(); err != nil {
	//	return err
	//}
	//if err := s.idleCmd.Close(); err != nil {
	//	return err
	//}
	return nil
}

func (s *IMAPClient) Fetch(ctx context.Context, num uint32) error {
	s.log.Infof("s.lastSeq :%d, num:%d", s.lastSeq, num)

	if s.lastSeq >= num {
		return nil
	}
	err := s.fetchFunc(ctx, num)
	if err != nil {
		return err
	}
	s.lastSeq = num
	return nil
}

// readUnReadMessages 读取未读消息，并将符合条件的邮件数据保存到数据库中
func (s *IMAPClient) readUnReadMessages(ctx context.Context) error {
	if _, err := s.imapClient.Select("INBOX", nil).Wait(); err != nil {
		return err
	}
	searchData, err := s.imapClient.Search(&imap.SearchCriteria{
		NotFlag: []imap.Flag{imap.FlagSeen},
	}, &imap.SearchOptions{
		ReturnAll: true,
	}).Wait()
	if err != nil {
		return err
	}
	for _, num := range searchData.AllSeqNums() {
		err := s.fetchFunc(ctx, num)
		if err != nil {
			return err
		}
	}
	return nil
}
