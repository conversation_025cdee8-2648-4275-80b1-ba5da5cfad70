# 如何使用zerolog库

## kratos框架下的使用

### 导入package

```
import "open-service/extern/miniutils/log/zerolog"
```

### 替换kratos原有的logger

```
	logger := log.With(zerolog.NewLogger(
            bc.Server.Name,
            zerolog.WithPath(bc.Log.Path),
            zerolog.WithCompress(bc.Log.Compress),
            zerolog.WithConsole(bc.Log.Console), 
    ),
"trace.id", tracing.TraceID(),
"span.id", tracing.SpanID(),
)
 
```
### 配置定义

```
message Log{
  // 日志存储路径
  string path = 1;
  // 日志级别
  string level = 2;
  // 日志文件大小
  int32 max_size =3;
  // 日志文件最大存储时间
  int32 max_age =4;
  // 日志文件最大存储数量
  int32 max_backups =5;
  // 日志文件备份是否压缩
  bool compress = 6;
  // 是否输出到控制台
  bool console = 7;
}
```