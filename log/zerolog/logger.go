package zerolog

import (
	"github.com/go-kratos/kratos/v2/log"
	"github.com/natefinch/lumberjack"
	"github.com/rs/zerolog"
	log2 "github.com/rs/zerolog/log"
	"os"
	"time"
)

var _ log.Logger = (*Logger)(nil)

type Logger struct {
	log *zerolog.Logger
}

type LogConfig struct {
	level           string
	path            string
	fileName        string
	maxSize         int
	maxAge          int
	maxBackups      int
	compress        bool
	console         bool
	callerSkipCount int
}

type Option func(lc *LogConfig)

func WithLevel(level string) Option {
	return func(lc *LogConfig) {
		lc.level = level
	}
}

func WithPath(path string) Option {
	return func(lc *LogConfig) {
		lc.path = path
	}
}

func WithFileName(fileName string) Option {
	return func(lc *LogConfig) {
		lc.fileName = fileName
	}
}

func WithMaxSize(maxSize int) Option {
	return func(lc *LogConfig) {
		lc.maxSize = maxSize
	}
}

func WithMaxAge(maxAge int) Option {
	return func(lc *LogConfig) {
		lc.maxAge = maxAge
	}
}

func WithMaxBackups(maxBackups int) Option {
	return func(lc *LogConfig) {
		lc.maxBackups = maxBackups
	}
}

func WithCompress(compress bool) Option {
	return func(lc *LogConfig) {
		lc.compress = compress
	}
}

func WithConsole(console bool) Option {
	return func(lc *LogConfig) {
		lc.console = console
	}
}

// WithCallerSkipCount 设置caller skip count
// 不同的日志库，包装的层数不一样，这里可以设置跳过的层数。例如：kratos的logger会多3层包装，这里callerSkipCount设置为3
func WithCallerSkipCount(callerSkipCount int) Option {
	return func(lc *LogConfig) {
		lc.callerSkipCount = callerSkipCount
	}
}

const (
	DefaultPath       = "./log"   // 默认保存目录
	DefaultFileName   = "srv.log" // 默认文件名
	DefaultMaxSize    = 50        // 默认50M
	DefaultMaxAge     = 7         // 默认保存七天
	DefaultMaxBackups = 5         // 默认5个备份
	DefaultCompress   = true      // 默认压缩
)

// NewLogger 创建一个新的kratos logger
// kratos的logger相对于zerolog的logger，性能有一些损失
func NewLogger(serviceName string, opts ...Option) log.Logger {
	opts = append(opts, WithCallerSkipCount(5)) // kratos logger包装了5层, 这里设置为5
	// 创建一个原始的zerolog logger
	logger := NewRaw(serviceName, opts...)
	// 包装一下
	return &Logger{
		log: &logger,
	}
}

// Log 实现kratos的Logger接口
func (l *Logger) Log(level log.Level, keyvals ...interface{}) (err error) {
	var event *zerolog.Event
	if len(keyvals) == 0 {
		return
	}
	if len(keyvals)%2 != 0 { // 如果不是偶数个参数，就补一个
		keyvals = append(keyvals, "")
	}

	switch level {
	case log.LevelDebug:
		event = l.log.Debug()
	case log.LevelInfo:
		event = l.log.Info()
	case log.LevelWarn:
		event = l.log.Warn()
	case log.LevelError:
		event = l.log.Error()
	case log.LevelFatal:
		event = l.log.Fatal()
	default:
	}

	for i := 0; i < len(keyvals); i += 2 {
		key, ok := keyvals[i].(string)
		if !ok {
			continue
		}
		event = event.Any(key, keyvals[i+1])
	}
	event.Send()
	return
}

// Raw 返回原始的zerolog logger
func (l *Logger) Raw() *zerolog.Logger {
	return l.log
}

func newLogConfig(serviceName string, opts ...Option) *LogConfig {
	lc := &LogConfig{
		level:      log.LevelDebug.String(),
		path:       DefaultPath,
		fileName:   serviceName + ".log",
		maxSize:    DefaultMaxSize,
		maxAge:     DefaultMaxAge,
		maxBackups: DefaultMaxBackups,
		compress:   DefaultCompress,
	}
	for _, o := range opts {
		o(lc)
	}
	return lc
}

// NewRaw 创建一个新的zerolog logger
// 如果是性能敏感的场景，可以直接使用zerolog.Logger
func NewRaw(serviceName string, opts ...Option) zerolog.Logger {
	lc := newLogConfig(serviceName, opts...)

	// 以lumberjack为基础，创建一个zerolog的logger
	lj := &lumberjack.Logger{
		Filename:   lc.path + "/" + lc.fileName,
		MaxSize:    lc.maxSize,
		MaxAge:     lc.maxAge,
		MaxBackups: lc.maxBackups,
		Compress:   lc.compress,
	}
	var (
		logger zerolog.Logger
		ctx    = log2.With()
	)
	// 设置caller skip count
	if lc.callerSkipCount > 0 {
		ctx = ctx.CallerWithSkipFrameCount(lc.callerSkipCount)
	}

	// 如果是console输出，就同时输出到console
	if lc.console {
		consoleWriter := zerolog.ConsoleWriter{Out: os.Stdout, TimeFormat: time.RFC3339}
		multi := zerolog.MultiLevelWriter(consoleWriter, lj)
		logger = ctx.Logger().Output(multi)
	} else {
		logger = ctx.Logger().Output(lj)
	}

	// 调整日志等级
	switch log.ParseLevel(lc.level) {
	case log.LevelDebug:
		logger = logger.Level(zerolog.DebugLevel)
	case log.LevelInfo:
		logger = logger.Level(zerolog.InfoLevel)
	case log.LevelWarn:
		logger = logger.Level(zerolog.WarnLevel)
	case log.LevelError:
		logger = logger.Level(zerolog.ErrorLevel)
	case log.LevelFatal:
		logger = logger.Level(zerolog.FatalLevel)
	}

	return logger
}
