package snowflake

import (
	"errors"
	"fmt"
	"math"
	"sync"
	"time"
)

const (
	goBeginTime = "2006-01-02 15:04:05" //go开始时间
	idBeginTime = "2023-09-26 00:00:00" //ID开始时间
)

// idDivider snowflake id divider
type idDivider struct {
	lastTime int64
	curIndex int64
	lock     *sync.Mutex
	regionId int64 //region id
	serverId int64 //server id
}

func newIdDivider(regionId, serverId int64) *idDivider {
	return &idDivider{
		regionId: regionId,
		serverId: serverId,
		lock:     &sync.Mutex{},
	}
}

const (
	bitsTimestamp = 42 // 时间戳占的位数
	bitsRegionId  = 5  // 区域占的位数，最多2^5=32个区域
	bitsServerId  = 5  // 服务节点数占的位数，最多2^5=32个区域
	bitsId        = 12 // id，最多2^12=4096个

	bitsShiftTimestamp = bitsRegionId + bitsServerId + bitsId
	bitsShiftRegionId  = bitsServerId + bitsId
	bitsShiftServerId  = bitsId
	bitsShiftId        = 0
)

var (
	idMaskTimestamp = uint64(math.Pow(2, bitsTimestamp)-1) << bitsShiftTimestamp
	idMaskRegion    = uint64(math.Pow(2, bitsRegionId)-1) << bitsShiftRegionId
	idMaskServer    = uint64(math.Pow(2, bitsServerId)-1) << bitsShiftServerId
	idMaskId        = uint64(math.Pow(2, bitsId)-1) << bitsShiftId
)

// genId generate unique id
// format:
// |      timestamp  (42bits, can used for 139 years) |  region id (5bits, 32 regions at most)  |  server id(5bits,
// 32 servers at most)  |    id index  (12bits, 4096 identities at most)     |
// |             相对时间，单位：毫秒        |               区域标识             |             服务器标识                   |                  id 计数器                      |
func (d *idDivider) genId(beginTimeMilliSecs int64) (uid uint64, err error) {
	//check region id
	if d.regionId > int64(math.Pow(2, bitsShiftRegionId)-1) ||
		d.regionId < 0 {
		//
		return 0, errors.New(fmt.Sprintf("region id(%d) invalid, valid range  [0, %d]", d.regionId, int64(math.Pow(2, bitsShiftRegionId)-1)))
	}

	//check server id
	if d.serverId > int64(math.Pow(2, bitsShiftServerId)-1) ||
		d.serverId < 0 {
		//
		return 0, errors.New(fmt.Sprintf("server id(%d) invalid, valid range  [0, %d]", d.serverId, int64(math.Pow(2, bitsShiftServerId)-1)))
	}

	d.lock.Lock()
	defer d.lock.Unlock()

	timestampPart := (time.Now().UnixNano()/int64(time.Millisecond) - beginTimeMilliSecs) << bitsShiftTimestamp
	regionPart := d.regionId << bitsShiftRegionId
	serverPart := d.serverId << bitsShiftServerId

	if d.lastTime != timestampPart {
		if d.lastTime > timestampPart { //check clock move backwards.
			return 0, fmt.Errorf("lastTime %d > timestampPart %d, maybe clock is moving backwards. Rejecting requests until %d", d.lastTime, timestampPart, d.lastTime)
		} else {
			d.curIndex = 0
			d.lastTime = timestampPart
		}
	} else {
		d.curIndex++
		//check if is overflow
		if d.curIndex > int64(math.Pow(2, bitsId)-1) {
			return 0, fmt.Errorf("id index %d is overflow.  valid range [0, %d]", d.curIndex, int64(math.Pow(2, bitsId)-1))
		}
	}

	return uint64(timestampPart | regionPart | serverPart | d.curIndex), nil
}

// IdTimestamp parse timestamp into readable string.
func IdTimestamp(timestamp int64) string {
	tm := time.Unix(int64((timestamp+mgr.beginTimeMilliSecs)/1000), 0)
	return tm.Format("2006-01-02 03:04:05 PM\n")
}

// IdDecode decode id to parts.
func IdDecode(id uint64) (timestamp, regionId, serverId, index int64) {
	timestamp = int64((id & idMaskTimestamp) >> bitsShiftTimestamp)
	regionId = int64((id & idMaskRegion) >> bitsShiftRegionId)
	serverId = int64((id & idMaskServer) >> bitsShiftServerId)
	index = int64((id & idMaskId) >> bitsShiftId)
	return
}

// idManager id manager
type idManager struct {
	beginTimeMilliSecs int64
	dividers           []*idDivider
}

func newIdManager(num int, regionId, serverId int64) (mgr *idManager) {
	mgr = &idManager{
		dividers: make([]*idDivider, 0, num),
	}

	for i := 0; i < num; i++ {
		mgr.dividers = append(mgr.dividers, newIdDivider(regionId, serverId))
	}

	return
}

// init init id manager
func (m *idManager) init() (err error) {
	beginTime, err := time.Parse(goBeginTime, idBeginTime)
	if err != nil {
		return
	}
	m.beginTimeMilliSecs = beginTime.UnixNano() / int64(time.Millisecond)
	return
}

var mgr *idManager

// Init 初始化
func Init(num int, regionId, serverId int64) error {
	mgr = newIdManager(num, regionId, serverId)
	return mgr.init()
}

// Gen 根据类型生成id
func Gen(idType int) (id uint64, err error) {
	if idType < 0 || idType >= len(mgr.dividers) {
		return 0, errors.New(fmt.Sprintf("wrong id type %d", idType))
	}

	return mgr.dividers[idType].genId(mgr.beginTimeMilliSecs)
}

//func IdMgrInit(idDivderSize int) (err error) {
//	regionId, serverId, _ := GetServerId()
//	return Init(idDivderSize, regionId, serverId)
//}

//// get region id of specific id.
//func GetRegionId(id, regionNum uint64) uint64 {
//	return id % regionNum
//}
