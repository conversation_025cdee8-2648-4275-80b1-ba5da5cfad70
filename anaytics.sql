SELECT SUM("site_ad_formats"."impressions")                                               AS "impressions",
       SUM("site_ad_formats"."clicks")                                                    AS "clicks",
       SUM("site_ad_formats"."ad_requests")                                               AS "ad_requests",
       SUM("site_ad_formats"."matched_ad_requests")                                       AS "matched_ad_requests",
       CAST(ROUND(SUM(site_ad_formats.estimated_earnings)::numeric, 2) AS numeric(20, 2)) AS "estimated_earnings",
       CASE
           WHEN SUM(site_ad_formats.clicks) > 0 THEN ROUND(
                   (SUM(site_ad_formats.estimated_earnings) / SUM(site_ad_formats.clicks))::numeric, 4)
           ELSE 0 END                                                                     AS "cost_per_click",
       CASE
           WHEN SUM(site_ad_formats.impressions) > 0 THEN ROUND(
                   (SUM(site_ad_formats.clicks)::numeric / SUM(site_ad_formats.impressions))::numeric, 4)
           ELSE 0 END                                                                     AS "impressions_ctr",
       CASE
           WHEN SUM(site_ad_formats.ad_requests) > 0 THEN ROUND(
                   (SUM(site_ad_formats.matched_ad_requests)::numeric / SUM(site_ad_formats.ad_requests))::numeric, 4)
           ELSE 0 END                                                                     AS "ad_requests_coverage",
       CASE
           WHEN SUM(site_ad_formats.impressions) > 0 THEN ROUND(
                   (SUM(site_ad_formats.estimated_earnings) / SUM(site_ad_formats.impressions) * 1000)::numeric, 4)
           ELSE 0 END                                                                     AS "impressions_rpm",
       CASE
           WHEN MAX(t2.page_views) > 0 THEN ROUND(
                   (SUM(site_ad_formats.estimated_earnings) / MAX(t2.page_views) * 1000)::numeric, 4)
           ELSE 0 END                                                                     AS "page_views_rpm",
       CASE
           WHEN SUM(site_ad_formats.impressions) > 0 THEN
               SUM(site_ad_formats.impressions * site_ad_formats.active_view_viewability) /
               SUM(site_ad_formats.impressions)
           ELSE 0 END                                                                     AS "active_view_viewability",
       CASE
           WHEN MAX(t2.page_views) > 0 THEN ROUND(
                   (SUM(site_ad_formats.impressions)::numeric / MAX(t2.page_views))::numeric, 4)
           ELSE 0 END                                                                     AS "impressions_per_page_view",
       "site_ad_formats"."site",
       "site_ad_formats"."date",
       max(t2.page_views)                                                                 AS page_views
FROM "site_ad_formats"
         LEFT JOIN "cooperation_site_url_channels" AS "t1" ON "t1"."site" = "site_ad_formats"."site"
         LEFT JOIN "sites" AS "t2" ON "t2"."site" = "site_ad_formats"."site" AND "t2"."date" = "site_ad_formats"."date"
WHERE (
    (
        (
            ("site_ad_formats"."date" >= '2025-07-31 16:00:00 +0000 ' AND
             "site_ad_formats"."date" <= '2025-08-06 16:00:00 +0000 ')
                AND "url_channel" = ''
            )
            AND "t1"."deleted_at" IS NULL
        )
        AND ("t1"."stopping_time" = '0001-01-01 00:00:00 +0000 ' OR "site_ad_formats"."date" < "stopping_time")
    )
  AND (
    (
        NOT (
            "site_ad_formats"."site" = 'htv11o.minigamebm.com' AND
            "site_ad_formats"."date" >= '2025-08-01 16:00:00 +0000 '
                OR "site_ad_formats"."site" = 'ncmjhz.minigamebm.com' AND
                   ("site_ad_formats"."date" >= '2025-07-31 16:00:00 +0000 ' AND
                    "site_ad_formats"."date" < '2025-08-02 16:00:00 +0000 ')
                OR "site_ad_formats"."site" = '9cm5kh.minigamebm.com' AND
                   "site_ad_formats"."date" >= '2025-07-31 16:00:00 +0000 '
                OR "site_ad_formats"."site" = 'fpzxbi123.minigamebm.com' AND
                   ("site_ad_formats"."date" >= '2025-08-01 16:00:00 +0000 ' AND
                    "site_ad_formats"."date" < '2025-08-04 16:00:00 +0000 ')
                OR "site_ad_formats"."site" = 'digpn9.minigamebm.com' AND
                   "site_ad_formats"."date" >= '2025-08-06 16:00:00 +0000 '
            )
        )
        OR (
        (
            "site_ad_formats"."site" = 'htv11o.minigamebm.com' AND
            "site_ad_formats"."date" >= '2025-08-01 16:00:00 +0000 '
                OR "site_ad_formats"."site" = 'ncmjhz.minigamebm.com' AND
                   ("site_ad_formats"."date" >= '2025-07-31 16:00:00 +0000 ' AND
                    "site_ad_formats"."date" < '2025-08-02 16:00:00 +0000 ')
                OR "site_ad_formats"."site" = '9cm5kh.minigamebm.com' AND
                   "site_ad_formats"."date" >= '2025-07-31 16:00:00 +0000 '
                OR "site_ad_formats"."site" = 'fpzxbi123.minigamebm.com' AND
                   ("site_ad_formats"."date" >= '2025-08-01 16:00:00 +0000 ' AND
                    "site_ad_formats"."date" < '2025-08-04 16:00:00 +0000 ')
                OR "site_ad_formats"."site" = 'digpn9.minigamebm.com' AND
                   "site_ad_formats"."date" >= '2025-08-06 16:00:00 +0000 '
            )
            AND "site_ad_formats"."ad_format" IN ('MANUAL_INTERSTITIAL', 'MANUAL_REWARDED')
        )
    )
GROUP BY "site_ad_formats"."date", "site_ad_formats"."site"
