package email

import (
	"context"
	"crypto/tls"
	"github.com/google/wire"
	"gopkg.in/gomail.v2"
)

// ProviderSet is email notify providers.
var ProviderSet = wire.NewSet(NewNotifyEmail)

type NotifyEmail struct {
	Host     string
	Port     int
	UserName string
	PassWord string
}

type Config struct {
	Host     string
	Port     int32
	Username string
	Password string
}

type Value struct {
	// 标题
	Title string
	// 接受者
	To []string
	//内容
	Content string
}

func NewNotifyEmail(config *Config) *NotifyEmail {
	return &NotifyEmail{
		Host:     config.Host,
		Port:     int(config.Port),
		UserName: config.Username,
		PassWord: config.Password,
	}
}

func (n *NotifyEmail) SendEmail(ctx context.Context, value Value) error {
	m := gomail.NewMessage()
	//m.SetHeader("From", "<EMAIL>", "微游开发者服务团队")
	m.SetAddressHeader("From", "<EMAIL>", "微游开发者服务团队")
	m.SetHeader("To", value.To...)
	m.SetHeader("Subject", value.Title)
	m.SetBody("text/html; charset=UTF-8", value.Content)
	d := gomail.NewDialer(n.Host, n.Port, n.UserName, n.PassWord)
	d.TLSConfig = &tls.Config{InsecureSkipVerify: true}
	if err := d.DialAndSend(m); err != nil {
		return err
	}
	return nil
}
