-- 修复page_url系列表中的appID字段
-- 针对 televs.com 和 *.televs.com/twa/game?appId= 格式的URL（支持主域名和任意子域名）
-- 根据FormatURL函数逻辑，从查询参数appId中提取游戏名称作为appID

-- 开始事务
BEGIN;

-- 1. 修复 page_urls 表（主表，无appID字段，跳过）

-- 2. 修复 page_url_infos 表
-- 使用正则表达式匹配 televs.com 和 *.televs.com/twa/game?appId= 格式的URL
UPDATE page_url_infos
SET app_id = SUBSTRING(page_url FROM 'https://([^.]*\.)?televs\.com/twa/game\?appId=([^&]+)', 2)
WHERE page_url ~ '^https://([^.]*\.)?televs\.com/twa/game\?appId=.+';

-- 3. 修复 page_url_info_hkd 表
UPDATE page_url_info_hkd
SET app_id = SUBSTRING(page_url FROM 'https://([^.]*\.)?televs\.com/twa/game\?appId=([^&]+)', 2)
WHERE page_url ~ '^https://([^.]*\.)?televs\.com/twa/game\?appId=.+';

-- 4. 修复 page_url_ad_format_infos 表
UPDATE page_url_ad_format_infos
SET app_id = SUBSTRING(page_url FROM 'https://([^.]*\.)?televs\.com/twa/game\?appId=([^&]+)', 2)
WHERE page_url ~ '^https://([^.]*\.)?televs\.com/twa/game\?appId=.+';

-- 5. 修复 page_url_ad_format_info_hkd 表
UPDATE page_url_ad_format_info_hkd
SET app_id = SUBSTRING(page_url FROM 'https://([^.]*\.)?televs\.com/twa/game\?appId=([^&]+)', 2)
WHERE page_url ~ '^https://([^.]*\.)?televs\.com/twa/game\?appId=.+';

-- 显示修复结果统计
SELECT 'page_url_infos' as table_name, COUNT(*) as updated_rows
FROM page_url_infos
WHERE page_url ~ '^https://([^.]*\.)?televs\.com/twa/game\?appId=.+'

UNION ALL

SELECT 'page_url_info_hkd' as table_name, COUNT(*) as updated_rows
FROM page_url_info_hkd
WHERE page_url ~ '^https://([^.]*\.)?televs\.com/twa/game\?appId=.+'

UNION ALL

SELECT 'page_url_ad_format_infos' as table_name, COUNT(*) as updated_rows
FROM page_url_ad_format_infos
WHERE page_url ~ '^https://([^.]*\.)?televs\.com/twa/game\?appId=.+'

UNION ALL

SELECT 'page_url_ad_format_info_hkd' as table_name, COUNT(*) as updated_rows
FROM page_url_ad_format_info_hkd
WHERE page_url ~ '^https://([^.]*\.)?televs\.com/twa/game\?appId=.+';

-- 提交事务
COMMIT;

-- 验证修复结果的查询语句
-- 可以单独运行以检查修复是否成功
/*
SELECT page_url, app_id, sub_channel, page_type
FROM page_url_infos
WHERE page_url ~ '^https://([^.]*\.)?televs\.com/twa/game\?appId=.+'
ORDER BY page_url;

-- 查看所有匹配的URL示例，按域名分组
SELECT DISTINCT SUBSTRING(page_url FROM '^https://([^/]+)') as domain,
       COUNT(*) as url_count
FROM page_url_infos
WHERE page_url ~ '^https://([^.]*\.)?televs\.com/twa/game\?appId=.+'
GROUP BY SUBSTRING(page_url FROM '^https://([^/]+)')
ORDER BY domain;
*/
