syntax = "proto3";

package openuser.v1;

import "errors/errors.proto";

option go_package = "open-proto/open-user/api/extern;extern";

enum ErrorReason {
  option (errors.default_code) = 500;

  UNKNOWN_ERROR = 0;

  // 验证码相关 [1, 500)
  CAPTCHA_GENERATE_FAILED = 1[(errors.code) = 500];  // 生成校验码失败
  CAPTCHA_GENERATE_TOO_FREQUENTLY = 2[(errors.code) = 400]; // 校验码生成过频
  CAPTCHA_SEND_EMAIL_FAILED = 3[(errors.code) = 400]; // 发送邮件失败
  CAPTCHA_EXPIRED_OR_NOT_EXIST = 4[(errors.code) = 400]; // 验证码已失效或者为生成
  CAPTCHA_DOES_NOT_MATCH = 5[(errors.code) = 400]; // 验证码已失效或者为生成
  CAPTCHA_MOBILE_INCORRECT = 6[(errors.code) = 400]; // 你输入的 手机号 与预留的手机号不符，请重新输入！
  CAPTCHA_EMAIL_INCORRECT = 7[(errors.code) = 400]; //  你输入的 邮箱 与预留的邮箱不符，请重新输入！


  // User 相关 [500, 1000)
  USER_PASSWORD_INVALID = 500[(errors.code) = 500];  // 用户密码无效
  USER_ACCOUNT_INCORRECT = 501[(errors.code) = 400];  // 账号输入错误或此账号未注册，请重新输入，如您暂未注册账号请点击注册
  USER_PASSWORD_INCORRECT = 502[(errors.code) = 400];  // 密码输入错误，请重新输入，如您已遗忘密码请点击忘记密码！
  USER_RETRIEVE_FAILED = 503[(errors.code) = 400]; // 找回密码错误
  USER_MOBILE_INCORRECT = 504[(errors.code) = 400]; // 你输入的 手机号 与预留的手机号不符，请重新输入！
  USER_EMAIL_INCORRECT = 505[(errors.code) = 400]; //  你输入的 邮箱 与预留的邮箱不符，请重新输入！
  USER_SAME_PASSWORD = 506[(errors.code) = 400]; //新密码与原密码相同，请重新输入新的修改密码！
  USER_PASSWORD_NOT_MATCH = 507[(errors.code) = 400]; //您的原始密码输入错误，请重新输入，如已遗忘请点击忘记密码！
  USER_NOT_FOUND = 508[(errors.code) = 400]; // 用户不存在



  // CP 相关 [1000, 2000)
  CP_EMAIL_EXISTS = 1000[(errors.code) = 400];
  CP_MOBILE_EXISTS = 1001[(errors.code) = 400];
  MERCHANT_NOT_FOUND = 1002[(errors.code) = 400];
  CP_NAME_INVALID = 1003[(errors.code) = 400];
  CP_EMAIL_INVALID = 1004[(errors.code) = 400];
  CP_LOGIN_FAILED = 1005[(errors.code) = 500];
  CP_MOBILE_INVALID = 1006[(errors.code) = 400];
  CP_INFO_INVALID = 1007[(errors.code) = 400];
  CP_SIGN_FAILED = 1008[(errors.code) = 400];
  CP_IS_EXISTS = 1009[(errors.code) = 400]; //cp已存在
  CP_NAME_EXIST = 1010[(errors.code) = 400];  //该名称已被注册，请重新输入！
  CP_IS_NOT_EXISTS = 1011[(errors.code) = 400]; //cp不存在
  CP_INFO_CreditCode_INVALID = 1012[(errors.code) = 400];//  该代码已被注册，请重新输入！
  INSPECT_AUTH_STATE_CHANGED  = 1013[(errors.code) = 400];//您当前审核状态有变化，将为您自动刷新 3S ！
  INSPECT_PARAMETER_NOT_CHANGED  = 1014[(errors.code) = 400];//存在未修改字段 ！
  INSPECT_EXISTS = 1015[(errors.code) = 400]; //已存在认证、不能重复创建
  CP_MOBILE_HAS_BEEN_BOUND = 1016[(errors.code) = 400];//当前手机号已被绑定，请输入正确的手机号！
  CP_EMAIL_HAS_BEEN_BOUND = 1017[(errors.code) = 400];//当前邮箱已被绑定，请输入正确的邮箱！
  CP_EN_NAME_EXIST = 1018[(errors.code) = 400];  //该英文名称已被注册，请重新输入！
  CP_FULL_NAME_EXIST = 1019[(errors.code) = 400];  //该厂商全称已被注册，请重新输入！
  UPLOAD_FILE_TOO_FREQUENTLY = 1998[(errors.code) = 400]; // 上传文件太频繁
  PARAMETER_ERROR = 1999[(errors.code) = 400];  // 参数错误

  // 消息相关错误 [2001, 2500)
  MESSAGE_NOT_EXIST = 2001[(errors.code)  = 404 ];
  USER_MESSAGE_NOT_EXIST = 2002[(errors.code)  = 404 ];

}
