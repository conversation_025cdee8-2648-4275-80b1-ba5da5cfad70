syntax = "proto3";

package user.v1;

option go_package = "open-proto/open-user/api/extern;extern";

// The user service definition.
service Captcha {
  // 为手机生成验证码
  rpc CaptchaByMobile (CaptchaByMobileRequest) returns (CaptchaByMobileReply) {
  }
  // 为邮箱生成验证码
  rpc CaptchaByEmail (CaptchaByEmailRequest) returns (CaptchaByEmailReply) {
  }
}

message CaptchaByMobileRequest {
  // 国家区号
  string national_code = 1;
  // 手机号
  string mobile = 2;
  // 验证码类型(1,登入; 2,密码找回)
  uint32 types = 3;
}

message CaptchaByMobileReply {
}

message CaptchaByEmailRequest {
  // 邮箱地址
  string email = 1;
  // 验证码类型(1,登入; 2,密码找回)
  uint32 types = 2;
}

message CaptchaByEmailReply {
}