syntax = "proto3";

package user.v1;

import "open-proto/open-common/merchant.proto";
import "validate/validate.proto";
import "google/protobuf/timestamp.proto";

option go_package = "open-proto/open-user/api/extern;extern";

// 用户
service User {
  // 用户登入状态
  rpc GetUserLoginState (GetUserLoginStateRequest) returns (GetUserLoginStateReply) {
  }
  rpc GetMerchantByUid(GetMerchantByUidRequest) returns (GetMerchantByUidReply) {
  }
  rpc GetMerchantInfo(GetMerchantInfoRequest) returns (GetMerchantInfoReply) {
  }
}

message GetMerchantByUidRequest{
  uint64 uid = 1;
}

message GetMerchantByUidReply{
  common.merchant.v1.MerchantInfo merchant = 1;
}

message GetMerchantInfoRequest{
  uint64 merchant_id = 1;
}

message GetMerchantInfoReply{
  common.merchant.v1.MerchantInfo merchant = 1;
}


// 登出时间请求
message GetUserLoginStateRequest {
  // uid
  uint64 id = 1; // uid
}
// 登出返回
message GetUserLoginStateReply {
  // 营业开始时间
  optional google.protobuf.Timestamp logout_time = 1;
  // 营业开始时间
  optional uint32 state = 2;
}
