syntax = "proto3";

package user.v1;

option go_package = "open-proto/open-user/api/extern;extern";

//import "google/api/field_behavior.proto";
service Upload {
  // 为手机生成验证码
  rpc CheckUploadCount (CheckUploadCountRequest) returns (CheckUploadCountReply) {
  }
  // 为邮箱生成验证码
  rpc IncrUploadCount (IncrUploadCountRequest) returns (IncrUploadCountReply) {
  }
}

message CheckUploadCountRequest{
  // uid
  uint64 uid = 1;
}
message CheckUploadCountReply{
}

message IncrUploadCountRequest{
  // uid
  uint64 uid = 1;
}
message IncrUploadCountReply{
}
