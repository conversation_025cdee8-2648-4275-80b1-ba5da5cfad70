syntax = "proto3";

package publisher.v1;

import "validate/validate.proto";
import "google/protobuf/timestamp.proto";
import "google/protobuf/field_mask.proto";

option go_package = "open-proto/open-common/api/extern;extern";

message MerchantChannelStateDetail{
  uint32 state = 1;
  google.protobuf.Timestamp at = 2;
}

message MerchantChannel {
  // 唯一id
  optional uint64 id = 1;
  // 商户id
  optional uint64 merchant_id = 2;
  // 渠道名称
  optional string name = 3;
  // 域名
  optional string domain = 4;
  // 流量来源，0 未知 1 Mintegral 2 Applovin 3 Google 4 Meta 5 TikTok 6 Snapchat 7 现有用户 8 其他广告平台
  repeated uint32 traffic_source=5;
  // 行业类型
  optional uint32 industry_type = 6;
  // 用户分布，为cldr国家/地区编码列表
  repeated string user_area = 7;
  // 用户量级（DAU），0 未知 1 [0, 10_000) 2 [10_000, 50_000) 3 [50_000,200_000 4 [200_000, 500_000) 5 [500_000, 1_000_000) 6 [1_000_000, 2_000_000) 7 [2_000_000, +∞)
  optional uint32 user_scale=8;
  // 媒体类型，0 未知 1 网站 2 google商店 3 苹果商店 4 其他应用市场
  optional uint32 media_type = 9;
  // 是否合作过h5游戏，false：否，true：是
  optional bool use_to_cooperate_with_h5 = 10;
  // 用户入口示意图列表
  repeated string user_entry_pics = 11;
  // 模板id
  optional uint32 template = 12;
  // 游戏app_id列表
  repeated string games = 13;
  // 部署状态，0 未知 1 待部署 2 部署完成
  optional uint32 state = 14;
  // 发布状态，0 未知 1 未发布 2 审核通过 3 已发布 4 已下架 5 违规下架 6 已删除
  optional uint32 publish_state = 15;
  // 状态描述
  repeated MerchantChannelStateDetail state_detail = 16;
  // 创建时间
  optional google.protobuf.Timestamp create_time = 17;
  // 更新时间
  optional google.protobuf.Timestamp update_time = 18;
}
