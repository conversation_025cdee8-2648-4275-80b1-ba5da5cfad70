syntax = "proto3";

package common.merchant.v1;

import "validate/validate.proto";
import "google/protobuf/timestamp.proto";

option go_package = "open-proto/open-common/api/extern;extern";

message MerchantInfo{
  // 发行商id
  optional uint64 id = 1;
  // 超级管理员账户 id
  optional uint64 su = 2;
  // 名称
  optional string name = 3;
  // 英文名称
  optional string en_name = 4;
  // 全称
  optional string full_name = 5;
  // 注册地区行政编码
  repeated string reg_region_code = 6;
  // 注册地址
  optional string reg_address = 7;
  // 统一社会信用代码
  optional string credit_code = 8;
  // 营业开始时间
  optional google.protobuf.Timestamp trade_begin_time = 9;
  // 营业结束时间
  optional google.protobuf.Timestamp trade_end_time = 10;
  // 营业结束时间是否长期
  optional bool is_trade_endless = 34;
  // 营业执照扫描件url
  optional string license_url = 11;
  // 邮箱
  optional string email = 12 [ (validate.rules).string.email = true ];
  // 联系人
  optional string contact_name = 33;
  // 0 显示状态 未知， 1 未认证，2 审核中，3 已认证
  optional uint32 state = 15;
  // 客户经理id
  repeated uint64 manager_id = 16;
  // 客户经理(企业邮箱格式)
  repeated string manager_name = 17;
  // 状态: true 启用、false 禁用
  optional bool enable = 20;
  // QQ
  optional string qq = 21;
  // WeiXin
  optional string wechat = 22;
  // 认证状态: 1.未认证；2已认证
  optional uint32 real_state = 23;
  // 注册时间
  optional google.protobuf.Timestamp create_at = 25;
  // 审核人(企业邮箱格式)
  optional string confirm_name = 26;
  // 审核时间
  optional google.protobuf.Timestamp confirm_at = 27;
  // 认证时间（审核通过时间）
  optional google.protobuf.Timestamp pass_at = 32;
  // 来源（1.注册 2.后台 3.迁移）
  optional uint32 from = 28;
  // 账号状态: true 启用、false 禁用
  optional bool user_enable = 29;
  // 是否存在审核数据
  bool exist_inspect = 30;
  // 用户注册邮箱(后台使用)
  string user_email = 31;
  // 联系人，国家区号
  optional string contact_national_code = 35;
  // 联系人手机号
  optional string contact_mobile = 36;
}