syntax = "proto3";

package metadata.v1;

import "google/protobuf/timestamp.proto";

option go_package = "open-proto/open-common/api/extern;extern";
// metadata条目
message MetadataItem {
  // 名称
  string name = 1;
  // 枚举
  uint32 value = 2;
}

message LanguageItem {
  // 名称
  string name = 1;
  // 枚举
  uint32 value = 2;
  // 语言代码
  string code = 3;
}

// metadata关系条目
message MetadataRelationItem {
  // 父级
  uint32 parent = 1;
  // 子级
  uint32 son = 2;
}

// 游戏引擎信息
message MetadataGameEngineInfo {
  // 名称
  string name = 1;
  // 枚举
  uint32 value = 2;
  // 游戏引擎版本列表
  repeated .metadata.v1.MetadataItem versions= 3;
}

message MetadataGameEngineListRequest{}
message MetadataGameEngineListReply{
  // 游戏引擎列表
  repeated MetadataGameEngineInfo data = 1;
}

message MetadataGameTypeInfo {
  // 名称
  string name = 1;
  // 枚举
  uint32 value = 2;
  // 游戏类型下的推荐标签id列表
  repeated uint32 tags = 3;
  // 图标
  string icon = 4;
  // 排除推荐的
  repeated uint32 other_tags = 5;
}

message MetadataGameTypeListI18nRequest { 
  // 语言
  string lang = 1;
}

message MetadataGameTagListI18nRequest { 
  // 语言
  string lang = 1;
}

message MetadataGameLanguageListI18nRequest { 
  // 语言
  string lang = 1;
}

message MetadataGameTypeListRequest {}
message MetadataGameTypeListReply {
  // 游戏类型列表
  repeated .metadata.v1.MetadataGameTypeInfo data = 1;
}

message MetadataGameTypeGameUsedListReply {
  // 游戏类型列表
  repeated .metadata.v1.MetadataGameTypeInfo data = 1;
}

message MetadataGameTagListRequest {}
message MetadataGameTagListReply {
  // 游戏Tag列表
  repeated .metadata.v1.MetadataItem data = 1;
}

// 游戏语言分组
message MetadataGameLanguageGroupInfo {
  // 名称
  string name = 1;
  // 枚举
  uint32 value = 2;
  // 分组下的语言列表
  repeated .metadata.v1.MetadataItem languages = 3;
}

message MetadataGameLanguageListRequest {}
message MetadataGameLanguageListReply {
  // 游戏语言列表
  repeated MetadataGameLanguageGroupInfo data = 1;
}

message MetadataLanguageListReply {
  repeated .metadata.v1.LanguageItem data = 1;
}

// metadata表原始数据
message MetadataRawData {
  optional .google.protobuf.Timestamp delete_at = 1;
  uint32 type = 2;
  uint32 subtype = 3;
  uint32 subtype1 = 4;
  uint32 subtype2 = 5;
  string label = 6;
  string remark = 7;
  string extension = 8;
}

message MerchantChannelTemplate {
  // 模板id
  uint32 id = 1;
  // 模板名称
  string name = 2;
  // 模板分类
  uint32 type = 3;
  // 游戏链接规则
  string game_link_rule = 7;
  // 模板图片url
  string url = 5;
  // 模板图片缩略图url
  string thumbnail_url = 6;
}

message MerchantChannelTemplateType {
  // 模板分类id
  uint32 id = 1;
  // 模板分类名称
  string name = 2;
}

message MetadataMerchantChannelTemplateListRequest {
  string lang = 1;
}

message MetadataMerchantChannelTemplateTypeListRequest {
  string lang = 1;
}

message MetadataMerchantChannelTemplateListReply {
  // 模板列表
  repeated MerchantChannelTemplate data = 1;
}

message MetadataMerchantChannelTemplateTypeListReply {
  // 模板分类列表
  repeated MerchantChannelTemplateType data = 1;
}

message MetadataMerchantChannelTemplateByStrategyRequest {
  // 语言
  string lang = 1;
}

message MetadataMerchantChannelTemplateByStrategy {
  // 变现策略
  uint32 strategy = 1;
  // 模板列表
  repeated MerchantChannelTemplate data = 2;
}

message MetadataMerchantChannelTemplateByStrategyReply {
  repeated MetadataMerchantChannelTemplateByStrategy data = 1;
}


message MetadataMerchantIndustryListRequest{
  // 语言
  string lang = 1;
}

message MetadataMerchantIndustryListReply {
  // 行业列表
  repeated MetadataItem data = 1;
}
