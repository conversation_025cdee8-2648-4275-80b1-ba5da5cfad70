syntax = "proto3";

package publisher.v1;

import "validate/validate.proto";
import "google/protobuf/timestamp.proto";
import "google/protobuf/field_mask.proto";

option go_package = "open-proto/open-common/api/extern;extern";

message Publisher {
  // 唯一id
  optional uint64 id = 1;
  // 公司名
  optional string company_name = 2;
  // 联系人
  optional string contact_person = 3;
  // 联系方式
  optional string contact_number = 4 [ (validate.rules).string.min_len = 5 ];
  // 联系邮箱
  optional string contact_email = 5 [ (validate.rules).string.email = true ];
  // 联系状态,0 未知、1 待联系、2 已联系、3 确认合作、4 取消合作
  optional uint32 state = 6;
  // 来源,0、未知 1、渠道平台 2、后台
  optional uint32 from = 7;
  // 联系情况
  optional string contact_status = 8;
  // 创建时间
  optional google.protobuf.Timestamp create_at = 9;
  // 更新时间
  optional google.protobuf.Timestamp update_at = 10;
}
