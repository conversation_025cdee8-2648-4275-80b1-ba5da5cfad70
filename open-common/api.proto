syntax = "proto3";

package api.v1;

import "google/protobuf/timestamp.proto";

option go_package = "open-proto/open-common/api/extern;extern";

// 获取商户api密钥
message SecretRequest{
  uint64 merchant_id = 1;
}

message SecretReply{
  string secret =1;
}

// 创建商户api密钥
message SecretCreateRequest{
  uint64 merchant_id = 1;
}

message ChannelGameLinksRequest {
  // 渠道域名，通过域名来唯一表示一个渠道链接
  string domain = 1;
  // 商户id
  optional uint64 merchant_id = 2;
}

message ChannelGameLinksReply {
  // 游戏链接列表
  repeated ChannelGameLink links = 1;
}

message ChannelGameLink {
  string app_id = 1;
  string link = 2;
}

// 游戏详情请求
message ChannelGameDetailRequest {
  // 渠道域名
  string domain = 1;
  // 游戏id，app_id
  string id = 2;
  // 商户id
  optional uint64 merchant_id = 3;
}

message ChannelGameDetail {
  // 游戏名称
  string name = 1;
  // 游戏分类
  uint32 type =2;
  // 游戏玩法
  string how_to_play = 3;
  // 游戏简介
  string description = 4;
  // 游戏图标
  GameMaterial icon = 5;
  // 游戏大图标
  GameMaterial big_icon = 6;
  // 游戏banner图
  GameMaterial banner = 7;
  // 游戏闪频图
  GameMaterial flash = 8;
  // 游戏链接
  string link = 9;
}

message GameMaterial {
  // 素材uri
  string uri =1;
  // 素材大小
  Size size = 2;
}

message Size {
  // 宽度，单位：px
  uint32 width = 1;
  // 高度，单位：px
  uint32 height = 2;
}

message ChannelGameDetailReply {
  // 游戏id
  string id = 1;
  // 游戏详情
  ChannelGameDetail detail = 2;
}