syntax = "proto3";

package merchantchannelinspect.v1;

import "validate/validate.proto";
import "google/protobuf/timestamp.proto";
import "google/protobuf/field_mask.proto";

option go_package = "open-proto/open-common/api/extern;extern";

message MerchantChannelInspectInfo {
  // 唯一id
  optional uint64 id = 1;
  // 商户id
  optional uint64 merchant_id = 2;
  // 渠道链接id
  optional uint64 merchant_channel_id = 3;
  // 审核类型，0 未知 1 新增渠道 2 渠道下架 3 恢复上架 4 违规下架 5 违规申诉 6 上架
  optional uint32 type = 4;
  // 审核状态，0 未知 1 审核中 2 通过 3 不通过 4 撤回
  optional uint32 state = 5;
  // 域名
  optional string domain = 6;
  // 流量来源，0 未知 1 Mintegral 2 Applovin 3 Google 4 Meta 5 TikTok 6 Snapchat 7 现有用户 8 其他广告平台
  repeated uint32 traffic_source=7;
  // 行业类型
  optional uint32 industry_type = 8;
  // 用户分布，为cldr国家/地区编码列表
  repeated string user_area = 9;
  // 用户量级（DAU），0 未知 1 [0, 10_000) 2 [10_000, 50_000) 3 [50_000,200_000 4 [200_000, 500_000) 5 [500_000, 1_000_000) 6 [1_000_000, 2_000_000) 7 [2_000_000, +∞)
  optional uint32 user_scale=10;
  // 媒体类型，0 未知 1 网站 2 google商店 3 苹果商店 4 其他应用市场
  optional uint32 media_type = 11;
  // 产品链接
  optional string product_link = 12;
  // 是否合作过h5游戏，false：否，true：是
  optional bool use_to_cooperate_with_h5 = 13;
  // 用户入口示意图列表
  repeated string user_entry_pics = 14;
  // 模板id
  optional uint32 template = 15;
  // 策略类型，0 未知 1 纯净版 2 普通版 3 增强版
  optional uint32 strategy = 16;
  // 提交来源，1 商户 2 后台
  optional uint32 from = 17;
  // 创建时间
  optional google.protobuf.Timestamp create_time = 18;
  // 更新时间
  optional google.protobuf.Timestamp update_time = 19;
  // 审核人
  optional string confirm_name = 20;
  // 审核时间
  optional google.protobuf.Timestamp confirm_at = 21;
  // 提交人
  optional string commit_name = 22;
  // 提交时间
  optional google.protobuf.Timestamp commit_at = 23;
  // 不合规的参数名
	repeated string illegal_list = 24;
	// 合规的参数名
	repeated string compliance_list = 25;
	// 审核反馈
	optional string feedback = 26;
}

message MerchantChannelInspectCreateRequest {
  // 审核信息
  MerchantChannelInspectInfo info = 1;
  // 操作人id(前端不传)
  optional uint64 uid = 2;
  // 提交来源，1 商户 2 后台
  optional uint32 from = 3;
}

message MerchantChannelInspectCreateReply {}

message MerchantChannelInspectUpdateRequest {
  // 审核信息
  MerchantChannelInspectInfo info = 1;
  // 更新了哪些字段
  google.protobuf.FieldMask update_fields = 2;
}

message MerchantChannelInspectUpdateReply {}

message MerchantChannelInspectDetailRequest {
  // 审核id
  uint64 id = 1;
}

message MerchantChannelInspectDetailReply {
  // 审核信息
  MerchantChannelInspectInfo data = 1;
}


//筛选条件
message MerchantChannelInspectFilter {
  // 商户名称（名称、英文名称、全称）
  optional string merchant_name = 1;
  // 审核类型，0 未知 1 新增渠道 2 渠道下架 3 恢复上架 4 违规下架 5 违规申诉 6 上架
  optional uint32 type = 2;
  // 审核状态，0 未知 1 审核中 2 通过 3 不通过 4 撤回
  optional uint32 state = 3;
  // 提交来源，1 商户 2 后台
  optional uint32 from = 4;
}

message MerchantChannelInspectSearchRequest {
  // 筛选条件
  MerchantChannelInspectFilter filter = 1;
  //页码
  int32 page = 2;
  //每页条数
  int32 page_size = 3;
}

message MerchantChannelInspectSearchReply {
  // 页码
  int32 page = 1;
  // 每页条数
  int32 page_size = 2;
  // 总条数
  int32 total = 3;
  // 审核信息列表
  repeated MerchantChannelInspectInfo data = 4;
}

message MerchantChannelInspectConfirmRequest{
  // uid
  uint64 uid = 6;
  // 审核id
  uint64 id = 1;
  // 不合规的参数名
  repeated string illegal_list = 2;
  // 合规的参数名
  repeated string compliance_list = 3;
  // 审核反馈
  string feedback = 4;
  // 是否通过: true: 通过; false:不通过
  bool is_pass = 5;
  // 更新时间
  optional google.protobuf.Timestamp update_time = 7;
}

// 撤回审核请求
message MerchantChannelInspectWithdrawRequest{
  // uid 操作人id(前端不传)
  uint64 uid = 6;
  // 审核id
  uint64 id = 1;
}
