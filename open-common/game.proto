syntax = "proto3";

package game.v1;

import "validate/validate.proto";
import "google/protobuf/timestamp.proto";

option go_package = "open-proto/open-common/api/extern;extern";

// 游戏所有信息
message GameAll {
  GameInfo game = 1;
  GameVersionInfo game_version = 2;
  GameInspectInfo game_inspect = 3;
}

message GameOnlyVersion{
  // 游戏版本唯一ID
  uint64 id = 1 ;
  // 游戏版本字符串标识
  string version = 2;
  // 版本状态（0.未知;1.未发布;2.审核中;3.审核未通过，提交审核后，审核被打回的状态;4.撤回审核;5.审核通过）
  optional uint32 state = 3;
  // 发布状态（0.未知;1.已发布;2.已下架）
  optional uint32 publish_state = 4;
  // 发布状态的审核状态(0.未知;1.审核中;2.撤回审核;3.审核未通过;4.审核通过)
  optional uint32 publish_inspect_state = 5;
  // 创建时间
  optional .google.protobuf.Timestamp create_time = 8;
}

// 游戏
message GameInfo {
  // 游戏的唯一标识
  uint64 id = 1 ;
  // 商户ID
  uint64 merchant_id = 2 ;
  // 游戏的应用ID，全局唯一
  string app_id = 3;
  // 是否禁用：1 启用; 2 禁用
  uint32 enable = 4;
  // 创建时间
  optional .google.protobuf.Timestamp create_at = 5;
}

// 游戏版本
message GameVersionInfo {
  // 游戏版本的唯一标识
  uint64 id = 1;
  // 商户ID
  uint64 merchant_id = 2;
  // 游戏的唯一标识
  uint64 game_id = 3;
  // 游戏版本字符串标识
  string version = 4;
  // 游戏名称
  optional string name = 5 [(validate.rules).string.max_len = 64];
  // 游戏英文名称
  optional string name_en = 6[(validate.rules).string.max_len = 64];
  // 游戏类型
  optional uint32 type = 7;
  // 屏幕类型 0.未知; 1.竖屏; 2.横屏
  optional uint32 landscape = 8;
  // 标签
  repeated uint32 tag = 9;
  // 游戏简介
  optional string description = 10[(validate.rules).string.max_len = 1000];
  // 游戏图标
  optional string icon = 11;
  // 游戏大图标
  optional string big_icon = 12;
  // Banner宣传图
  optional string banner = 13;
  // 闪屏图
  optional string flash = 14;
  // 多语言
  repeated uint32 i18n = 15;
  // 游戏引擎
  optional uint32 engine = 16;
  // 游戏引擎版本
  optional uint32 engine_version = 17;
  // 游戏引擎自定义
  optional string engine_other = 18 [(validate.rules).string.max_len = 32];
  // 游戏包
  optional string package = 19;
  // 游戏包名
  optional string package_name = 20;
  // 游戏包大小
  optional string package_size = 21;
  // 游戏接入检测表单 SDK检测(0.未知;1、已接入; 2、未接入)
  optional uint32 check_sdk  = 22;
  // 插屏广告(0.未知;1、已接入; 2、未接入)
  optional uint32 check_interstitial_ad  = 23;
  // 激励视频(0.未知;1、已接入; 2、未接入)
  optional uint32 check_incentive_video  = 24;
  // 加载时长 Fast 3G 最低标准 X秒
  optional uint32 check_loading_fast_3g  = 25;
  // 加载时长 Slow 3G 最低标准 X秒
  optional uint32 check_loading_slow_3g  = 26;
  // 版本审核进度(0.未知;1.基础信息;2.游戏资料;3.接入SDK;4.申请审核;5.上架入库)
  optional uint32 progress = 27;
  // 版本当前审核进度(0.未知;1.基础信息;2.游戏资料;3.接入SDK;4.申请审核;5.上架入库)
  optional uint32 progress_view = 28;
  // 版本状态（0.未知;1.未发布;2.审核中;3.审核未通过，提交审核后，审核被打回的状态;4.撤回审核;5.审核通过）
  optional uint32 state = 29;
  // 是否是游戏的活跃版本(0 未知;1.活跃版本；2.非活跃版本)
  optional uint32 active = 30;
  // 更新时间
  optional .google.protobuf.Timestamp update_at = 31;
  // 发布状态（0.未知;1.已发布;2.已下架）
  optional uint32 publish_state = 32;
  // 游戏的应用ID，全局唯一
  string app_id = 33;
  // 发布状态的审核状态(0.未知;1.审核中;2.撤回审核;3.审核未通过;4.审核通过)
  optional uint32 publish_inspect_state = 34;
    // 游戏包类型 1.正式包 2.测试包
  optional uint32 game_package_type = 35;
}


// 游戏接入检测
message AccessDetection{}


// 游戏审核
message GameInspectInfo {
  // 游戏审核的唯一标识
  uint64 id = 1;
  // 审核类型(0.未知;1.新游接入;2.版本更新;3.游戏下架;4.恢复上架)
  uint32 type =2;
  // 商户ID
  uint64 merchant_id = 3;
  // 游戏的唯一标识
  uint64 game_id = 4;
  // 游戏版本ID
  uint64 game_version_id = 5;
  // 提交人唯一 id
  uint64 creator_id = 6;
  // 提交人类型(0.未知;1.开发者;2.运营经理;3.数据迁移)
  uint32 creator_type = 7;
  // 提交时间
  optional .google.protobuf.Timestamp create_at = 8;
  // 审核人唯一id
  uint64 operator_id  = 9;
  // 审核时间
  optional .google.protobuf.Timestamp inspect_at = 10;
  // 审核状态(0.未知;1.审核中;2.撤回审核;3.审核未通过;4.审核通过)
  uint32 state = 11;
  // 审核反馈
  string feedback = 13;

  // 游戏的应用ID，全局唯一
  optional string app_id = 21;
  // 游戏名称
  optional string name = 14 [(validate.rules).string.max_len = 64];
  // 游戏英文名称
  optional string name_en = 15[(validate.rules).string.max_len = 64];
  // 游戏图标
  optional string icon = 16;
  // 游戏类型
  optional uint32 game_type = 17;
  // 提交人
  optional string creator_name = 18;
  // 审核人
  optional string operator_name = 19;
  // 游戏版本字符串标识
  optional string version = 20;
  // 不合规的参数名
  repeated string illegal_list = 22;
  // 合规的参数名
  repeated string compliance_list = 23;

  // 屏幕类型 0.未知; 1.竖屏; 2.横屏
  optional uint32 landscape = 24;
  // 标签
  repeated uint32 tag = 25;
  // 游戏简介
  optional string description = 26[(validate.rules).string.max_len = 1000];
  // 游戏大图标
  optional string big_icon = 27;
  // Banner宣传图
  optional string banner = 28;
  // 闪屏图
  optional string flash = 29;
  // 多语言
  repeated uint32 i18n = 30;
  // 游戏引擎
  optional uint32 engine = 31;
  // 游戏引擎版本
  optional uint32 engine_version = 32;
  // 游戏引擎自定义
  optional string engine_other = 33 [(validate.rules).string.max_len = 32];
  // 游戏包
  optional string package = 34;
  // 游戏包名
  optional string package_name = 35;
  // 游戏包大小
  optional string package_size = 36;
  // 游戏接入检测表单 SDK检测(0.未知;1、已接入; 2、未接入)
  optional uint32 check_sdk  = 37;
  // 插屏广告(0.未知;1、已接入; 2、未接入)
  optional uint32 check_interstitial_ad  = 38;
  // 激励视频(0.未知;1、已接入; 2、未接入)
  optional uint32 check_incentive_video  = 39;
  // 加载时长 Fast 3G 最低标准 X秒
  optional uint32 check_loading_fast_3g  = 40;
  // 加载时长 Slow 3G 最低标准 X秒
  optional uint32 check_loading_slow_3g  = 41;
  // 版本审核进度(0.未知;1.基础信息;2.游戏资料;3.接入SDK;4.申请审核;5.上架入库)
  optional uint32 progress = 42;
  // 版本当前审核进度(0.未知;1.基础信息;2.游戏资料;3.接入SDK;4.申请审核;5.上架入库)
  optional uint32 progress_view = 43;
  // 游戏包类型 1.正式包 2.测试包
  optional uint32 game_package_type = 44;
}


message GameVersionBaseInfo {
  // 游戏版本的唯一标识
  uint64 id = 1;
  // 商户ID
  uint64 merchant_id = 2;
  // 游戏的唯一标识
  uint64 game_id = 3;
  // 游戏的应用ID，全局唯一
  string app_id = 5;
  // 游戏英文名称
  optional string name_en = 6;
  // 游戏图标
  optional string icon = 7;
  // 游戏版本字符串标识
  string version = 8;
  // 版本状态（0.未知;1.未发布;2.审核中;3.审核未通过，提交审核后，审核被打回的状态;4.撤回审核;5.审核通过/已发布）
  optional uint32 state = 9;
  // 版本审核进度(0.未知;1.基础信息;2.游戏资料;3.接入SDK;4.申请审核;5.上架入库)
  optional uint32 progress = 10;
  // 发布状态（0.未知;1.已发布;2.已下架）
  optional uint32 publish_state = 11;
  // 发布状态的审核状态(0.未知;1.审核中;2.撤回审核;3.审核未通过;4.审核通过)
  optional uint32 publish_inspect_state = 15;
  // 创建时间
  optional .google.protobuf.Timestamp create_at = 12;
  // 更新时间
  optional .google.protobuf.Timestamp update_at = 13;
  // 是否有未发布的
   optional bool has_unpublish = 14;
}

message AdminGameInfo {
  // 游戏包类型 1.正式包 2.测试包
  optional uint32 game_package_type = 20;
  bool has_unpublish = 19;
  // 是否已经推送
  bool has_pushed = 18;
  // 游戏的版本id
  uint64 game_version_id = 17;
  // 游戏的id
  uint64 game_id = 16;
  // APP ID
  string app_id = 1;
  // 游戏icon
  optional string icon = 2;
  // 游戏名
  optional string name = 3 [(validate.rules).string.max_len = 64]; 
  // 游戏英文名
  optional string name_en = 4[(validate.rules).string.max_len = 64];
  // 游戏类型
  optional uint32 game_type = 5;
  // 屏幕类型 0.未知; 1.竖屏; 2.横屏
  optional uint32 landscape = 6;
  // 多语言
  repeated uint32 i18n = 7;
  // 创建时间
  optional .google.protobuf.Timestamp game_create_time = 8;
  // 当前版本
  string version = 9;
  // 版本时间
  optional .google.protobuf.Timestamp inspected_time = 10;
  // 所属商户
  string merchant_name = 11;
  // 来源
  uint32 from = 12;
  // 进度(0.未知;1.基础信息;2.游戏资料;3.接入SDK;4.申请审核;5.上架入库)
  optional uint32 progress = 13; 
  // 状态
  uint32 enable = 14;
  // 发布状态
  uint32 state = 15;
}


message ListGamesRequest {
  int32 page_size = 3;
  int32 page = 2;
  ListGameFilter filter = 1;
  string lang = 4;
}

message ListGameFilter {
  // 类型
  optional uint32 type = 1;
  // 标签
  repeated uint32 tags = 2;
  // 是否有game_url
  optional uint32 has_game_url = 3;
  // 排序类型 1 .热门 2.最新 3.高收益
  optional uint32 sort_type = 4;
}

message ListGamesReply {
  repeated GameData data = 1;
  int32 total = 2;
}

message GameData {
  uint64 game_id = 1;
  optional string banner = 2;
  repeated uint32 tags = 3;
  optional string description = 4;
  optional string name = 5;
  optional string icon = 6;
  optional int64  like_count = 7;
  optional int64  play_count = 8;
  optional int64  yesterday_play_count = 9;
  repeated string country = 10;
  optional string how_to_play = 11;
  optional string game_url = 12;
  optional double  rating = 13;
  optional uint32 game_type = 14;
  optional uint32 landscape = 15;
  optional string flash = 16;
  optional double package_size = 17;
  .google.protobuf.Timestamp update_at = 18;
  optional uint32 check_loading_fast_3g = 19;
  optional uint32 check_loading_slow_3g = 20;
  repeated string matching = 21;
  optional string app_id = 22;
  repeated uint32 languages = 23;
}