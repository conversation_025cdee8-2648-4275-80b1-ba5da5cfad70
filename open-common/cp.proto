syntax = "proto3";

package user.v1;

import "validate/validate.proto";
import "google/protobuf/timestamp.proto";
import "google/protobuf/field_mask.proto";

option go_package = "open-proto/open-common/api/extern;extern";

// 开发者基础信息
message UserInfo {
	// 类型（1.邮箱; 2.短信; 3.密码）
	optional uint32 type = 1;
	// 国家区号
	optional string national_code = 3;
	// 手机号,这里最小长度为5,考虑到后续要支持国外手机号。
	optional string mobile = 4[(validate.rules).string.min_len = 5];
	// 邮箱
	optional string email = 5[(validate.rules).string.email = true];
	// 是否启用(false 禁用; true 启用)
	optional bool enable = 6;
	// 是否初始化 (true.已初始化;false.未初始化)
	optional bool is_init = 7;
	// 密码是否需要重置（true: 需要; false: 不需要）
	optional bool password_reset = 8;
	// cp-id
	optional uint64 cp_id = 9;
	// 密码
	optional  string password = 10;
	// uid
	optional uint64 uid = 11;
	// 最近一次登入时间
	optional google.protobuf.Timestamp latest_login_ts = 12;
	// 最近一次后台密码修改时间
	optional google.protobuf.Timestamp latest_reset_pw_ts = 13;
  // su关联ID
	optional uint64 relation_id = 14;
}

// 开发者厂商信息
message CpInfo {
	// CP-ID
	optional uint64 id = 1;
	// 超级管理员（CP）id
	optional uint64 su = 2;
	// 名称
	optional string name = 3;
	// 英文名称
	optional string en_name =4;
	// 全称
	optional string full_name = 5;
	// 注册地区行政编码
	repeated string reg_region_code = 6;
	// 注册地址
	optional string reg_address = 7;
	// 统一社会信用代码
	optional string credit_code = 8;
	// 营业开始时间
	optional google.protobuf.Timestamp trade_begin_time = 9;
	// 营业结束时间
	optional google.protobuf.Timestamp trade_end_time = 10;
	optional bool is_trade_endless = 34;
	// 营业执照扫描件url
	optional string license_url = 11;
	// 邮箱
	optional string email = 12[(validate.rules).string.email = true];
	// 联系人
	optional string contact_name = 33;
	// 联系人，国家区号
	optional string contact_national_code = 13;
	// 联系人手机号
	optional string contact_mobile = 14[(validate.rules).string.min_len = 5];
	// 0 显示状态 未知， 1 未认证，2 审核中，3 已认证
	optional uint32 state = 15;
	// 客户经理id
	repeated uint64 manager_id = 16;
	// 客户经理(企业邮箱格式)
	repeated string manager_name = 17;
	// 游戏数量
	optional uint32 game_count = 18;
	// 组织类型 1企业/2个人
	optional uint32 org_type = 19;
	//状态: true 启用、false 禁用
	optional bool  enable = 20;
	// QQ
	optional string qq = 21;
	// WeiXin
	optional string wechat = 22;

	// 认证状态: 1.未认证；2已认证
	optional uint32  real_state = 23;
	//cp类型（1 国内；2海外）
	optional uint32 cp_type = 24;
	//注册时间
	optional google.protobuf.Timestamp sign_in_at = 25;
	//审核人(企业邮箱格式)
	optional string confirm_name = 26;
	//审核时间
	optional google.protobuf.Timestamp confirm_at = 27;
	//认证时间（审核通过时间）
	optional google.protobuf.Timestamp pass_at = 32;
	//来源（1.注册 2.后台 3.迁移）
	optional uint32 from = 28;
	//账号状态: true 启用、false 禁用
	optional bool  user_enable = 29;
	//CP是否存在审核数据
	bool exist_inspect = 30;
	//用户注册邮箱(后台使用)
	string user_email = 31;
	// su关联ID
	optional uint64 relation_id = 35;
}


// 开发者审核信息
message InspectInfo {
	//ID(创建时不传)
	optional uint64 id = 1;
	//ID(厂商ID)
	optional uint64 cp_id = 2;
	// 厂商名称
	optional string name = 3;
	// 英文名称
	optional string en_name =4;
	// 全称
	optional string full_name = 5;
	// 注册地区行政编码
	repeated string reg_region_code = 6;
	// 注册地址
	optional string reg_address = 7;
	// 统一社会信用代码
	optional string credit_code = 8;
	// 营业开始时间
	optional google.protobuf.Timestamp trade_begin_time = 9;
	// 营业结束时间
	optional google.protobuf.Timestamp trade_end_time = 10;
	// 营业结束时间是否长期
	optional bool is_trade_endless = 38;
	// 营业执照扫描件url
	optional string license_url = 11;
	// 邮箱
	optional string email = 12[(validate.rules).string.email = true];
	// 联系人
	optional string contact_name = 36;
	// 联系人，国家区号
	optional string contact_national_code = 13;
	// 联系人手机号
	optional string contact_mobile = 14;
	// QQ
	optional string qq = 16;
	// WeiXin
	optional string wechat = 17;
	// 认证状态，0:未知 ,1:待提交, 2:待审核, 3:审核失败, 4:结束,
	optional uint32 auth_state = 18;

	// 客户经理id
	repeated uint64 manager_id = 19;
	// 组织类型 1企业/2个人
	optional uint32 org_type = 21;
	// 认证类型: 1认证资质、2修改信息
	optional uint32 auth_type = 22;
	//注册时间
	optional google.protobuf.Timestamp sign_in_at = 23;
	//提交人(企业邮箱格式)
	optional string commit_name = 24;
	//提交时间
	optional google.protobuf.Timestamp commit_at = 25;
	//审核人(企业邮箱格式)
	optional string confirm_name = 26;
	//审核时间
	optional google.protobuf.Timestamp confirm_at = 27;
	//来源（1.开发者 2.后台 3.迁移）
	optional uint32 from = 28;

	// 不合规的参数名
	repeated string illegal_list = 29;
	// 合规的参数名
	repeated string compliance_list = 30;
	//审核反馈
	optional string feedback = 31;



	// 超级管理员uid（创建时不传）
	optional uint64 su = 32;
	//cp类型（1 国内；2海外）
	optional uint32 cp_type = 33;


	// 修改的参数key
	repeated string modified_parameter = 34;
	// 更新了哪些字段
	google.protobuf.FieldMask update_fields = 35;
	// 更新时间
	optional google.protobuf.Timestamp update_time = 37;

	// su关联ID
	optional uint64 relation_id = 39;
}

// 开发者厂商名称
message CpName {
	// CP-ID
	optional uint64 id = 1;
	// 名称
	optional string name = 2;
}