syntax = "proto3";

package carousel.v1;

import "google/protobuf/timestamp.proto";

option go_package = "open-proto/open-common/api/extern;extern";

message Carousel {
  // 唯一id
  optional uint64 id = 1;
  // 关联的游戏id
  optional uint64 game_id = 2;
  // 轮播图名称
  optional string name = 3;
  // 排序
  optional uint32 order = 4;
  // 状态，1 启用、 2 停用
  optional uint32 enable = 5;
  // 创建时间
  optional google.protobuf.Timestamp create_at = 6;
  // 更新时间
  optional google.protobuf.Timestamp update_at = 7;
  // 轮播素材
  optional string banner = 8;
  // 删除时间
  optional google.protobuf.Timestamp delete_at = 9;
}
