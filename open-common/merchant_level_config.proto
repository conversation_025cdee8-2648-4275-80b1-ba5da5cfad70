syntax = "proto3";

package level.v1;

import "validate/validate.proto";
import "google/protobuf/timestamp.proto";
import "google/protobuf/field_mask.proto";

option go_package = "open-proto/open-common/api/extern;extern";

message MerchantLevelConfigInfo {
  // 唯一id
  optional int64 id = 1;
  // 等级名称
  optional string name = 2;
  // 认证状态，0 未知，1 认证中，2 已认证
  optional uint32 auth_state = 3;
  // 渠道链接数量上限， -1 表示不限
  optional int32 channel_count = 4;
  // 前端显示
  optional string display = 5;
  // 备注信息
  optional string desc = 6;
  // 创建时间
  optional google.protobuf.Timestamp create_time = 7;
  // 更新时间
  optional google.protobuf.Timestamp update_time = 8;
}

message MerchantLevelConfigFilter {
  // 等级名称
  optional string name = 1;
}

message MerchantLevelConfigSearchRequest {
  MerchantLevelConfigFilter filter = 1;
  // 页码
  uint32 page = 2;
  // 每页条数
  uint32 page_size = 3;
}

message MerchantLevelConfigSearchReply {
  repeated MerchantLevelConfigInfo data = 1;
  // 页码
  uint32 page = 2;
  // 每页条数
  uint32 page_size = 3;
  // 总条数
  uint32 total = 4;
}

message MerchantLevelConfigCreateRequest {
  // 前置条件：认证状态，0 未知，1 认证中，2 已认证
  uint32 auth_state = 1;
  // 等级名称
  string name = 2;
  // 渠道链接数量上限， -1 表示不限
  int32 channel_count = 3;
  // 备注信息
  string desc = 4;
}

message MerchantLevelConfigCreateReply {
  MerchantLevelConfigInfo data = 1;
}

message MerchantLevelConfigDetailRequest {
  int64 id = 1;
}

message MerchantLevelConfigDetailReply {
  MerchantLevelConfigInfo data = 1;
}