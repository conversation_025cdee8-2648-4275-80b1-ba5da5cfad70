syntax = "proto3";

package sitedemand.v1;

import "google/protobuf/timestamp.proto";

option go_package = "open-proto/open-common/api/extern;extern";

message SiteDemand {
  // 网站需求id
  optional uint64 demand_id = 1;
  // 发行商
  optional string publisher = 2;
  // 发行商id
  optional uint64 publisher_id = 13;
  // 模板枚举:  1、minigame2.0模板 2、微游中心模板
  optional uint32 template = 3;
  // 网站域名
  optional string domain = 4;
  // 其他需求
  optional string other_demand = 5;
  // 联系人
  optional string contact_person = 6;
  // 国家区号
  optional string nation_code = 15;
  // 联系手机号
  optional string mobile = 7;
  // 完整手机号，区号+手机号
  optional string full_phone_number = 14;
  // 联系状态 1、待联系 2、已联系
  optional uint32 contact_state = 8;
  // 联系情况
  optional string contact_status = 9;
  // 联系邮箱
  optional string email = 12;
  // 更新时间
  optional google.protobuf.Timestamp update_at = 10;
  // 创建时间
  optional google.protobuf.Timestamp create_at = 11;
}