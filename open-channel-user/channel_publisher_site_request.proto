syntax = "proto3";

package channeluser.v1;

import "validate/validate.proto";
import "google/protobuf/timestamp.proto";
import "google/protobuf/empty.proto";
import "google/protobuf/field_mask.proto";

option go_package = "open-proto/open-channel-user/api/extern;extern";

// 网站

service ChannelPublisherSiteRequest {
  // 提交网站需求
  rpc Submit(SubmitRequest) returns (SubmitReply) {}
  // 更新网站需求
  rpc Update(UpdateRequest) returns (UpdateReply) {}
  // 列出最新的网站需求，展示最新6条表单数据
  rpc Latests(LatestsRequest) returns (LatestsReply) {}
  // 网站需求列表
  rpc Lists(ListsRequest) returns (ListsReply) {}
  // 删除网站需求
  rpc Delete(DeleteRequest) returns (DeleteReply) {}
  // 获取网站需求详情
  rpc Get(GetRequest) returns (GetReply) {}
}

message SubmitRequest {
  // 用户id
  uint64 uid = 1;
  // 模板枚举:  1、minigame2.0模板 2、微游中心模板
  uint32 template = 2;
  // 网站域名
  string domain = 3;
  // 其他需求
  string other_demand = 4;
  // 联系人
  string contact_person = 5;
  // 联系手机号
  optional string mobile = 6;
  // 联系邮箱
  optional string email = 7;
  // 国家区号
  optional string national_code = 8;
}

message SubmitReply {}

message UpdateRequest {
  // 用户id
  uint64 uid = 1;
  // 网站需求的id
  uint64 demand_id = 2;
  // 模板枚举:  1、minigame2.0模板 2、微游中心模板
  uint32 template = 3;
  // 网站域名
  string domain = 4;
  // 其他需求
  string other_demand = 5;
  // 联系人
  string contact_person = 6;
  // 联系手机号
  optional string mobile = 7;
  // 联系邮箱
  optional string email = 8;
  // 国家区号
  optional string national_code = 9;
  // 更新的字段列表
  google.protobuf.FieldMask update_fields = 10;
}

message UpdateReply {}

message LatestsRequest {
  // 用户id
  uint64 uid = 1;
}

message SiteDemandSummary {
  // 网站需求id
  uint64 demand_id = 4;
  // 网站域名
  string domain = 1;
  // 联系状态
  uint32 contact_state = 2;
  // 更新时间
  .google.protobuf.Timestamp update_at = 3;
}

message LatestsReply {
  // 最新6条的网站需求
  repeated SiteDemandSummary data = 1;
}

message ListsRequest {
  // 用户id
  uint64 uid = 1;
  // 页码
  int32 page = 2;
  // 每页条数
  int32 page_size = 3;
}

message SiteDemandItem {
  // 网站需求id
  uint64 demand_id = 5;
  // 网站域名
  string domain = 1;
  // 联系状态 1、待联系 2、已联系
  uint32 contact_state = 2;
  // 更新时间
  .google.protobuf.Timestamp update_at = 3;
  // 创建时间
  .google.protobuf.Timestamp create_at = 4;
}

message ListsReply {
  // 网站需求列表
  repeated SiteDemandItem data = 1;
  // 页码
  int32 page = 2;
  // 每页条数
  int32 page_size = 3;
  // 总条数
  int32 total = 4;
}

message DeleteRequest {
  // 用户id
  uint64 uid = 1;
  // 网站需求id
  uint64 demand_id = 2;
}

message DeleteReply {}

message GetRequest {
  // 用户id
  uint64 uid = 1;
  // 网站需求id
  uint64 demand_id = 2;
}

message GetReply {
  // 网站需求id
  uint64 demand_id = 7;
  // 模板枚举:  1、minigame2.0模板 2、微游中心模板
  uint32 template = 1;
  // 网站域名
  string domain = 2;
  // 其他需求
  string other_demand = 3;
  // 联系人
  string contact_person = 4;
  // 国家区号
  optional string national_code = 8;
  // 联系手机号
  optional string mobile = 5;
  // 联系邮箱
  optional string email = 6;
  // 联系状态
  uint32 contact_state = 9;
}
