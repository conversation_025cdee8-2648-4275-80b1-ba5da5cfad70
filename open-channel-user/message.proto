syntax = "proto3";

package publishermessage.v1;

import "validate/validate.proto";
import "google/protobuf/empty.proto";
import "google/protobuf/timestamp.proto";
import "open-proto/open-common/message.proto";



option go_package = "open-proto/open-channel-user/api/extern;extern";

// The publisher message service definition.
service PublisherMessage {
  // 消息列表包含已读 分页
  rpc Search(PublisherMessageSearchRequest) returns (.common.message.v1.MessageSearchReply) {}
  // 处理消息
  rpc Operate(PublisherMessageOperateRequest) returns (google.protobuf.Empty) {}
  // 消息数量
  rpc Count(PublisherMessageCountRequest) returns (.common.message.v1.MessageCountReply) {}
  // 获取消息
  rpc Info(PublisherMessageInfoRequest) returns (.common.message.v1.MessageInfoReply) {}
}

message PublisherMessageInfoRequest {
  uint64 id = 1;
  uint64 uid = 2;
}

message PublisherMessageInfoReply {
  MessageInfo message = 1;
}

message MessageInfo {
  // 标题
  string title = 1;
  // 消息类型 1公告 2通知 4报表 5工单 3活动
  uint32 msg_type = 2;
  // 消息内容
  string content = 3;
  // 封面
  optional string cover = 4;
  // 是否开启轮播推荐 true 是
  optional bool rotate  = 5;
  // id
  uint64 id = 6;
  // 消息状态 是否已读 1 未读 2 已读
  uint32 state = 7;
  // 发布时间
  .google.protobuf.Timestamp publish_time = 8;
  // 内容的描述
  string desc = 9;
  // 消息的数据，用于前端渲染
  MessageData data = 10;
}

message InspectItem {
  // 字段名
  string field = 1;
  // 值
  string value = 2;
  // 是否通过
  bool is_pass = 3;
}

message MessageData {
  // 消息通知的类型 1.cp审核 2.游戏审核
  uint32 notice_msg_type = 1;
  // 审核项目的列表
  repeated InspectItem inspect_items = 2;
  // 反馈意见
  string feedback = 3;
  // cp名
  optional string cp_name = 4;
  // 是否通过
  bool is_pass = 5;
  // 应用的id
  optional string app_id = 6;
  // 应用name
  optional string name = 7;
}


message PublisherMessageCountRequest {
  uint64 uid = 1;
}

message PublisherMessageCountReply {
  // 所有类别未读消息数量
  uint32 unread_count = 1;
  // 平台公告未读的数量
  uint32 announcement_unread_count = 2;
  // 消息通知未读的数量
  uint32 notice_unread_count = 3;
  // 数据报表未读的消息
  uint32 report_unread_count = 4;
  // 工单未读的消息
  uint32 tickets_unread_count = 5;
  // 活动未读的消息
  uint32 event_unread_count = 6;
  // 平台公告全部消息的数量
  uint32 announcement_count = 7;
  // 消息通知全部消息的数量
  uint32 notice_count = 8;
  // 数据报表全部消息的数量
  uint32 report_count = 9;
  // 工单全部消息的数量
  uint32 tickets_count = 10;
  // 活动全部消息的数量
  uint32 event_count = 11;
}

message PublisherMessageListReply {
  repeated MessageInfo data = 1;
}

message PublisherMessageSearchReply{
  repeated MessageInfo data = 1;
  //页码
  int32 page = 2;
  //每页条数
  int32 page_size = 3;
  //总条数
  int32 total = 4;
}


message PublisherMessageSearchRequest {
  uint64 uid  = 1;
  //搜索条件
  .common.message.v1.MessageFilter filter = 4;
  //页码
  int32 page = 2;
  //每页条数
  int32 page_size = 3;
}

message PublisherMessageListRequest {
  uint64 uid  = 1;
  //搜索条件
  .common.message.v1.MessageFilter filter = 4;
}

message PublisherMessageOperateRequest {
  uint64 uid = 1;
  // 消息id
  repeated uint64 message_id = 2;
  // 1 已读 2 删除
  uint32 operate = 3;
}