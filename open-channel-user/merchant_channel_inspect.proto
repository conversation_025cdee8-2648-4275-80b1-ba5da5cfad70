syntax = "proto3";

package channeluser.v1;

import "validate/validate.proto";
import "open-proto/open-common/merchant_channel_inspect.proto";
import "google/protobuf/empty.proto";

option go_package = "open-proto/open-channel-user/api/extern;extern";

// 渠道链接审核管理
service MerchantChannelInspectService {
  // 保存审核草稿
  rpc Save(merchantchannelinspect.v1.MerchantChannelInspectCreateRequest)
      returns (google.protobuf.Empty) {}

  // 提交审核
  rpc Submit(merchantchannelinspect.v1.MerchantChannelInspectCreateRequest)
      returns (google.protobuf.Empty) {}

  // 获取渠道链接审核详情
  rpc Detail(merchantchannelinspect.v1.MerchantChannelInspectDetailRequest)
      returns (merchantchannelinspect.v1.MerchantChannelInspectDetailReply) {}

  // 审核撤回
  rpc Withdraw(merchantchannelinspect.v1.MerchantChannelInspectWithdrawRequest)
      returns (google.protobuf.Empty) {};
}