syntax = "proto3";

package channeluser.v1;

import "validate/validate.proto";
import "google/protobuf/timestamp.proto";
import "google/protobuf/empty.proto";
import "google/protobuf/field_mask.proto";

option go_package = "open-proto/open-channel-user/api/extern;extern";

// 用户
service User {
  // 登录/注册
  rpc LoginRelated (UserLoginRequest) returns (UserLoginRelatedReply) {
  }

  // 登出
  rpc LogoutRelated(UserLogoutRequest) returns (UserLogoutReply) {
  }

  // 信息
  rpc Info (UserInfoRequest) returns (UserInfoReply) {
  }

  // 上传文件
  rpc UploadFile(UserUploadFileRequest) returns (UserUploadFileReply) {
  }

  // 用户下载文件
  rpc DownloadFile(UserDownloadFileRequest) returns (UserDownloadFileReply) {
  }

  // 用户删除文件
  rpc DeleteFile(UserDeleteFileRequest) returns (UserDeleteFileReply) {
  }

  // 密码找回
  rpc RetrievePasswordRelated(UserRetrievePasswordRequest) returns (UserRetrievePasswordReply) {
  }

  // 强制修改密码（后台操作）
  rpc ResetPasswordRelated(UserResetPasswordRequest) returns (.google.protobuf.Empty) {
  }

  // 修改密码
  rpc ChangePasswordRelated(UserChangePasswordRequest) returns (UserChangePasswordReply) {
  }

  // 用户状态
  rpc LoginState (UserLoginStateRequest) returns (UserLoginStateReply) {
  }

  // 厂商注册
  rpc Register (RegisterRequest) returns (RegisterReply) {
  }

  // 用户提交认证
  rpc InspectSubmit (InspectSubmitRequest) returns (InspectSubmitReply) {
  }

  // 用户撤回认证
  rpc InspectWithdraw (InspectWithdrawRequest) returns (InspectWithdrawReply) {
  }
  rpc GetMerchantByUid(GetMerchantByUidRequest) returns (GetMerchantByUidReply) {
  }
  rpc GetMerchantInfo(GetMerchantInfoRequest) returns (GetMerchantInfoReply) {
  }
}

message GetMerchantByUidRequest{
  uint64 uid = 1;
}

message GetMerchantByUidReply{
  MerchantInfo merchant = 1;
}

message GetMerchantInfoRequest{
  uint64 merchant_id = 1;
}

message GetMerchantInfoReply{
  MerchantInfo merchant = 1;
}

// 用户登录请求
message UserLoginRequest {
  // 登入方式（1、密码 2、验证码）
  uint32 type = 1;
  // 邮箱
  optional string email = 2[(validate.rules).string.email = true];
  // 验证码
  string captcha = 3;
  // 账号
  optional string account = 4;
  // 密码
  optional string password = 5[(validate.rules).string.min_len = 5];
}

message CaptchaInfo{
  // 邮箱
  optional string email = 1[(validate.rules).string.email = true];
  // 验证码
  string captcha = 2;
}
message AccountInfo{
  // 账号
  optional string account = 1;
  // 密码
  optional string password = 2[(validate.rules).string.min_len = 5];
}

// 用户信息
// deprecated
message ChannelUserInfo {
  // 邮箱
  optional string email = 1[(validate.rules).string.email = true];
  // 是否启用(false 禁用; true 启用)
  optional bool enable = 2;
  // 是否初始化 (true.已初始化;false.未初始化)
  optional bool is_init = 3;
  // 密码是否需要重置（true: 需要; false: 不需要）
  optional bool password_reset = 4;
  // uid
  optional uint64 uid = 11;
  // 最近登入时间
  optional google.protobuf.Timestamp latest_login_at = 12;
  // 最近登出时间
  optional google.protobuf.Timestamp latest_logout_at = 13;
  // 最近重置密码时间
  optional google.protobuf.Timestamp latest_reset_password_at = 14;
  // su关联id
  optional uint64 relation_id = 15;
}

// 用户变更信息
// deprecated
message  ChannelUserChangeInfo {
  // uid
  uint64 uid = 1;
  // 邮箱
  optional string email = 2[(validate.rules).string.email = true];
  // 密码
  optional  string password = 3;
  // 是否启用(false 禁用; true 启用)
  optional bool enable = 4;
  // 所属的发行商id
  optional uint64 publisher_id = 5;
  // 更新了哪些字段
  google.protobuf.FieldMask update_fields = 6;
}

// 用户信息
message UserRelationInfo {
  // uid
  optional uint64 uid = 1;
  // 邮箱
  optional string email = 2[(validate.rules).string.email = true];
  // 国家区号
  optional string national_code = 3;
  // 手机号,这里最小长度为5,考虑到后续要支持国外手机号。
  optional string mobile = 4[(validate.rules).string.min_len = 5];
  // 是否启用(false 禁用; true 启用)
  optional bool enable = 5;
  // 是否初始化 (true.已初始化;false.未初始化)
  optional bool is_init = 6;
  // 密码是否需要重置（true: 需要; false: 不需要）
  optional bool password_reset = 7;
  // 所属的发行商id
  optional uint64 merchant_id = 8;
  // 最近登入时间
  optional google.protobuf.Timestamp latest_login_at = 9;
  // 最近登出时间
  optional google.protobuf.Timestamp latest_logout_at = 10;
  // 最近重置密码时间
  optional google.protobuf.Timestamp latest_reset_password_at = 11;
  // 是否屏蔽菜单，true 屏蔽 ，false 不屏蔽
  optional bool is_hide_menu = 12;
}

// 用户变更信息
message UserRelationChangeInfo {
  // 服务端微服务使用，前端不填。
  uint64 uid = 1;
  // 邮箱
  optional string email = 2[(validate.rules).string.email = true];
  // 国家区号
  optional string national_code = 3;
  // 手机号,这里最小长度为5,考虑到后续要支持国外手机号。
  optional string mobile = 4[(validate.rules).string.min_len = 5];
  // 密码
  optional  string password = 5;
  // 是否启用(false 禁用; true 启用)
  optional bool enable = 6;
  // 所属的发行商id
  optional uint64 merchant_id = 7;
  // 更新了哪些字段
  google.protobuf.FieldMask update_fields = 8;
}

message UserLoginReply {
  // 用户id
  uint64 id = 1;
  // 是否已经初始化
  uint64 merchant_id = 2;
}

message UserLoginRelatedReply {
  // 用户id
  uint64  uid = 1;
  // 商户id
  uint64 merchant_id = 2;
  // 是否注册
  bool is_sign_up = 6;
}

// 用户关联信息请求
message UserRelatedRequest {
  // uid
  uint64 uid = 1;
}
message UserRelatedReply {
  uint64 cp_user_id = 1;
}

// 用户登录请求
message UserLogoutRequest {
  // uid
  uint64 id = 1;
}
message UserLogoutReply {
}

// 用户状态信息请求
message UserInfoRequest {
  uint64 id = 1;
}
// 用户状态信息响应
message UserInfoReply {
  // 用户信息
  UserRelationInfo user = 1;
  // 商户信息
  MerchantInfo merchant = 2;
  // 商户认证信息
  MerchantInspectInfo inspect = 3;
  // 状态: 0 未知 1 未认证，2 审核中，3 已认证
  optional uint32 state = 4;
  // 商户OpenApi信息
  MerchantOpenApiInfo api = 5;
}

// 用户上传文件
message UserUploadFileRequest {
  // uid
  uint64 uid = 1;
  //资源名称 Ep: "license"
  string asset_name = 2;
  // 资源类型 (Ep: "png","jpg")
  string content_type = 3;
  //资源大小
  int64  object_size = 4;
  //数据
  bytes  data = 5;
  // 获取文件扩展名
  string  extension = 6;
}

message UserUploadFileReply {
  //图片访问路径
  string asset_url = 1;
}

// 用户下载文件
message UserDownloadFileRequest {
  // uid
  uint64 uid = 1;
  //资源名称 Ep: "license"
  string asset_name = 2;
  // 资源类型 (Ep: "png","jpg")
  string content_type = 3;
}
message UserDownloadFileReply {
  //数据
  bytes  data = 1;
}

// 用户删除文件
message UserDeleteFileRequest {
  // uid
  uint64 uid = 1;
  //资源名称 Ep: "license"
  string asset_name = 2;
  // 资源类型 (Ep: "png","jpg")
  string content_type = 3;
}

message UserDeleteFileReply {
}

// 密码找回请求
message UserRetrievePasswordRequest {
  // 邮箱
  optional string email = 1[(validate.rules).string.email = true];
  // 验证码
  string captcha = 2;
  // 密码
  optional string password = 3[(validate.rules).string.min_len = 5];
}
// 密码找回响应
message UserRetrievePasswordReply {
  // 用户id
  uint64 id = 1;
}

// 密码重置请求
message UserResetPasswordRequest {
  // 用户id
  optional uint64 uid = 1;
  // 密码
  optional string password = 2[(validate.rules).string.min_len = 5];
}

// 修改密码请求
message UserChangePasswordRequest {
  // 用户id
  optional uint64 uid = 1;
  // 原始密码
  optional string original_password = 2[(validate.rules).string.min_len = 5];
  // 密码
  optional string password = 3[(validate.rules).string.min_len = 5];
}
// 修改密码响应
message UserChangePasswordReply {
}

// 登出时间请求
message UserLoginStateRequest {
  // uid
  uint64 id = 1; // uid
}
// 账户当前状态请求
message UserLoginStateReply {
  // 上次登出时间
  optional google.protobuf.Timestamp logout_time = 1;
  // 用户状态
  optional uint32 state = 2;
}

// 商户认证请求
message InspectSubmitRequest {
  MerchantInspectInfo inspect = 1;
}

// 商户认证响应
message InspectSubmitReply {
  uint64 id = 1;
}

// 用户撤回认证请求
message InspectWithdrawRequest {
  uint64 id = 1;
}

// 用户撤回认证响应
message InspectWithdrawReply {
  MerchantInspectInfo inspect = 1;
}

// 商户注册请求
message RegisterRequest {
  // 发行商名称
  optional string name = 1;
  // 密码
  optional string password = 2;
  // UID
  optional uint64 uid = 3;
}

// 商户注册响应
message RegisterReply{
  uint64 id = 1;
}

// 开发者审核信息
// deprecated
message PublisherInspectInfo {
  // ID(创建时不传)
  optional uint64 id = 1;
  // ID
  optional uint64 publisher_id = 2;
  // 名称
  optional string name = 3;
  // 英文名称
  optional string en_name = 4;
  // 全称
  optional string full_name = 5;
  // 注册地区行政编码
  repeated string reg_region_code = 6;
  // 注册地址
  optional string reg_address = 7;
  // 统一社会信用代码
  optional string credit_code = 8;
  // 营业开始时间
  optional google.protobuf.Timestamp trade_begin_time = 9;
  // 营业结束时间
  optional google.protobuf.Timestamp trade_end_time = 10;
  // 营业结束时间是否长期
  optional bool is_trade_endless = 38;
  // 营业执照扫描件url
  optional string license_url = 11;
  // 邮箱
  optional string email = 12 [ (validate.rules).string.email = true ];
  // 联系人
  optional string contact_name = 36;
  // QQ
  optional string qq = 16;
  // WeiXin
  optional string wechat = 17;
  // 认证状态，0:未知 ,1:待提交, 2:待审核, 3:审核失败, 4:结束,
  optional uint32 auth_state = 18;
  // 客户经理id
  repeated uint64 manager_id = 19;
  // 认证类型: 1认证资质、2修改信息
  optional uint32 auth_type = 22;
  // 注册时间
  optional google.protobuf.Timestamp sign_in_at = 23;
  // 提交人(企业邮箱格式)
  optional string commit_name = 24;
  // 提交时间
  optional google.protobuf.Timestamp commit_at = 25;
  // 审核人(企业邮箱格式)
  optional string confirm_name = 26;
  // 审核时间
  optional google.protobuf.Timestamp confirm_at = 27;
  // 来源（1.开发者 2.后台 3.迁移）
  optional uint32 from = 28;
  // 不合规的参数名
  repeated string illegal_list = 29;
  // 合规的参数名
  repeated string compliance_list = 30;
  // 审核反馈
  optional string feedback = 31;
  // 超级管理员uid（创建时不传）
  optional uint64 su = 32;
  // 修改的参数key
  repeated string modified_parameter = 34;
  // 更新了哪些字段
  google.protobuf.FieldMask update_fields = 35;
  // 更新时间
  optional google.protobuf.Timestamp update_time = 37;
  // 联系人，国家区号
  optional string contact_national_code = 39;
  // 联系人手机号
  optional string contact_mobile = 40;
  // su关联id
  optional uint64 relation_id = 41;
}

// 发行商
// deprecated
message PublisherSummary {
  optional uint64 id = 1;
  optional string name = 3;
}

// 发行商
// deprecated
message PublisherInfo {
  // 发行商id
  optional uint64 id = 1;
  // 超级管理员账户 id
  optional uint64 su = 2;
  // 名称
  optional string name = 3;
  // 英文名称
  optional string en_name = 4;
  // 全称
  optional string full_name = 5;
  // 注册地区行政编码
  repeated string reg_region_code = 6;
  // 注册地址
  optional string reg_address = 7;
  // 统一社会信用代码
  optional string credit_code = 8;
  // 营业开始时间
  optional google.protobuf.Timestamp trade_begin_time = 9;
  // 营业结束时间
  optional google.protobuf.Timestamp trade_end_time = 10;
  // 营业结束时间是否长期
  optional bool is_trade_endless = 34;
  // 营业执照扫描件url
  optional string license_url = 11;
  // 邮箱
  optional string email = 12 [ (validate.rules).string.email = true ];
  // 联系人
  optional string contact_name = 33;
  // 0 显示状态 未知， 1 未认证，2 审核中，3 已认证
  optional uint32 state = 15;
  // 客户经理id
  repeated uint64 manager_id = 16;
  // 客户经理(企业邮箱格式)
  repeated string manager_name = 17;
  // 状态: true 启用、false 禁用
  optional bool enable = 20;
  // QQ
  optional string qq = 21;
  // WeiXin
  optional string wechat = 22;
  // 认证状态: 1.未认证；2已认证
  optional uint32 real_state = 23;
  // 注册时间
  optional google.protobuf.Timestamp create_at = 25;
  // 审核人(企业邮箱格式)
  optional string confirm_name = 26;
  // 审核时间
  optional google.protobuf.Timestamp confirm_at = 27;
  // 认证时间（审核通过时间）
  optional google.protobuf.Timestamp pass_at = 32;
  // 来源（1.注册 2.后台 3.迁移）
  optional uint32 from = 28;
  // 账号状态: true 启用、false 禁用
  optional bool user_enable = 29;
  // 是否存在审核数据
  bool exist_inspect = 30;
  // 用户注册邮箱(后台使用)
  string user_email = 31;
  // 联系人，国家区号
  optional string contact_national_code = 35;
  // 联系人手机号
  optional string contact_mobile = 36;
  // su关联id
  optional uint64 relation_id = 37;
}

// 商户审核信息
message MerchantInspectInfo {
  // ID(创建时不传)
  optional uint64 id = 1;
  // ID
  optional uint64 merchant_id = 2;
  // 名称
  optional string name = 3;
  // 英文名称
  optional string en_name = 4;
  // 全称
  optional string full_name = 5;
  // 注册地区行政编码
  repeated string reg_region_code = 6;
  // 注册地址
  optional string reg_address = 7;
  // 统一社会信用代码
  optional string credit_code = 8;
  // 营业开始时间
  optional google.protobuf.Timestamp trade_begin_time = 9;
  // 营业结束时间
  optional google.protobuf.Timestamp trade_end_time = 10;
  // 营业结束时间是否长期
  optional bool is_trade_endless = 11;
  // 营业执照扫描件url
  optional string license_url = 12;
  // 邮箱
  optional string email = 13 [ (validate.rules).string.email = true ];
  // 联系人
  optional string contact_name = 14;
  // QQ
  optional string qq = 15;
  // WeiXin
  optional string wechat = 16;
  // 认证状态，0:未知 ,1:待提交, 2:待审核, 3:审核失败, 4:结束,
  optional uint32 auth_state = 17;
  // 客户经理id
  repeated uint64 manager_id = 18;
  // 认证类型: 1认证资质、2修改信息
  optional uint32 auth_type = 19;
  // 注册时间
  optional google.protobuf.Timestamp sign_in_at = 20;
  // 提交人(企业邮箱格式)
  optional string commit_name = 21;
  // 提交时间
  optional google.protobuf.Timestamp commit_at = 22;
  // 审核人(企业邮箱格式)
  optional string confirm_name = 23;
  // 审核时间
  optional google.protobuf.Timestamp confirm_at = 24;
  // 来源（1.开发者 2.后台 3.迁移）
  optional uint32 from = 25;
  // 不合规的参数名
  repeated string illegal_list = 26;
  // 合规的参数名
  repeated string compliance_list = 27;
  // 审核反馈
  optional string feedback = 28;
  // 超级管理员uid（创建时不传）
  optional uint64 su = 29;
  // 修改的参数key（用于服务端返回前端认证修改了哪些字段，数据库对应字段为modified_parameter）
  repeated string modified_parameter = 30;
  // 更新了哪些字段（用于前端告诉服务端哪些字段发生了变化）
  google.protobuf.FieldMask update_fields = 31;
  // 更新时间
  optional google.protobuf.Timestamp update_time = 32;
  // 联系人，国家区号
  optional string contact_national_code = 33;
  // 联系人手机号
  optional string contact_mobile = 34;
  // 组织类型 1企业/2个人
  optional uint32 org_type = 35;
  //cp类型（1 国内；2海外）
  optional uint32 cp_type = 36;
  // 身份id
  optional string identity_id = 37;
  // 帐号等级
  optional string level_name = 38;
  // 注册国家或地区
  optional string reg_area = 39;
}

// 商户
message MerchantSummary {
  // 商户id
  optional uint64 id = 1;
  // 商户名称
  optional string name = 3;
  // 渠道数量
  optional uint32 channel_count = 4;
  // 等级
  optional string level_name = 5;
}

// 商户信息
message MerchantInfo {
  // 发行商id
  optional uint64 id = 1;
  // 超级管理员账户 id
  optional uint64 su = 2;
  // 名称
  optional string name = 3;
  // 英文名称
  optional string en_name = 4;
  // 全称
  optional string full_name = 5;
  // 注册地区行政编码
  repeated string reg_region_code = 6;
  // 注册地址
  optional string reg_address = 7;
  // 统一社会信用代码
  optional string credit_code = 8;
  // 营业开始时间
  optional google.protobuf.Timestamp trade_begin_time = 9;
  // 营业结束时间
  optional google.protobuf.Timestamp trade_end_time = 10;
  // 营业结束时间是否长期
  optional bool is_trade_endless = 11;
  // 营业执照扫描件url
  optional string license_url = 12;
  // 邮箱
  optional string email = 13 [ (validate.rules).string.email = true ];
  // 联系人
  optional string contact_name = 14;
  // 0 显示状态 未知， 1 未认证，2 审核中，3 已认证
  optional uint32 state = 15;
  // 客户经理id
  repeated uint64 manager_id = 16;
  // 客户经理(企业邮箱格式)
  repeated string manager_name = 17;
  // 状态: true 启用、false 禁用
  optional bool enable = 18;
  // QQ
  optional string qq = 19;
  // WeiXin
  optional string wechat = 20;
  // 认证状态: 1.未认证；2已认证
  optional uint32 real_state = 21;
  // 注册时间
  optional google.protobuf.Timestamp create_at = 22;
  // 审核人(企业邮箱格式)
  optional string confirm_name = 23;
  // 审核时间
  optional google.protobuf.Timestamp confirm_at = 24;
  // 认证时间（审核通过时间）
  optional google.protobuf.Timestamp pass_at = 25;
  // 来源（1.注册 2.后台 3.迁移）
  optional uint32 from = 26;
  // 账号状态: true 启用、false 禁用
  optional bool user_enable = 27;
  // 是否存在审核数据
  bool exist_inspect = 28;
  // 用户注册邮箱(后台使用)
  string user_email = 29;
  // 联系人，国家区号
  optional string contact_national_code = 30;
  // 联系人手机号
  optional string contact_mobile = 31[(validate.rules).string.min_len = 5];
  // 游戏数量
  optional uint32 game_count = 32;
  // 游戏数量
  optional uint32 channel_count = 33;
  // 组织类型 1企业/2个人
  optional uint32 org_type = 34;
  //cp类型（1 国内；2海外）
  optional uint32 cp_type = 35;
  // 身份id
  optional string identity_id = 36;
  // 商户等级，修改为字符串类型，lv0 lv1 lv2 unlimited
  optional string level_name = 37;
  // 普通版策略数量
  optional uint32 normal_channel_count = 38;
  // 纯净版策略数量
  optional uint32 pure_channel_count = 39;
  // 增强版策略数量
  optional uint32 enhanced_channel_count = 40;
  optional int64  data_cp_id = 41;
  optional int64  data_channel_id = 42;
  // 注册国家或地区
  optional string reg_area = 43;
  // 是否特殊渲染
  // 部分商户需要隐藏开发者相关菜单
  optional bool is_special_render = 44;
}

message MerchantOpenApiInfo {
  string secret = 1;
}