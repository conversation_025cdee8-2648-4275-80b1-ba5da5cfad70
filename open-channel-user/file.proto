syntax = "proto3";

package channeluser.v1;

option go_package = "open-proto/open-channel-user/api/extern;extern";

//import "google/api/field_behavior.proto";
service Upload {
  // 为手机生成验证码
  rpc CheckUploadCount (CheckUploadCountRequest) returns (CheckUploadCountReply) {
  }
  // 为邮箱生成验证码
  rpc IncrUploadCount (IncrUploadCountRequest) returns (IncrUploadCountReply) {
  }
}

message CheckUploadCountRequest{
  // uid
  uint64 uid = 1;
}
message CheckUploadCountReply{
}

message IncrUploadCountRequest{
  // uid
  uint64 uid = 1;
}
message IncrUploadCountReply{
}

message UploadFileRequest {
  // 1.营业执照扫描件
  string  name = 1;
  // 资源类型 (Ep: "png","jpg")
  string content_type = 2;
}


message DownloadFileRequest {
  // 1.营业执照扫描件
  string  name = 1;
  // uid
  uint64  uid = 2;
}

