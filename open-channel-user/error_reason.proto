syntax = "proto3";

package channeluser.v1;

import "errors/errors.proto";

option go_package = "open-proto/open-channel-user/api/extern;extern";

enum ErrorReason {
  option (errors.default_code) = 500;

  UNKNOWN_ERROR = 0;

  // 验证码相关 [1, 500)
  CAPTCHA_GENERATE_FAILED = 1[(errors.code) = 500];  // 生成校验码失败
  CAPTCHA_GENERATE_TOO_FREQUENTLY = 2[(errors.code) = 400]; // 校验码生成过频
  CAPTCHA_SEND_EMAIL_FAILED = 3[(errors.code) = 400]; // 发送邮件失败
  CAPTCHA_EXPIRED_OR_NOT_EXIST = 4[(errors.code) = 400]; // 验证码已失效或者为生成
  CAPTCHA_NO_MATCH = 5[(errors.code) = 400]; // 验证码不正确
  CAPTCHA_EMAIL_INCORRECT = 6 [(errors.code) = 400]; // 邮箱 与预留的邮箱不符，请重新输入！

  // User 相关 [500, 1000)
  USER_PASSWORD_INVALID = 500[(errors.code) = 500];  // 用户密码格式非法
  USER_ACCOUNT_INCORRECT = 501[(errors.code) = 400];  // 账号输入错误或此账号未注册，请重新输入，如您暂未注册账号请点击注册
  USER_PASSWORD_INCORRECT = 502[(errors.code) = 400];  // 密码输入错误，请重新输入，如您已遗忘密码请点击忘记密码！
  USER_RETRIEVE_FAILED = 503[(errors.code) = 500]; // 找回密码失败
  USER_EMAIL_INCORRECT = 504[(errors.code) = 400]; //  你输入的 邮箱 与预留的邮箱不符，请重新输入！
  USER_SAME_PASSWORD = 505[(errors.code) = 400]; // 新密码与原密码相同，请重新输入新的修改密码！
  USER_NOT_FOUND = 506[(errors.code) = 400]; // 用户不存在
  USER_SAVE_PASSWORD_FAILED = 507[(errors.code) = 500]; // 保存密码失败
  USER_CREATE_FAILED = 508[(errors.code) = 500]; // 创建用户失败
  USER_LOGIN_FAILED = 509[(errors.code) = 500]; // 登录失败
  USER_RESET_PASSWORD_FAILED = 510[(errors.code) = 500]; // 重置密码失败
  USER_PASSWORD_NO_MATCH = 511[(errors.code) = 400]; // 您的原始密码输入错误，请重新输入！

   // 商户相关 [1000, 2000)
   MERCHANT_EMAIL_EXISTS = 1000[(errors.code) = 400];
   MERCHANT_MOBILE_EXISTS = 1001[(errors.code) = 400];
   MERCHANT_NO_FOUND = 1002[(errors.code) = 400];
   MERCHANT_NAME_INVALID = 1003[(errors.code) = 400];
   MERCHANT_EMAIL_INVALID = 1004[(errors.code) = 400];
   MERCHANT_LOGIN_FAILED = 1005[(errors.code) = 500];
   MERCHANT_MOBILE_INVALID = 1006[(errors.code) = 400];
   MERCHANT_INFO_INVALID = 1007[(errors.code) = 400];
   MERCHANT_SIGN_FAILED = 1008[(errors.code) = 400];
   MERCHANT_IS_EXISTS = 1009[(errors.code) = 400]; //商户已存在
   MERCHANT_NAME_EXISTS = 1010[(errors.code) = 400];  //该名称已被注册，请重新输入！
   MERCHANT_INFO_CREDIT_CODE_INVALID = 1012[(errors.code) = 400];//  该代码已被注册，请重新输入！
   INSPECT_AUTH_STATE_CHANGED  = 1013[(errors.code) = 400];//您当前审核状态有变化，将为您自动刷新 3S ！
   INSPECT_PARAMETER_NOT_CHANGED  = 1014[(errors.code) = 400];//存在未修改字段 ！
   INSPECT_EXISTS = 1015[(errors.code) = 400]; //已存在认证、不能重复创建
   MERCHANT_MOBILE_HAS_BEEN_BOUND = 1016[(errors.code) = 400];//当前手机号已被绑定，请输入正确的手机号！
   MERCHANT_EMAIL_HAS_BEEN_BOUND = 1017[(errors.code) = 400];//当前邮箱已被绑定，请输入正确的邮箱！
   MERCHANT_EN_NAME_EXISTS = 1018[(errors.code) = 400];  //该英文名称已被注册，请重新输入！
   MERCHANT_FULL_NAME_EXISTS = 1019[(errors.code) = 400];  //该厂商全称已被注册，请重新输入！
   UPLOAD_FILE_TOO_FREQUENTLY = 1020[(errors.code) = 400]; // 上传文件太频繁
   PARAMETER_ERROR = 1021[(errors.code) = 400];  // 参数错误
   MERCHANT_FIELD_DUPLICATES = 1022[(errors.code) = 400];  // 存在重复的参数
   MERCHANT_REGISTER_ALREADY = 1023[(errors.code) = 400];  // 商户已经注册
   MERCHANT_REGISTER_FAILED = 1024[(errors.code) = 400];  // 商户注册失败

  // 消息相关错误 [2001, 2500)
  MESSAGE_NOT_EXIST = 2001[(errors.code)  = 404 ]; // 消息不存在
  USER_MESSAGE_NOT_EXIST = 2002[(errors.code)  = 404 ]; // 用户的消息不存在

  // site 相关[2501,3000)
  SITE_DEMAND_NOT_EXISTS = 2501[(errors.code)  = 404 ]; // 网站需求不存在
  SITE_DEMAND_FIND_FAILED = 2502[(errors.code)  = 500 ]; // 查找网站需求失败
  SITE_DEMAND_CREATE_FAILED = 2503[(errors.code)  = 500 ]; // 创建网站需求失败
  SITE_DEMAND_UPDATE_FAILED = 2504[(errors.code)  = 500 ]; // 更新网站需求失败
  SITE_DEMAND_SEARCH_FAILED = 2505[(errors.code)  = 500 ]; // 搜索网站需求失败
  SITE_DEMAND_DELETE_FAILED = 2506[(errors.code)  = 500 ]; // 删除网站需求失败
  SITE_DEMAND_DOMAIN_DUPLICATE = 2507 [(errors.code)  = 400 ]; // 该域名已存在，请重新输入
  SITE_DEMAND_STATE_CHANGED = 2508[(errors.code)  = 400 ]; // 数据发生变化！3s后将自动刷新

  // 数据中台
  GET_DATA_TIMEOUT = 3001[(errors.code)  = 504 ];// 获取数据超时

  // 商户渠道链接相关
  MERCHANT_CHANNEL_DOMAIN_IS_VALID = 4501[(errors.code) = 400]; // 渠道域名不合法
  MERCHANT_CHANNEL_FIND_FAILED = 4502 [(errors.code)  = 500 ]; // 搜索渠道失败
  MERCHANT_CHANNEL_NAME_EXIST = 4503 [(errors.code)  = 400 ]; // 渠道链接已存在
  MERCHANT_CHANNEL_CREATE_FAILED = 4504 [(errors.code)  = 500 ]; // 创建渠道链接失败
  MERCHANT_CHANNEL_DOMAIN_EXIST = 4505 [(errors.code)  = 400 ]; // 渠道链接已存在
  MERCHANT_CHANNEL_NOT_EXISTS = 4506 [(errors.code)  = 404 ]; // 渠道链接不存在
  MERCHANT_CHANNEL_OWNERSHIP_FORBIDDEN  = 4507 [(errors.code) = 403]; // 渠道链接所有权错误
  MERCHANT_CHANNEL_GENERATE_URL_TOO_FREQUENTLY = 4508[(errors.code) = 400]; // 随机生成链接过于频繁

  // 渠道链接审核相关
  MERCHANT_CHANNEL_INSPECT_NOT_EXISTS = 5501[(errors.code) = 400]; // 审核信息不存在！
}
