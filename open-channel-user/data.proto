syntax = "proto3";

package channeluser.v1;

import "validate/validate.proto";
import "google/protobuf/timestamp.proto";
import "google/protobuf/empty.proto";
import "google/protobuf/struct.proto";
import "open-proto/open-common/data.proto";

option go_package = "open-proto/open-channel-user/api/extern;extern";


service DataService {
  rpc GetGeneralOverviewData(GetGeneralOverviewDataRequest) returns (GetGeneralOverviewDataReply) {}
  rpc GetBusinessGeneralOverviewData(GetBusinessGeneralOverviewDataRequest) returns (GetBusinessGeneralOverviewDataReply) {}
  rpc GetBusinessFilterData(GetBusinessFilterDataRequest) returns (GetBusinessFilterDataReply){}
  rpc GetBusinessDimensionFilterList(.google.protobuf.Empty) returns (GetBusinessDimensionFilterListReply){}
  rpc GetBusinessData(GetBusinessDataRequest) returns (GetBusinessDataReply){}
  rpc ExportBusinessData(ExportBusinessDataRequest) returns (ExportBusinessDataReply){}
  rpc GetBusinessChartData(GetBusinessChartDataRequest) returns (GetBusinessChartDataReply){}
  // 运营管理-收益汇总
  rpc GetBusinessIncomeOverview(BusinessIncomeOverviewRequest) returns (BusinessIncomeOverviewReply) {}
}

message BusinessIncomeOverviewRequest {
  string name = 1 [deprecated = true];
  int64 data_cp_id = 2;
  int64 data_channel_id = 3;
}

message DayData {
  double value = 1;
  string day = 2;
}

message BusinessIncomeOverviewReply {
  double yesterday_estimated_earnings = 1;
  double today_estimated_earnings = 2;
  double total_estimated_earnings = 3;
  repeated DayData days = 4;
}

message GetGeneralOverviewDataRequest {
  string name = 1 [deprecated = true];
  // 服务端微服务使用，前端不填。
  uint64 uid = 2;
  int64 data_cp_id = 3;
  int64 data_channel_id = 4;
}

message GetGeneralOverviewDataReply {
  // 网站数量
  int32 site_count = 1;
  // 预估收入
  double estimated_earnings = 2;
  // ECPM
  double impressions_rpm = 3;
  .google.protobuf.Timestamp start_time = 4;
  .google.protobuf.Timestamp end_time = 5;
  // 今年每月收入数据
  repeated .data.v1.MonthData estimated_earning_month = 6;
  repeated .data.v1.MonthData impressions_rpm_month = 7;
  // 收入月环比
  double estimated_earning_mom = 8;
  //
  double impressions_rpm_mom = 9;
  double yesterday_estimated_earnings = 10;
  double today_estimated_earnings = 11;
  double total_estimated_earnings = 12;
  uint32 game_count = 13;
  uint32 pass_count = 14;
}


message GetBusinessChartDataRequest {
  repeated string  dimensions = 1;
  repeated string metrics = 2;
  string filters = 3;
  int32 page_size = 4;
  int32 page = 5;
  Date start_date = 6;
  Date end_date = 7;
  repeated string filter_fields = 8;
  string name = 9;
  repeated string data_dimensions = 10;
  int64 data_channel_id = 11;
}

message GetBusinessChartDataReply {
  repeated .google.protobuf.Struct rows = 1;
}

message GetBusinessGeneralOverviewDataRequest {
  string name = 1;
  // 服务端微服务使用，前端不填。
  uint64 uid = 2;
  int64 data_cp_id = 3;
  int64 data_channel_id = 46;
}


message GetBusinessGeneralOverviewDataReply {
  // 网站数量
  int32 site_count = 1;
  // 预估收入
  double estimated_earnings = 2;
  // 网页浏览量
  int64 page_views = 3;
  // 广告展示次数
  int64 impressions = 4;
  // ECPM
  double impressions_rpm = 5;
  .google.protobuf.Timestamp start_time = 6;
  .google.protobuf.Timestamp end_time = 7;
  // 今年每月收入数据
  repeated .data.v1.MonthData estimated_earning_month = 10;
  repeated .data.v1.MonthData impressions_rpm_month = 11;
  // 收入月环比
  double estimated_earning_mom = 8;
  // ecpm月环比
  double impressions_rpm_mom = 9;
}

message ExportBusinessDataRequest {
  repeated string  dimensions = 1;
  repeated string metrics = 2;
  string filters = 3;
  Date start_date = 4;
  Date end_date = 5;
  repeated string order = 6;
  string name = 7 [deprecated = true];
  string language = 8;
  int64 data_channel_id = 9;
}

message ExportBusinessDataReply {
  bytes data = 1;
  string file_name = 2;
}

message Date {
  int32 day = 1;
  int32 year = 2;
  int32 month = 3;
}

message GetBusinessDataRequest {
  repeated string dimensions = 1;
  repeated string metrics = 2;
  string filters = 3;
  int32 page_size = 4;
  int32 page = 5;
  Date start_date = 6;
  Date end_date = 7;
  repeated string order = 8;
  bool is_contrast = 9;
  Date contrast_start_date = 10;
  Date contrast_end_date = 11;
  repeated string filter_fields = 12;
  string name = 13 [deprecated = true];
  int64 data_channel_id = 14;
}

message GetBusinessDataReply {
  int32     total = 1;
  repeated .google.protobuf.Struct rows = 2;
  repeated .google.protobuf.Struct totals = 3;
  repeated .google.protobuf.Struct charts = 4;
}

message GetBusinessDimensionFilterListReply {
  message DimensionFilter {
    repeated string dimensions = 1;
    repeated string filters = 2;
  }
  repeated DimensionFilter dimension_filters = 2;
}

message GetBusinessFilterDataRequest {
  string name = 1;
  string field = 2;
}

message GetBusinessFilterDataReply {
  repeated string data = 1;
}

