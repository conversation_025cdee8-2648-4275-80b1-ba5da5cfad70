package token

import (
	"context"
	"github.com/go-kratos/kratos/v2/middleware"
	"time"
)

const (
	StateLocked = 2

	Uid          = "uid"
	BusinessType = "business_type"
)

type TimeFunc func(context.Context, uint64) (time.Time, int32, error)

// CheckBusinessTypeFunc 检查业务类型函数
type CheckBusinessTypeFunc func(context.Context, string) bool

type Option func(*options)

// Parser is a jwt parser
type options struct {
	timeFunc              TimeFunc
	checkBusinessTypeFunc CheckBusinessTypeFunc
}

// WithUserFunc 获得上次退出登录的时间及用户状态
func WithUserFunc(f TimeFunc) Option {
	return func(o *options) {
		o.timeFunc = f
	}
}

// WithCheckBusinessTypeFunc 检查业务类型
func WithCheckBusinessTypeFunc(f CheckBusinessTypeFunc) Option {
	return func(o *options) {
		o.checkBusinessTypeFunc = f
	}
}

// Server token验证中间件
func Server(opts ...Option) middleware.Middleware {
	o := &options{}
	for _, opt := range opts {
		opt(o)
	}
	return func(handler middleware.Handler) middleware.Handler {
		return func(ctx context.Context, req interface{}) (interface{}, error) {
			// 从context中解析claims
			claims, err := ParseAccessJwtTokenFromContext(ctx)
			if err != nil {
				return nil, err
			}

			ctx, err = claimsCheck(ctx, o.timeFunc, o.checkBusinessTypeFunc, claims)
			if err != nil {
				return nil, err
			}

			return handler(ctx, req)
		}
	}
}

// ParseUidFromContext 从context中解析uid
func ParseUidFromContext(ctx context.Context) (uid uint64, ok bool) {
	tmp := ctx.Value(Uid)
	if tmp != nil {
		uid, ok = tmp.(uint64)
		return
	}
	return
}
