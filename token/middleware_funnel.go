package token

import (
	"github.com/go-kratos/kratos/v2/errors"
	"github.com/go-kratos/kratos/v2/middleware"
	"github.com/go-kratos/kratos/v2/transport"
	"strings"
	"time"
)

/*
	funnel 为 go-kratos 的 jwt 中间件的升级版本，支持在token解析失败的情况下继续处理业务。
    可对比 github.com/go-kratos/kratos/v2@v2.8.0/middleware/auth/jwt/jwt.go。
*/

import (
	"context"
	"github.com/golang-jwt/jwt/v5"
)

type authKey struct{}

const (

	// bearerWord the bearer key word for authorization
	bearerWord string = "Bearer"

	// bearerFormat authorization token format
	bearerFormat string = "Bearer %s"

	// authorizationKey holds the key used to store the JWT Token in the request tokenHeader.
	authorizationKey string = "Authorization"

	// reason holds the error reason.
	reason string = "UNAUTHORIZED"
)

var (
	ErrMissingJwtToken        = errors.Unauthorized(reason, "JWT token is missing")
	ErrMissingKeyFunc         = errors.Unauthorized(reason, "keyFunc is missing")
	ErrTokenInvalid           = errors.Unauthorized(reason, "Token is invalid")
	ErrTokenExpired           = errors.Unauthorized(reason, "JWT token has expired")
	ErrTokenParseFail         = errors.Unauthorized(reason, "Fail to parse JWT token ")
	ErrUnSupportSigningMethod = errors.Unauthorized(reason, "Wrong signing method")
	ErrWrongContext           = errors.Unauthorized(reason, "Wrong context for middleware")
	ErrNeedTokenProvider      = errors.Unauthorized(reason, "Token provider is missing")
	ErrSignToken              = errors.Unauthorized(reason, "Can not sign token.Is the key correct?")
	ErrGetKey                 = errors.Unauthorized(reason, "Can not get key while signing token")
	ErrTokenLogout            = errors.Unauthorized(reason, "User is logout")
	ErrUserLocked             = errors.Unauthorized("USER_FORBIDDEN", "User is forbidden")
)

type FunnelOption func(o *funnelOptions)

// Parser is a jwt parser
type funnelOptions struct {
	signingMethod jwt.SigningMethod
	claims        func() jwt.Claims

	timeFunc              TimeFunc
	checkBusinessTypeFunc CheckBusinessTypeFunc
}

// FunnelWithUserFunc 获得上次退出登录的时间及用户状态
func FunnelWithUserFunc(f TimeFunc) FunnelOption {
	return func(o *funnelOptions) {
		o.timeFunc = f
	}
}

// FunnelWithCheckBusinessTypeFunc 检查业务类型
func FunnelWithCheckBusinessTypeFunc(f CheckBusinessTypeFunc) FunnelOption {
	return func(o *funnelOptions) {
		o.checkBusinessTypeFunc = f
	}
}

// FunnelWithSigningMethod with signing method option.
func FunnelWithSigningMethod(method jwt.SigningMethod) FunnelOption {
	return func(o *funnelOptions) {
		o.signingMethod = method
	}
}

// FunnelWithClaims with customer claim
// If you use it in Server, f needs to return a new jwt.Claims object each time to avoid concurrent write problems
// If you use it in Client, f only needs to return a single object to provide performance
func FunnelWithClaims(f func() jwt.Claims) FunnelOption {
	return func(o *funnelOptions) {
		o.claims = f
	}
}

// FunnelServer is a server auth middleware. Check the token and extract the info from token.
func FunnelServer(keyFunc jwt.Keyfunc, opts ...FunnelOption) middleware.Middleware {
	o := &funnelOptions{
		signingMethod: jwt.SigningMethodHS256,
	}
	for _, opt := range opts {
		opt(o)
	}
	return func(handler middleware.Handler) middleware.Handler {
		return func(ctx context.Context, req interface{}) (interface{}, error) {
			if header, ok := transport.FromServerContext(ctx); ok {
				if keyFunc == nil {
					return nil, ErrMissingKeyFunc
				}
				auths := strings.SplitN(header.RequestHeader().Get(authorizationKey), " ", 2)
				// if token is valid, put the claims into context
				if len(auths) == 2 && strings.EqualFold(auths[0], bearerWord) {
					jwtToken := auths[1]
					var (
						tokenInfo *jwt.Token
						err       error
					)
					if o.claims != nil {
						tokenInfo, err = jwt.ParseWithClaims(jwtToken, o.claims(), keyFunc)
					} else {
						tokenInfo, err = jwt.Parse(jwtToken, keyFunc)
					}
					// if token is valid, put the claims into context
					if err == nil &&
						tokenInfo.Valid &&
						tokenInfo.Method == o.signingMethod {
						ctx = context.WithValue(ctx, authKey{}, tokenInfo.Claims)

						var claims *MyCustomClaims
						claims, err = ParseAccessJwtToken(tokenInfo.Claims)
						if err == nil {
							ctx, _ = claimsCheck(ctx, o.timeFunc, o.checkBusinessTypeFunc, claims)
						}
					}
				}
				return handler(ctx, req)
			}
			return nil, ErrWrongContext
		}
	}
}

func claimsCheck(ctx context.Context,
	timeFunc TimeFunc,
	checkBusinessTypeFunc CheckBusinessTypeFunc,
	claims *MyCustomClaims) (ctxRet context.Context, err error) {
	ctxRet = ctx
	// 如果设置了获取用户状态函数，就执行并检查用户状态
	if timeFunc != nil {
		// 获得上次退出登录的时间及用户状态
		var (
			lastLogoutTime time.Time
			state          int32
		)
		lastLogoutTime, state, err = timeFunc(ctx, claims.Uid)
		if err != nil {
			return
		}
		// 用户被锁定
		if state == StateLocked {
			err = ErrUserLocked
			return
		}
		// token的签发时间在上次退出登录时间之前
		if claims.IssuedAt.Before(lastLogoutTime) {
			err = ErrTokenLogout
			return
		}
	}

	// 如果设置了检查业务类型函数，就执行并检查业务类型
	if checkBusinessTypeFunc != nil {
		// 检查业务类型
		if !checkBusinessTypeFunc(ctx, claims.BusinessType) {
			err = ErrTokenInvalid
			return
		}
	}

	// 将uid放入context
	ctxRet = context.WithValue(ctx, Uid, claims.Uid)
	return
}
