package token

import (
	"context"
	"fmt"
	"github.com/go-kratos/kratos/v2/middleware/auth/jwt"
	jwtv5 "github.com/golang-jwt/jwt/v5"
	"github.com/pkg/errors"
	"net/http"
	"strings"
	"time"
)

type MyCustomClaims struct {
	Uid          uint64 `json:"uid"`
	BusinessType string `json:"business_type,omitempty"` // 业务类型，避免在相同域名下同名cookie冲突。例如：同一个minigame.com下，开发者登录后的token和发行商登录后的token
	jwtv5.RegisteredClaims
}

// JwtToken generate jwt
func JwtToken(secret string, expiredDuration time.Duration, uid uint64) (string, error) {
	iat := time.Now()
	claims := &MyCustomClaims{
		Uid: uid,
		RegisteredClaims: jwtv5.RegisteredClaims{
			IssuedAt: jwtv5.NewNumericDate(iat),
		},
	}
	if expiredDuration > 0 {
		claims.ExpiresAt = jwtv5.NewNumericDate(iat.Add(expiredDuration))
	}
	return jwtv5.NewWithClaims(jwtv5.SigningMethodHS256, claims).SignedString([]byte(secret))
}

// JwtToken2 generate jwt
func JwtToken2(secret, businessType string, expiredDuration time.Duration, uid uint64) (string, error) {
	iat := time.Now()
	claims := &MyCustomClaims{
		Uid:          uid,
		BusinessType: businessType,
		RegisteredClaims: jwtv5.RegisteredClaims{
			IssuedAt:  jwtv5.NewNumericDate(iat),
			ExpiresAt: jwtv5.NewNumericDate(iat.Add(expiredDuration)),
		},
	}
	return jwtv5.NewWithClaims(jwtv5.SigningMethodHS256, claims).SignedString([]byte(secret))
}

var ErrEmptySecretKey = errors.New("secret key no set")

// ParseJwtToken parse jwt token to MyCustomClaims
// if token is expired, ErrTokenExpired will be return.
func ParseJwtToken(secret, token string) (claims *MyCustomClaims, err error) {
	var t *jwtv5.Token
	t, err = parseJwtToken(secret, token)
	if err != nil {
		err = fmt.Errorf("parseJwtToken %v failed : %v", token, err)
		return
	}
	var ok bool
	if claims, ok = t.Claims.(*MyCustomClaims); !ok {
		err = fmt.Errorf("jwt.Token %v assert to *MyCustomClaims error", t)
		return
	}
	return
}

// parseJwtToken parse token string into jwt.Token
func parseJwtToken(secret, token string) (t *jwtv5.Token, err error) {
	if len(secret) <= 0 {
		err = ErrEmptySecretKey
		return
	}

	return jwtv5.ParseWithClaims(
		token, &MyCustomClaims{}, func(token *jwtv5.Token) (interface{}, error) {
			return []byte(secret), nil
		},
	)
}
func ParseAccessJwtTokenFromContext(ctx context.Context) (mc *MyCustomClaims, err error) {
	claims, ok := jwt.FromContext(ctx)
	if !ok {
		return nil, errors.New("no jwt jwt in context")
	}

	if mc, err = ParseAccessJwtToken(claims); err != nil {
		return nil, err
	}
	return mc, nil
}
func ParseAccessJwtToken(claims jwtv5.Claims) (*MyCustomClaims, error) {
	if claims == nil {
		return nil, errors.New("claims is nil")
	}

	mc, ok := claims.(*MyCustomClaims)
	if !ok {
		return nil, errors.New("claims is not map claims")
	}

	return mc, nil
}

// ParseUidFromHeader 从Header中解析jwt token
func ParseUidFromHeader(keyFunc jwtv5.Keyfunc, header http.Header) (uid uint64, has bool, err error) {
	auths := strings.SplitN(header.Get("Authorization"), " ", 2)
	if len(auths) != 2 || !strings.EqualFold(auths[0], "Bearer") {
		err = ErrMissingJwtToken
		return
	}
	jwtToken := auths[1]
	var (
		tokenInfo *jwtv5.Token
	)
	tokenInfo, err = jwtv5.Parse(jwtToken, keyFunc)
	if err != nil {
		return
	}
	if !tokenInfo.Valid {
		err = errors.New("ErrTokenInvalid")
		return
	}
	jcd, has := tokenInfo.Claims.(jwtv5.MapClaims)
	if !has {
		err = errors.New("token is not implement MapClaims")
		return
	}
	return uint64(jcd[Uid].(float64)), true, nil
}
