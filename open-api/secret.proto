syntax = "proto3";

package openapi.v1;

import "validate/validate.proto";
import "google/protobuf/empty.proto";
import "google/protobuf/timestamp.proto";
import "open-proto/open-common/api.proto";
option go_package = "open-proto/open-api/api/extern;extern";

service Secret {
  // 获取密钥信息
  rpc Secret(api.v1.SecretRequest) returns (api.v1.SecretReply) {}
  // 创建密钥信息
  rpc Create(api.v1.SecretCreateRequest) returns (google.protobuf.Empty) {}
}
