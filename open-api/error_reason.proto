syntax = "proto3";

package openapi.v1;

import "errors/errors.proto";

option go_package = "open-proto/open-admin-user/api/extern;extern";

enum ErrorReason {
  option (errors.default_code) = 500;

  UNKNOWN_ERROR = 0; // 未知错误
  PARAMETER_ERROR = 1[(errors.code) = 400];  // 参数错误
  SERVER_INTERNAL = 2[(errors.code) = 500]; // 服务器内部错误

  // 商户相关错误 [500,1000)
  MERCHANT_OPEN_API_INVALID = 500[(errors.code) = 400];  // 商户不可用
  MERCHANT_OPEN_API_CHANNEL_NOT_FOUND = 501[(errors.code) = 404]; // 商户渠道不存在
  MERCHANT_OPEN_API_CHANNEL_FORBIDDEN = 502[(errors.code) = 403]; // 商户渠道禁止访问
  MERCHANT_OPEN_API_GAME_NOT_FOUND = 503[(errors.code) = 404]; // 游戏不存在
}
