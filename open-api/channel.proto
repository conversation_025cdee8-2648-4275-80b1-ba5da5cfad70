syntax = "proto3";

package openapi.v1;

import "validate/validate.proto";
import "google/protobuf/empty.proto";
import "google/protobuf/timestamp.proto";
import "open-proto/open-common/api.proto";
option go_package = "open-proto/open-api/api/extern;extern";

service Channel {
  // 游戏链接列表
  rpc GameLinks(api.v1.ChannelGameLinksRequest) returns (api.v1.ChannelGameLinksReply) {}
  // 渠道游戏详情
  rpc GameDetail(api.v1.ChannelGameDetailRequest) returns (api.v1.ChannelGameDetailReply) {}
}
