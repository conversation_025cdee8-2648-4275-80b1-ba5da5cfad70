-- 为所有相关表添加 account 和 platform 字段以支持多账户多平台
-- 根据表行数优化执行策略：
-- 小表（<100万行）：可以直接执行
-- 中等表（100万-1000万行）：建议在低峰期执行
-- 大表（>1000万行）：需要特殊处理，建议分批执行或使用在线DDL工具

-- ========================================
-- 第一部分：分区表（主表，行数为0，可以快速执行）
-- ========================================

-- 1. URL Channel Country Ad Format 分区表 (主表: 0行)
ALTER TABLE url_channel_country_ad_format_info_partitions
    ADD COLUMN IF NOT EXISTS account  varchar NOT NULL DEFAULT 'unknown',
    ADD COLUMN IF NOT EXISTS platform varchar NOT NULL DEFAULT 'adsense';

-- 2. Site Country Ad Format 分区表 (主表: 0行)
ALTER TABLE site_country_ad_format_partitions
    ADD COLUMN IF NOT EXISTS account  varchar NOT NULL DEFAULT 'unknown',
    ADD COLUMN IF NOT EXISTS platform varchar NOT NULL DEFAULT 'adsense';

-- 3. Channel Country Ad Format 分区表 (主表: 0行)
ALTER TABLE channel_country_ad_format_partitions
    ADD COLUMN IF NOT EXISTS account  varchar NOT NULL DEFAULT 'unknown',
    ADD COLUMN IF NOT EXISTS platform varchar NOT NULL DEFAULT 'adsense';

-- 4. Game Country 分区表 (主表: 0行)
ALTER TABLE game_country_partitions
    ADD COLUMN IF NOT EXISTS account  varchar NOT NULL DEFAULT 'unknown',
    ADD COLUMN IF NOT EXISTS platform varchar NOT NULL DEFAULT 'adsense';

-- 5. Site Ad Format Histories 分区表 (主表: 0行)
ALTER TABLE site_ad_format_histories
    ADD COLUMN IF NOT EXISTS account  varchar NOT NULL DEFAULT 'unknown',
    ADD COLUMN IF NOT EXISTS platform varchar NOT NULL DEFAULT 'adsense';

-- ========================================
-- 第二部分：小表（<100万行，可以快速执行）
-- ========================================

-- Site Ad Units (1,688行)
ALTER TABLE site_ad_units
    ADD COLUMN IF NOT EXISTS account  varchar NOT NULL DEFAULT 'unknown',
    ADD COLUMN IF NOT EXISTS platform varchar NOT NULL DEFAULT 'adsense';

-- Custom Channels (922行)
ALTER TABLE custom_channels
    ADD COLUMN IF NOT EXISTS account  varchar NOT NULL DEFAULT 'unknown',
    ADD COLUMN IF NOT EXISTS platform varchar NOT NULL DEFAULT 'adsense';

-- Channels (388,156行)
ALTER TABLE channels
    ADD COLUMN IF NOT EXISTS account  varchar NOT NULL DEFAULT 'unknown',
    ADD COLUMN IF NOT EXISTS platform varchar NOT NULL DEFAULT 'adsense';

-- Games (371,842行)
ALTER TABLE games
    ADD COLUMN IF NOT EXISTS account  varchar NOT NULL DEFAULT 'unknown',
    ADD COLUMN IF NOT EXISTS platform varchar NOT NULL DEFAULT 'adsense';

-- Sites (547,463行)
ALTER TABLE sites
    ADD COLUMN IF NOT EXISTS account  varchar NOT NULL DEFAULT 'unknown',
    ADD COLUMN IF NOT EXISTS platform varchar NOT NULL DEFAULT 'adsense';

-- Site HKD (205,959行)
ALTER TABLE site_hkd
    ADD COLUMN IF NOT EXISTS account  varchar NOT NULL DEFAULT 'unknown',
    ADD COLUMN IF NOT EXISTS platform varchar NOT NULL DEFAULT 'adsense';

-- Channel HKD (134,594行)
ALTER TABLE channel_hkd
    ADD COLUMN IF NOT EXISTS account  varchar NOT NULL DEFAULT 'unknown',
    ADD COLUMN IF NOT EXISTS platform varchar NOT NULL DEFAULT 'adsense';

-- Game HKD (134,231行)
ALTER TABLE game_hkd
    ADD COLUMN IF NOT EXISTS account  varchar NOT NULL DEFAULT 'unknown',
    ADD COLUMN IF NOT EXISTS platform varchar NOT NULL DEFAULT 'adsense';

-- Channel Ad Formats (1,567,442行)
ALTER TABLE channel_ad_formats
    ADD COLUMN IF NOT EXISTS account  varchar NOT NULL DEFAULT 'unknown',
    ADD COLUMN IF NOT EXISTS platform varchar NOT NULL DEFAULT 'adsense';

-- Channel Ad Format HKD (545,064行)
ALTER TABLE channel_ad_format_hkd
    ADD COLUMN IF NOT EXISTS account  varchar NOT NULL DEFAULT 'unknown',
    ADD COLUMN IF NOT EXISTS platform varchar NOT NULL DEFAULT 'adsense';

-- Site Country HKD (3,862,029行) - 注意：这是中等大小的表
ALTER TABLE site_country_hkd
    ADD COLUMN IF NOT EXISTS account  varchar NOT NULL DEFAULT 'unknown',
    ADD COLUMN IF NOT EXISTS platform varchar NOT NULL DEFAULT 'adsense';

-- Site Ad Format HKD (609,166行)
ALTER TABLE site_ad_format_hkd
    ADD COLUMN IF NOT EXISTS account  varchar NOT NULL DEFAULT 'unknown',
    ADD COLUMN IF NOT EXISTS platform varchar NOT NULL DEFAULT 'adsense';

-- URL Channel Info HKD (2,344,111行)
ALTER TABLE url_channel_info_hkd
    ADD COLUMN IF NOT EXISTS account  varchar NOT NULL DEFAULT 'unknown',
    ADD COLUMN IF NOT EXISTS platform varchar NOT NULL DEFAULT 'adsense';

-- Page URL Info HKD (1,169,805行)
ALTER TABLE page_url_info_hkd
    ADD COLUMN IF NOT EXISTS account  varchar NOT NULL DEFAULT 'unknown',
    ADD COLUMN IF NOT EXISTS platform varchar NOT NULL DEFAULT 'adsense';

-- URL Channel All Info HKD (159,731行)
ALTER TABLE url_channel_all_info_hkd
    ADD COLUMN IF NOT EXISTS account  varchar NOT NULL DEFAULT 'unknown',
    ADD COLUMN IF NOT EXISTS platform varchar NOT NULL DEFAULT 'adsense';

-- Page URL Ad Format Info HKD (1,392,824行)
ALTER TABLE page_url_ad_format_info_hkd
    ADD COLUMN IF NOT EXISTS account  varchar NOT NULL DEFAULT 'unknown',
    ADD COLUMN IF NOT EXISTS platform varchar NOT NULL DEFAULT 'adsense';

-- URL Channel Ad Format Info HKD (554,685行)
ALTER TABLE url_channel_ad_format_info_hkd
    ADD COLUMN IF NOT EXISTS account  varchar NOT NULL DEFAULT 'unknown',
    ADD COLUMN IF NOT EXISTS platform varchar NOT NULL DEFAULT 'adsense';

-- Channel Countries HKD (8,641,733行) - 注意：这是中等大小的表
ALTER TABLE channel_countries_hkd
    ADD COLUMN IF NOT EXISTS account  varchar NOT NULL DEFAULT 'unknown',
    ADD COLUMN IF NOT EXISTS platform varchar NOT NULL DEFAULT 'adsense';

-- ========================================
-- 第三部分：中等表（100万-1000万行，建议在低峰期执行）
-- ========================================

-- Site Ad Formats (1,795,686行)
ALTER TABLE site_ad_formats
    ADD COLUMN IF NOT EXISTS account  varchar NOT NULL DEFAULT 'unknown',
    ADD COLUMN IF NOT EXISTS platform varchar NOT NULL DEFAULT 'adsense';

-- Page URL Ad Formats (4,927,748行)
ALTER TABLE page_url_ad_formats
    ADD COLUMN IF NOT EXISTS account  varchar NOT NULL DEFAULT 'unknown',
    ADD COLUMN IF NOT EXISTS platform varchar NOT NULL DEFAULT 'adsense';

-- Page URLs (4,342,542行)
ALTER TABLE page_urls
    ADD COLUMN IF NOT EXISTS account  varchar NOT NULL DEFAULT 'unknown',
    ADD COLUMN IF NOT EXISTS platform varchar NOT NULL DEFAULT 'adsense';

-- URL Channel Ad Formats (3,533,869行)
ALTER TABLE url_channel_ad_formats
    ADD COLUMN IF NOT EXISTS account  varchar NOT NULL DEFAULT 'unknown',
    ADD COLUMN IF NOT EXISTS platform varchar NOT NULL DEFAULT 'adsense';

-- Page URL Infos (4,347,064行)
ALTER TABLE page_url_infos
    ADD COLUMN IF NOT EXISTS account  varchar NOT NULL DEFAULT 'unknown',
    ADD COLUMN IF NOT EXISTS platform varchar NOT NULL DEFAULT 'adsense';

-- Page URL Ad Format Infos (4,925,054行)
ALTER TABLE page_url_ad_format_infos
    ADD COLUMN IF NOT EXISTS account  varchar NOT NULL DEFAULT 'unknown',
    ADD COLUMN IF NOT EXISTS platform varchar NOT NULL DEFAULT 'adsense';

-- URL Channel Ad Format Infos (3,549,004行)
ALTER TABLE url_channel_ad_format_infos
    ADD COLUMN IF NOT EXISTS account  varchar NOT NULL DEFAULT 'unknown',
    ADD COLUMN IF NOT EXISTS platform varchar NOT NULL DEFAULT 'adsense';

-- ========================================
-- 第四部分：大表（>1000万行，需要特殊处理）
-- ========================================
-- 警告：以下表的行数超过1000万，ALTER TABLE操作可能会很慢
-- 建议使用在线DDL工具（如pt-online-schema-change）或分批处理

-- URL Channel Country Ad Formats (49,728,014行) - 最大的表
-- 建议使用在线DDL工具或者在维护窗口期间执行
-- ALTER TABLE url_channel_country_ad_formats
-- ADD COLUMN IF NOT EXISTS account varchar NOT NULL DEFAULT 'unknown',
-- ADD COLUMN IF NOT EXISTS platform varchar NOT NULL DEFAULT 'adsense';

-- Channel Countries (24,732,285行)
-- ALTER TABLE channel_countries
-- ADD COLUMN IF NOT EXISTS account varchar NOT NULL DEFAULT 'unknown',
-- ADD COLUMN IF NOT EXISTS platform varchar NOT NULL DEFAULT 'adsense';

-- URL Channels (17,960,567行)
-- ALTER TABLE url_channels
-- ADD COLUMN IF NOT EXISTS account varchar NOT NULL DEFAULT 'unknown',
-- ADD COLUMN IF NOT EXISTS platform varchar NOT NULL DEFAULT 'adsense';

-- URL Channel Infos (16,568,188行)
-- ALTER TABLE url_channel_infos
-- ADD COLUMN IF NOT EXISTS account varchar NOT NULL DEFAULT 'unknown',
-- ADD COLUMN IF NOT EXISTS platform varchar NOT NULL DEFAULT 'adsense';

-- Site Countries (12,979,936行)
-- ALTER TABLE site_countries
-- ADD COLUMN IF NOT EXISTS account varchar NOT NULL DEFAULT 'unknown',
-- ADD COLUMN IF NOT EXISTS platform varchar NOT NULL DEFAULT 'adsense';

-- ========================================
-- 第五部分：创建索引（在所有字段添加完成后执行）
-- ========================================

-- 分区表索引（主表为空，创建速度快）
CREATE INDEX IF NOT EXISTS idx_url_channel_country_account ON url_channel_country_ad_format_info_partitions (account);
CREATE INDEX IF NOT EXISTS idx_url_channel_country_platform ON url_channel_country_ad_format_info_partitions (platform);

CREATE INDEX IF NOT EXISTS idx_site_country_account ON site_country_ad_format_partitions (account);
CREATE INDEX IF NOT EXISTS idx_site_country_platform ON site_country_ad_format_partitions (platform);

CREATE INDEX IF NOT EXISTS idx_channel_country_account ON channel_country_ad_format_partitions (account);
CREATE INDEX IF NOT EXISTS idx_channel_country_platform ON channel_country_ad_format_partitions (platform);

CREATE INDEX IF NOT EXISTS idx_game_country_account ON game_country_partitions (account);
CREATE INDEX IF NOT EXISTS idx_game_country_platform ON game_country_partitions (platform);

CREATE INDEX IF NOT EXISTS idx_site_adformat_history_account ON site_ad_format_histories (account);
CREATE INDEX IF NOT EXISTS idx_site_adformat_history_platform ON site_ad_format_histories (platform);

-- 小表索引
CREATE INDEX IF NOT EXISTS idx_site_adunit_account ON site_ad_units (account);
CREATE INDEX IF NOT EXISTS idx_site_adunit_platform ON site_ad_units (platform);

CREATE INDEX IF NOT EXISTS idx_custom_channels_account ON custom_channels (account);
CREATE INDEX IF NOT EXISTS idx_custom_channels_platform ON custom_channels (platform);

CREATE INDEX IF NOT EXISTS idx_channels_account ON channels (account);
CREATE INDEX IF NOT EXISTS idx_channels_platform ON channels (platform);

CREATE INDEX IF NOT EXISTS idx_games_account ON games (account);
CREATE INDEX IF NOT EXISTS idx_games_platform ON games (platform);

CREATE INDEX IF NOT EXISTS idx_sites_account ON sites (account);
CREATE INDEX IF NOT EXISTS idx_sites_platform ON sites (platform);

CREATE INDEX IF NOT EXISTS idx_site_hkd_account ON site_hkd (account);
CREATE INDEX IF NOT EXISTS idx_site_hkd_platform ON site_hkd (platform);

CREATE INDEX IF NOT EXISTS idx_channel_hkd_account ON channel_hkd (account);
CREATE INDEX IF NOT EXISTS idx_channel_hkd_platform ON channel_hkd (platform);

CREATE INDEX IF NOT EXISTS idx_game_hkd_account ON game_hkd (account);
CREATE INDEX IF NOT EXISTS idx_game_hkd_platform ON game_hkd (platform);

-- 中等表索引（建议在低峰期创建）
CREATE INDEX IF NOT EXISTS idx_site_adformat_account ON site_ad_formats (account);
CREATE INDEX IF NOT EXISTS idx_site_adformat_platform ON site_ad_formats (platform);

CREATE INDEX IF NOT EXISTS idx_page_url_adformat_account ON page_url_ad_formats (account);
CREATE INDEX IF NOT EXISTS idx_page_url_adformat_platform ON page_url_ad_formats (platform);

CREATE INDEX IF NOT EXISTS idx_page_urls_account ON page_urls (account);
CREATE INDEX IF NOT EXISTS idx_page_urls_platform ON page_urls (platform);

-- ========================================
-- 执行说明和建议
-- ========================================

-- 执行顺序建议：
-- 1. 先执行分区表（主表）的ALTER TABLE，速度很快
-- 2. 再执行小表的ALTER TABLE
-- 3. 在低峰期执行中等表的ALTER TABLE
-- 4. 大表需要特殊处理，建议：
--    a) 使用pt-online-schema-change等在线DDL工具
--    b) 或者在维护窗口期间执行
--    c) 或者考虑分批处理
-- 5. 最后创建索引

-- 监控建议：
-- 1. 执行前检查磁盘空间（ALTER TABLE可能需要额外空间）
-- 2. 监控数据库负载
-- 3. 对于大表，考虑先在从库执行测试

-- 回滚计划：
-- 如果需要回滚，可以使用以下语句删除字段：
-- ALTER TABLE table_name DROP COLUMN IF EXISTS account;
-- ALTER TABLE table_name DROP COLUMN IF EXISTS platform;
