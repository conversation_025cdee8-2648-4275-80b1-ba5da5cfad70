-- 创建包含 account 和 platform 字段的唯一索引
-- 这些索引确保在多账户多平台环境下数据的唯一性
-- 执行前请确保已经添加了 account 和 platform 字段

-- ========================================
-- 第一部分：分区表唯一索引（主表，行数为0，可以快速执行）
-- ========================================

-- 1. URL Channel Country Ad Format Info Partitions
-- 唯一约束：date + url_channel + ad_format + country + account + platform
DROP INDEX IF EXISTS url_channel_country_ad_format_info_partitions__a;
CREATE UNIQUE INDEX url_channel_country_ad_format_info_partitions_unique
    ON url_channel_country_ad_format_info_partitions (date, url_channel, ad_format, country, account, platform);

-- 2. Site Country Ad Format Partitions  
-- 唯一约束：date + site + ad_format + country + account + platform
DROP INDEX IF EXISTS site_country_ad_format_partitions_date_site_ad_format_country;
CREATE UNIQUE INDEX site_country_ad_format_partitions_unique
    ON site_country_ad_format_partitions (date, site, ad_format, country, account, platform);

-- 3. Channel Country Ad Format Partitions
-- 唯一约束：date + channel + channel_id + country + ad_format + account + platform
DROP INDEX IF EXISTS channel_country_ad_format_partitions_date_channel_channe_id_country_ad_format;
CREATE UNIQUE INDEX channel_country_ad_format_partitions_unique
    ON channel_country_ad_format_partitions (date, channel, channel_id, country, ad_format, account, platform);

-- 4. Game Country Partitions
-- 唯一约束：date + channel + channel_id + country + account + platform
DROP INDEX IF EXISTS game_country_partitions_date_channel_channe_id_country;
CREATE UNIQUE INDEX game_country_partitions_unique
    ON game_country_partitions (date, channel, channel_id, country, account, platform);

-- 5. Site Ad Format Histories
-- 唯一约束：site + date + ad_format + collected_at + account + platform
DROP INDEX IF EXISTS idx_unique_site_adformat_history;
CREATE UNIQUE INDEX site_ad_format_histories_unique
    ON site_ad_format_histories (site, date, ad_format, collected_at, account, platform);

-- ========================================
-- 第二部分：小表唯一索引（<100万行，可以快速执行）
-- ========================================

-- Sites (547,463行)
-- 唯一约束：date + site + account + platform
CREATE UNIQUE INDEX IF NOT EXISTS sites_unique
    ON sites (date, site, account, platform);

-- Channels (388,156行)
-- 唯一约束：date + channel + channel_id + account + platform
CREATE UNIQUE INDEX IF NOT EXISTS channels_unique
    ON channels (date, channel, channel_id, account, platform);

-- Games (371,842行)
-- 唯一约束：date + channel + channel_id + account + platform
CREATE UNIQUE INDEX IF NOT EXISTS games_unique
    ON games (date, channel, channel_id, account, platform);

-- Site Ad Units (1,688行)
-- 唯一约束：site + ad_unit_id + account + platform
CREATE UNIQUE INDEX IF NOT EXISTS site_ad_units_unique
    ON site_ad_units (site, ad_unit_id, account, platform);

-- Custom Channels (922行)
-- 唯一约束：channel_id + account + platform
CREATE UNIQUE INDEX IF NOT EXISTS custom_channels_unique
    ON custom_channels (channel_id, account, platform);

-- Site HKD (205,959行)
-- 唯一约束：date + site + account + platform
CREATE UNIQUE INDEX IF NOT EXISTS site_hkd_unique
    ON site_hkd (date, site, account, platform);

-- Channel HKD (134,594行)
-- 唯一约束：date + channel + channel_id + account + platform
CREATE UNIQUE INDEX IF NOT EXISTS channel_hkd_unique
    ON channel_hkd (date, channel, channel_id, account, platform);

-- Game HKD (134,231行)
-- 唯一约束：date + channel + channel_id + account + platform
CREATE UNIQUE INDEX IF NOT EXISTS game_hkd_unique
    ON game_hkd (date, channel, channel_id, account, platform);

-- Channel Ad Formats (1,567,442行)
-- 唯一约束：date + channel + ad_format + account + platform
CREATE UNIQUE INDEX IF NOT EXISTS channel_ad_formats_unique
    ON channel_ad_formats (date, channel, ad_format, account, platform);

-- Channel Ad Format HKD (545,064行)
-- 唯一约束：date + channel + ad_format + account + platform
CREATE UNIQUE INDEX IF NOT EXISTS channel_ad_format_hkd_unique
    ON channel_ad_format_hkd (date, channel, ad_format, account, platform);

-- ========================================
-- 第三部分：中等表唯一索引（100万-1000万行，建议在低峰期执行）
-- ========================================

-- Site Ad Formats (1,795,686行)
-- 唯一约束：date + site + ad_format + account + platform
CREATE UNIQUE INDEX IF NOT EXISTS site_ad_formats_unique
    ON site_ad_formats (date, site, ad_format, account, platform);

-- Page URLs (4,342,542行)
-- 唯一约束：date + page_url + account + platform
CREATE UNIQUE INDEX IF NOT EXISTS page_urls_unique
    ON page_urls (date, page_url, account, platform);

-- Page URL Ad Formats (4,927,748行)
-- 唯一约束：date + page_url + ad_format + account + platform
CREATE UNIQUE INDEX IF NOT EXISTS page_url_ad_formats_unique
    ON page_url_ad_formats (date, page_url, ad_format, account, platform);

-- URL Channel Ad Formats (3,533,869行)
-- 唯一约束：date + url_channel + ad_format + account + platform
CREATE UNIQUE INDEX IF NOT EXISTS url_channel_ad_formats_unique
    ON url_channel_ad_formats (date, url_channel, ad_format, account, platform);

-- Page URL Infos (4,347,064行)
-- 唯一约束：date + page_url + account + platform
CREATE UNIQUE INDEX IF NOT EXISTS page_url_infos_unique
    ON page_url_infos (date, page_url, account, platform);

-- Page URL Ad Format Infos (4,925,054行)
-- 唯一约束：date + page_url + ad_format + account + platform
CREATE UNIQUE INDEX IF NOT EXISTS page_url_ad_format_infos_unique
    ON page_url_ad_format_infos (date, page_url, ad_format, account, platform);

-- URL Channel Ad Format Infos (3,549,004行)
-- 唯一约束：date + url_channel + ad_format + account + platform
CREATE UNIQUE INDEX IF NOT EXISTS url_channel_ad_format_infos_unique
    ON url_channel_ad_format_infos (date, url_channel, ad_format, account, platform);

-- Site Country HKD (3,862,029行)
-- 唯一约束：date + site + country + account + platform
CREATE UNIQUE INDEX IF NOT EXISTS site_country_hkd_unique
    ON site_country_hkd (date, site, country, account, platform);

-- URL Channel Info HKD (2,344,111行)
-- 唯一约束：date + url_channel + account + platform
CREATE UNIQUE INDEX IF NOT EXISTS url_channel_info_hkd_unique
    ON url_channel_info_hkd (date, url_channel, account, platform);

-- Channel Countries HKD (8,641,733行)
-- 唯一约束：date + channel + channel_id + country + account + platform
CREATE UNIQUE INDEX IF NOT EXISTS channel_countries_hkd_unique
    ON channel_countries_hkd (date, channel, channel_id, country, account, platform);

-- ========================================
-- 第四部分：大表唯一索引（>1000万行，需要特殊处理）
-- ========================================
-- 警告：以下表的行数超过1000万，CREATE INDEX操作可能会很慢
-- 建议使用在线DDL工具或者在维护窗口期间执行

-- URL Channel Country Ad Formats (49,728,014行) - 最大的表
-- 唯一约束：date + url_channel + country + ad_format + account + platform
-- CREATE UNIQUE INDEX IF NOT EXISTS url_channel_country_ad_formats_unique
-- ON url_channel_country_ad_formats(date, url_channel, country, ad_format, account, platform);

-- Channel Countries (24,732,285行)
-- 唯一约束：date + channel + channel_id + country + account + platform
-- CREATE UNIQUE INDEX IF NOT EXISTS channel_countries_unique
-- ON channel_countries(date, channel, channel_id, country, account, platform);

-- URL Channels (17,960,567行)
-- 唯一约束：date + url_channel + country + account + platform
-- CREATE UNIQUE INDEX IF NOT EXISTS url_channels_unique
-- ON url_channels(date, url_channel, country, account, platform);

-- URL Channel Infos (16,568,188行)
-- 唯一约束：date + url_channel + account + platform
-- CREATE UNIQUE INDEX IF NOT EXISTS url_channel_infos_unique
-- ON url_channel_infos(date, url_channel, account, platform);

-- Site Countries (12,979,936行)
-- 唯一约束：date + site + country + account + platform
-- CREATE UNIQUE INDEX IF NOT EXISTS site_countries_unique
-- ON site_countries(date, site, country, account, platform);

-- ========================================
-- 执行说明和建议
-- ========================================

-- 执行顺序建议：
-- 1. 先执行分区表（主表）的唯一索引，速度很快
-- 2. 再执行小表的唯一索引
-- 3. 在低峰期执行中等表的唯一索引
-- 4. 大表需要特殊处理，建议：
--    a) 使用 CREATE INDEX CONCURRENTLY（PostgreSQL）
--    b) 或者在维护窗口期间执行
--    c) 或者考虑分批处理

-- 监控建议：
-- 1. 执行前检查磁盘空间（CREATE INDEX可能需要额外空间）
-- 2. 监控数据库负载和锁等待
-- 3. 对于大表，考虑先在从库执行测试

-- 回滚计划：
-- 如果需要回滚，可以使用以下语句删除索引：
-- DROP INDEX IF EXISTS index_name;

-- 注意事项：
-- 1. 这些唯一索引将替换原有的唯一索引
-- 2. 确保在执行前数据中没有重复记录
-- 3. 建议在执行前备份数据库
-- 4. 对于生产环境，建议分批执行，避免长时间锁表
