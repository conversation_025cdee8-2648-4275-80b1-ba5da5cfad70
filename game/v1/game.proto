syntax = "proto3";

package api.game.v1;

import "google/protobuf/empty.proto";

option go_package = "minigame.vip/minicloud/game-service/api/game/v1;v1";
option java_multiple_files = true;
option java_package = "api.game";


service Game {
  rpc SyncGameData (SyncGameDataRequest) returns (.google.protobuf.Empty) {}
};



message SyncGameDataRequest {
  string app_id = 1 [json_name = "app_id"];
  string display_name = 2 [json_name = "display_name"];
  string name = 3;
  string game_type = 4 [json_name = "game_type"];
  repeated uint32 game_tags = 5 [json_name = "game_tags"];
  bool landscape = 6;
  string icon = 7;
  string big_icon = 8[json_name = "big_icon"];
  string banner = 9;
  string flash = 10;
  uint32 engine = 11;
  uint32 engine_version = 12 [json_name = "engine_version"];
  string engine_other = 13 [json_name = "engine_other"];
  string package = 14;
  string package_name = 15 [json_name = "package_name"];
  string game_desc_from_cp = 16 [json_name = "game_desc_from_cp"];
  double  package_size = 17 [json_name = "package_size"];
  uint32 game_package_type = 18 [json_name = "game_package_type"];
}
