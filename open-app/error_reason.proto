syntax = "proto3";

package game.v1;

import "errors/errors.proto";

option go_package = "open-proto/open-admin-user/api/extern;extern";

enum ErrorReason {
  option (errors.default_code) = 500;

  UNKNOWN_ERROR = 0;

  // GAME 相关 [3000,4000)
  EXIST_APP_ID = 3000[(errors.code) = 400]; //已存在相同AppId
  EXIST_NAME_EN = 3001[(errors.code) = 400];//已存在相同NameEn
  EXIST_NAME = 3002[(errors.code) = 400];//已存在相同Name
  EXIST_GAME_VERSION = 3003[(errors.code) = 400]; // 已存在相同游戏版本
  NOT_EXIST_GAME_VERSION = 3004[(errors.code) = 400]; // 不存在相同游戏版本
  EXIST_GAME_UN_PUBLISH = 3005[(errors.code) = 400]; //已存在未发布的游戏
  NOT_EXIST_GAME = 3006[(errors.code) = 400]; // 游戏不存在
  GAME_INSPECTING = 3007[(errors.code) = 400]; // 游戏版本审核中
  GAME_HAVE_PUBLISH = 3008[(errors.code) = 400]; // 游戏版本已发布
  GAME_NOT_UN_PUBLISH = 3009[(errors.code) = 400]; // 游戏版本未下架
  NOT_EXIST_GAME_INSPECT = 3012[(errors.code) = 400]; // 游戏审核不存在
  GAME_INSPECT_STATES_CHANGE = 3011[(errors.code) = 400]; // 游戏审核状态发生变化
  GAME_VERSION_STATES_CHANGE = 3013[(errors.code) = 400]; // 游戏版本状态发生变化
  GAME_STATES_CHANGE = 3014[(errors.code) = 400]; // 游戏状态发生变化
  GAME_INSPECTING_UNPUBLISH_FORBIDDEN = 3015[(errors.code) = 400]; //存在审核中版本，无法下架
  GAME_EN_NAME_INVALID = 3016[(errors.code) = 400]; // 游戏英文名不符合规范
}