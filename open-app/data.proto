syntax = "proto3";

package game.v1;

import "validate/validate.proto";
import "google/protobuf/timestamp.proto";
import "google/protobuf/empty.proto";
import "google/protobuf/struct.proto";
import "open-proto/open-common/data.proto";
option go_package = "open-proto/open-app/api/extern;extern";


service DataService {
  rpc GetCpGeneralOverviewData(GetCpGeneralOverviewDataRequest) returns (GetCpGeneralOverviewDataReply) {}
  rpc GetCpFilterData(GetCpFilterDataRequest) returns (GetCpFilterDataReply){}
  rpc GetCpDimensionFilterList(.google.protobuf.Empty) returns (GetCpDimensionFilterListReply){}
  rpc GetCpData(GetCpDataRequest) returns (GetCpDataReply){}
  rpc ExportCpData(ExportCpDataRequest) returns (ExportCpDataReply){}
}

message ExportCpDataRequest {
  repeated string  dimensions = 1;
  repeated string metrics = 2;
  string filters = 3;
  Date start_date = 4;
  Date end_date = 5;
  repeated string order = 6;
  string merchant_name = 7 [deprecated = true];
  string language = 8;
  int64 data_cp_id = 9;
}

message ExportCpDataReply {
  bytes data = 1;
  string file_name = 2;
}

message Date {
  int32 day = 1;
  int32 year = 2;
  int32 month = 3;
}

message GetCpDataRequest {
  repeated string dimensions = 1;
  repeated string metrics = 2;
  string filters = 3;
  int32 page_size = 4;
  int32 page = 5;
  Date start_date = 6;
  Date end_date = 7;
  repeated string order = 8;
  bool is_contrast = 9;
  Date contrast_start_date = 10;
  Date contrast_end_date = 11;
  repeated string filter_fields = 12;
  string merchant_name = 13;
  int64 data_cp_id = 14;
}

message GetCpDataReply {
    int32     total = 1;
    repeated .google.protobuf.Struct rows = 2;
    repeated .google.protobuf.Struct totals = 3;
    repeated .google.protobuf.Struct charts = 4;
}

message GetCpDimensionFilterListReply {
  message DimensionFilter {
    repeated string dimensions = 1;
    repeated string filters = 2;
  }
  repeated DimensionFilter dimension_filters = 2;
}

message GetCpFilterDataRequest {
  string name = 1;
  string field = 2;
}

message GetCpFilterDataReply {
  repeated string data = 1;
}

message GetCpGeneralOverviewDataRequest {
  string name = 1 [deprecated = true];
  uint64 merchant_id = 2;
  int64 data_cp_id = 3;
}

message GetCpGeneralOverviewDataReply {
  // 游戏数量
  int32 game_count = 1;
  // 预估收入
  double estimated_earnings = 2;
  // 游戏次数
  int64 page_views = 3[deprecated = true];
  // 广告展示次数
  int64 impressions = 4[deprecated = true];
  // ECPM
  double impressions_rpm = 5;
  .google.protobuf.Timestamp start_time = 6;
  .google.protobuf.Timestamp end_time = 7;
  repeated .data.v1.MonthData estimated_earning_month = 8;
  repeated .data.v1.MonthData impressions_rpm_month = 9;
  double estimated_earning_mom = 10;
  double impressions_rpm_mom = 11;
  uint32 pass_count = 12;
  double total_estimated_earnings = 13;
}