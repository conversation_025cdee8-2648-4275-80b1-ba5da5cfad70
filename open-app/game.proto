syntax = "proto3";

package game.v1;

import "open-proto/open-common/game.proto";
import "validate/validate.proto";
import "google/protobuf/timestamp.proto";
import "google/protobuf/empty.proto";

option go_package = "open-proto/open-user/api/extern;extern";


service GameService {
  // 应用/游戏动态
  rpc GameDynamics(GameDynamicsRequest) returns (GameDynamicsReply) {
  }
  // 游戏列表
  rpc SearchGames(SearchGamesRequest) returns (SearchGamesReply) {
  }
  // 游戏版本版本
  rpc GameVersionList(GameVersionListRequest) returns (GameVersionListReply) {
  }
  // 创建游戏
  rpc CreateGame(CreateGameRequest) returns (CreateGameReply) {
  }
  // 更新游戏（保存草稿、上一步、下一步）
  rpc UpdateGame(UpdateGameRequest) returns (UpdateGameReply) {
  }
  // 生成游戏AppID
  rpc GenGameAppID(GenGameAppIDRequest) returns (GenGameAppIDReply) {
  }
  // 获取游戏版本号
  rpc GenGameVersion(GenGameVersionRequest) returns (GenGameVersionReply) {
  }
  // 提交审核
  rpc SubmitInspect(SubmitInspectRequest) returns (SubmitInspectReply) {
  }
  rpc WithdrawInspect(WithdrawInspectRequest) returns (WithdrawInspectReply) {
  }
  // 新增版本
  rpc CreateGameVersion(CreateGameVersionRequest) returns (CreateGameVersionReply) {
  }
  // 版本详情
  rpc GetGameVersion(GetGameVersionRequest) returns (GetGameVersionReply) {
  }
  // 游戏上架
  rpc PublishGameVersion(PublishGameVersionRequest) returns (PublishGameVersionReply) {
  }
  // 游戏下架
  rpc UnpublishGameVersion(UnpublishGameVersionRequest) returns (UnpublishGameVersionReply) {
  }
  // 删除游戏
  rpc DeleteGameVersion (DeleteGameRequest) returns (DeleteGameReply) {
  };


}


// 游戏版本列表请求
message GameVersionListRequest{
  // 游戏ID
  uint64 id = 1;
  // 上架/下架状态（0.未发布;1.上架;2.下架）)
  optional uint32 publish_state = 2;
}
// 游戏版本列表返回
message GameVersionListReply{
  // 游戏列表
  game.v1.GameAll latest = 1;
  // 游戏版本号列表
  repeated game.v1.GameOnlyVersion data = 2;
}

// 游戏创建请求
message CreateGameRequest {
  // 游戏名称
  string name = 1;
  // 游戏英文名称
  string name_en = 2;
  // 游戏类型
  uint32 type = 3;
  // 屏幕类型 0.未知; 1.竖屏; 2.横屏
  uint32 landscape = 4;
  // 游戏版本字符串标识
  string version = 5;
  // 游戏图标
  optional string icon = 6;
  // 游戏的应用ID，全局唯一
  string app_id = 7;
  // 商户ID去除，改为通过uid查询商户ID
  // uint64 merchant_id = 8;
  // 标签
  repeated uint32 tag = 9;
  // 游戏包类型 1.正式包 2.测试包
  uint32 game_package_type = 10;
  // 创建者uid，服务端微服务间使用，前端不指定
  uint64 uid = 11;
}
// 游戏创建响应
message CreateGameReply {
  // 游戏信息
  optional game.v1.GameInfo game = 1;
  // 游戏版本信息
  optional game.v1.GameVersionInfo game_version = 2;
}

// 游戏更新请求
message UpdateGameRequest {
  // 创建者uid，服务端微服务间使用，前端不指定
  uint64  uid = 1;
  // 游戏版本的唯一标识
  uint64 id = 2;
  // 游戏信息
  optional game.v1.GameVersionInfo game_version = 3;
  // 操作(0 未知 、1 上一步、2 保存草稿、3 下一步)
  optional uint32 operate = 4;
}
// 游戏更新响应
message UpdateGameReply {
  // 版本审核进度(0.未知;1.基础信息;2.游戏资料;3.接入SDK;4.申请审核;5.上架入库)
  optional uint32 progress = 1;
  // 版本当前审核进度(0.未知;1.基础信息;2.游戏资料;3.接入SDK;4.申请审核;5.上架入库)
  optional uint32 progress_current = 2;
}

// 生成游戏AppID请求
message GenGameAppIDRequest {
  // 游戏英文名称
  optional string name_en = 1;
}
// 生成游戏AppID响应
message GenGameAppIDReply {
  // 游戏的应用ID，全局唯一
  string app_id = 1;
}

// 获得游戏版本号请求
message GenGameVersionRequest {
  // 游戏的唯一标识(1创建: 不传 ；2更新版本： 传game-id)
  //  optional uint64 game_id = 1;
}
// 获得游戏版本号响应
message GenGameVersionReply {
  // 游戏版本字符串标识
  string version = 2;
}

// 提交审核请求
message SubmitInspectRequest {
  // 创建者uid，服务端微服务间使用，前端不指定
  uint64 uid = 1;
  // 游戏版本ID
  uint64 game_version_id = 2;
  // 商户ID去除，改为通过uid查询商户ID
  // uint64 merchant_id = 3;
  // 游戏的唯一标识
  uint64 game_id = 4;
}
// 提交审核响应
message SubmitInspectReply {
  // 游戏版本信息
  optional game.v1.GameVersionInfo game_version = 1;
  // 游戏审核信息
  optional game.v1.GameInspectInfo game_inspect = 2;
}


// 应用/游戏动态请求
message GameDynamicsRequest {
  // 商户ID去除，改为通过uid查询商户ID
  // uint64 merchant_id = 1;

  // 创建者uid，服务端微服务间使用，前端不指定
  uint64 uid = 1;
}
// 应用/游戏动态返回
message GameDynamicsReply {
  // 运营中
  uint32 pass_count = 1;
  // 待上架
  uint32 unpublish_count = 2;
  // 游戏版本信息
  repeated game.v1.GameVersionBaseInfo data = 3;
}

// 游戏列表请求
message SearchGamesRequest {
  // 发布状态（0.全部;1.未发布;2.已发布;）
  uint32 state = 1;
  //页码
  int32 page = 2;
  //每页条数
  int32 page_size = 3;
  // 商户ID去除，改为通过uid查询商户ID
  // uint64 merchant_id = 6;

  // 创建者uid，服务端微服务间使用，前端不指定
  uint64 uid = 4;
}
// 游戏列表返回
message SearchGamesReply {
  // 游戏总数
  uint32 total_count = 1;
  // 运营中
  uint32 pass_count = 2;
  // 待上架
  uint32 unpublish_count = 3;
  // 游戏版本信息
  repeated game.v1.GameVersionBaseInfo data = 4;
  //页码
  int32 page = 5;
  //每页条数
  int32 page_size = 6;
  //总条数
  int32 total = 7;
}


message GameFilter {
  //
  optional uint32 state = 1;
}


// 更新游戏包请求
message UpdateGamePackageRequest {
  // 创建者uid，服务端微服务间使用，前端不指定
  uint64 uid = 1;
  // 游戏版本的唯一标识
  uint64 game_version_id = 2;
  // 游戏包
  optional string package = 3;
  // 游戏包名
  optional string package_name = 4;
  // 游戏包大小
  optional uint64 package_size = 5;
}
// 游戏更新响应
message UpdateGamePackageReply {
  // 游戏包名
  optional string package_name = 4;
  // 游戏包大小
  optional string package_size = 5;
}


// 撤回审核请求
message WithdrawInspectRequest {
  // 创建者uid，服务端微服务间使用，前端不指定
  uint64 uid = 1;
  //游戏审核ID
  uint64 id = 2;
}
// 撤回审核响应
message WithdrawInspectReply {
  // 游戏审核信息
  optional game.v1.GameInspectInfo game_inspect = 1;
  // 游戏审核信息
  optional game.v1.GameVersionInfo game_version = 2;
}


// 游戏版本创建请求
message CreateGameVersionRequest {
  // 游戏的唯一标识
  uint64 game_id = 1 ;
  // 是否带入上一个已发布的版本信息
  bool copy_enable = 2;
}
// 游戏版本创建响应
message CreateGameVersionReply {
  // 游戏信息
  optional game.v1.GameInfo game = 1;
  // 游戏版本信息
  optional game.v1.GameVersionInfo game_version = 2;
}


// 游戏版本详情请求
message GetGameVersionRequest{
  // 游戏版本ID
  uint64 id = 1;
}
// 游戏版本详情返回
message GetGameVersionReply{
  // 游戏版本信息
  game.v1.GameVersionInfo game_version = 1;
  // 游戏审核信息
  game.v1.GameInspectInfo game_inspect = 2;
}


// 游戏版本上架请求
message PublishGameVersionRequest {
  // 创建者uid，服务端微服务间使用，前端不指定
  uint64 uid = 1;
  // 游戏版本的唯一标识
  uint64 id = 2;
}
// 游戏版本上架响应
message PublishGameVersionReply {
  // 游戏版本信息
  optional game.v1.GameVersionInfo game_version = 1;
  // 游戏审核信息
  optional game.v1.GameInspectInfo game_inspect = 2;
}

// 游戏版本下架请求
message UnpublishGameVersionRequest {
  // 创建者uid，服务端微服务间使用，前端不指定
  uint64 uid = 1;
  // 游戏版本的唯一标识
  uint64 id = 2;
}
// 游戏版本下架响应
message UnpublishGameVersionReply {
  // 游戏版本信息
  optional game.v1.GameVersionInfo game_version = 1;
  // 游戏审核信息
  optional game.v1.GameInspectInfo game_inspect = 2;
}

// 游戏版本删除请求
message DeleteGameRequest {
  // 游戏的唯一标识
  uint64 game_id = 1;
}
// 游戏版本删除响应
message DeleteGameReply {
}