package audit

import (
	"fmt"
	"github.com/golang-jwt/jwt/v5"
	"testing"
)

func TestAuditParse(t *testing.T) {
	var jwtSecret = []byte("minicloud")
	token := "eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJ1aWQiOiJlNjU3OTBjZS04MmZiLTQwYzItOTMwMi1iNzAwOGZiMzFhMDQiLCJyb2xlIjoic3VwZXJfYWRtaW4iLCJleHAiOjE3MzE5MzY3NjN9.ak5w8MNBou-5weY6Q165ysKd57Qc_lLzA1YgceVllFU"
	var claim jwt.MapClaims
	tokenClaims, err := jwt.ParseWithClaims(token, &claim, func(token *jwt.Token) (interface{}, error) {
		return jwtSecret, nil
	})
	if err != nil {
		panic(err)
	}
	fmt.Println(tokenClaims.Claims.(jwt.MapClaims))
}
