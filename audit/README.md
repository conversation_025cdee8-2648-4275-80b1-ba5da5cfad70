# 审计中间件接入

##
* 引入miniutils子模块
* Makfile文件的api模块新增：
```bash
protoc --proto_path=./extern \
      --proto_path=./third_party \
        --go_out=paths=source_relative:./extern \
        --go-http_out=paths=source_relative:./extern \
        --go-grpc_out=paths=source_relative:./extern \
        --go-errors_out=paths=source_relative:./extern \
        --validate_out=paths=source_relative,lang=go:./extern \
        extern/miniutils/audit/audit.proto
```
* `internal/server/http.go` 引入中间件：
```go
audit.NewAuditMiddleware(auditConf.Addr, auth.ApiKey, logger).
WithTls(auditConf.Tls).
MatchAll(true).
Build(),
```
* `conf.proto` 新增：
```proto
message Audit{
  string addr = 1;
  bool tls = 2;
}
```