package audit

import (
	"context"
	"fmt"
	"github.com/go-kratos/kratos/v2/log"
	"github.com/go-kratos/kratos/v2/middleware"
	"github.com/go-kratos/kratos/v2/middleware/auth/jwt"
	"github.com/go-kratos/kratos/v2/middleware/selector"
	"github.com/go-kratos/kratos/v2/transport"
	"github.com/go-kratos/kratos/v2/transport/grpc"
	"github.com/go-kratos/kratos/v2/transport/http"
	jwtv5 "github.com/golang-jwt/jwt/v5"
	ggrpc "google.golang.org/grpc"
	"strings"
	"time"
)

type audit struct {
	operationSet    map[string]struct{}
	log             *log.Helper
	auditClient     AuditClient
	auditHttpClient AuditHTTPClient
	tls             bool
	addr            string
	timeout         time.Duration
	auditAll        bool
	matchFn         func(context.Context, string) bool
	apiKey          string
	useHttp         bool
	dataSender      func(context.Context, *CreateRequest) error
}

func getGrpcDialer(tls bool) func(ctx context.Context, opts ...grpc.ClientOption) (*ggrpc.ClientConn, error) {
	if tls {
		return grpc.Dial
	}
	return grpc.DialInsecure
}

func NewAuditMiddleware(addr, apiKey string, logger log.Logger) *audit {
	a := &audit{
		addr:         addr,
		tls:          false,
		operationSet: map[string]struct{}{},
		log:          log.NewHelper(logger, log.WithMessageKey("miniutils/audit")),
		apiKey:       apiKey,
		useHttp:      false,
	}
	a.matchFn = a.match
	return a
}

func (a *audit) UseHttp() *audit {
	a.useHttp = true
	return a
}

func (a *audit) MatchAll(all bool) *audit {
	a.auditAll = all
	return a
}

func (a *audit) WithTimeout(timeout time.Duration) *audit {
	a.timeout = timeout
	return a
}

func (a *audit) WithTls(tls bool) *audit {
	a.tls = tls
	return a
}

func (a *audit) sendByHttp(ctx context.Context, req *CreateRequest) error {
	_, err := a.auditHttpClient.Create(ctx, req)
	return err
}

func (a *audit) sendByGrpc(ctx context.Context, req *CreateRequest) error {
	_, err := a.auditClient.Create(ctx, req)
	return err
}

func (a *audit) AddOperation(operations ...string) *audit {
	for _, operation := range operations {
		a.operationSet[operation] = struct{}{}
	}
	return a
}

func (a *audit) useGrpcClient() {
	conn, err := getGrpcDialer(a.tls)(
		context.Background(),
		grpc.WithEndpoint(a.addr),
		grpc.WithTimeout(a.timeout),
	)
	if err != nil {
		panic(err)
	}
	a.auditClient = NewAuditClient(conn)
	a.dataSender = a.sendByGrpc
}

func (a *audit) parseUuidFromHeader(ctx context.Context) (string, bool) {
	if tr, ok := transport.FromServerContext(ctx); ok {
		if ht, ok := tr.(*http.Transport); ok {
			authorization := ht.Request().Header.Get("authorization")
			splits := strings.Split(authorization, " ")
			if len(splits) < 2 {
				return "", false
			}
			token := splits[1]
			var claim jwtv5.MapClaims
			tokenClaims, err := jwtv5.ParseWithClaims(
				token,
				&claim,
				func(token *jwtv5.Token) (interface{}, error) {
					return []byte(a.apiKey), nil
				})
			if err != nil {
				a.log.Error(err)
				return "", false
			}
			if tokenClaims == nil {
				return "", false
			}
			if tokenClaims.Claims == nil {
				return "", false
			}
			if claims, ok := tokenClaims.Claims.(*jwtv5.MapClaims); ok {
				uuid, ok := (*claims)["uid"].(string)
				return uuid, ok
			}
		}
	}
	return "", false
}

func (a *audit) useHttpClient() {
	conn, err := http.NewClient(
		context.Background(),
		http.WithEndpoint(a.addr),
		http.WithTimeout(a.timeout),
	)
	if err != nil {
		panic(err)
	}
	a.auditHttpClient = NewAuditHTTPClient(conn)
	a.dataSender = a.sendByHttp
}

func (a *audit) Build() middleware.Middleware {
	if a.useHttp {
		a.useHttpClient()
	} else {
		a.useGrpcClient()
	}
	if a.auditAll {
		a.matchFn = a.all
	}
	return selector.
		Server(func(handler middleware.Handler) middleware.Handler {
			return func(ctx context.Context, req interface{}) (reply interface{}, err error) {
				defer func() {
					if err := recover(); err != nil {
						a.log.Error(err)
					}
				}()
				var (
					method     string
					path       string
					body       string
					operatorId string
				)
				if tr, ok := transport.FromServerContext(ctx); ok {
					if ht, ok := tr.(*http.Transport); ok {
						method = ht.Request().Method
						path = ht.Request().URL.Path
					}
				}
				if token, ok := jwt.FromContext(ctx); ok {
					if claims, ok := token.(jwtv5.MapClaims); ok {
						if uuid, ok := claims["uid"].(string); ok {
							operatorId = uuid
						}
					}
				} else {
					uuid, ok := a.parseUuidFromHeader(ctx)
					if ok {
						operatorId = uuid
					}
				}
				err = a.dataSender(ctx, &CreateRequest{
					Method:     method,
					Path:       path,
					Body:       fmt.Sprint(req),
					OperatorId: operatorId,
				})
				if err != nil {
					a.log.Errorf("audit err:%v,data:%v", err, []interface{}{method, path, body, operatorId})
				}
				return handler(ctx, req)
			}
		}).
		Match(a.matchFn).
		Build()
}

func (a *audit) match(_ context.Context, operation string) bool {
	_, ok := a.operationSet[operation]
	return ok
}

func (a *audit) all(_ context.Context, _ string) bool {
	return true
}
