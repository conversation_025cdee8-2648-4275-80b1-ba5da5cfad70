syntax = "proto3";

package api.cooperation.v1;

import "google/api/annotations.proto";
import "google/protobuf/empty.proto";
option go_package = "git.minigame.vip/service/minigame-data-api/api/Cooperation/v1;v1";


service CooperationService {
  rpc UpdateCooperation(UpdateCooperationRequest) returns (.google.protobuf.Empty){
    option (google.api.http) = {
      post: "/v1/data/cooperation:update",
      body: "*"
    };
  }
  rpc DeleteCooperation(DeleteCooperationRequest) returns (.google.protobuf.Empty){
    option (google.api.http) = {
      delete: "/v1/data/cooperation:delete",
    };
  }
  rpc ListCooperation(ListCooperationRequest) returns (ListCooperationReply) {
    option (google.api.http) = {
      get: "/v1/data/cooperation:list",
    };
  }
  rpc ListCooperationUrlChannel(ListCooperationUrlChannelRequest)returns (ListCooperationUrlChannelReply) {
    option (google.api.http) = {
      get: "/v1/data/cooperation:url_channel",
    };
  }
  rpc ListCooperationSite(ListCooperationSiteRequest)returns (ListCooperationSiteReply) {
    option (google.api.http) = {
      get: "/v1/data/cooperation:site",
    };
  }
  rpc UpdateCooperationSite(UpdateCooperationSiteUrlChannelRequest) returns (.google.protobuf.Empty) {
    option (google.api.http) = {
      post: "/v1/data/cooperation/cooperation/site:update",
      body: "*"
    };
  }
  rpc UpdateCooperationUrlChannel(UpdateCooperationSiteUrlChannelRequest) returns (.google.protobuf.Empty) {
    option (google.api.http) = {
      post: "/v1/data/cooperation/cooperation/url_channel:update",
      body: "*"
    };
  }
  rpc CreateCooperationSites(CreateCooperationSitesRequest) returns (.google.protobuf.Empty) {
    option (google.api.http) = {
      post: "/v1/data/cooperation/cooperation/site:create",
      body: "*"
    };
  }
  rpc LisCooperationName(.google.protobuf.Empty) returns (LisCooperationNameReply) {
    option (google.api.http) = {
      get: "/v1/data/cooperation/cooperation/name:list",
    };
  }
}

message LisCooperationNameReply {
  repeated string name = 1;
}

message UpdateCooperationSiteUrlChannelRequest {
  int64  id = 1;
  int32 state = 2;
}

message CreateCooperationSitesRequest {
  string name = 1;
  repeated string sites = 2;
  int64 data_channel_id = 3;
}



message ListCooperationUrlChannelReply {
  repeated UrlChannel url_channel = 1;
  int32 total = 2;
  int32 operational_count = 3;
}
message ListCooperationSiteReply {
  repeated Site site = 1;
  int32 total = 2;
  int32 operational_count = 3;
}
message ListCooperationUrlChannelRequest {
  int32 page = 1;
  int32 limit = 2;
  string  filter = 3;
  string name = 4;
}
message ListCooperationSiteRequest {
  int32 page = 1;
  int32 limit = 2;
  string  filter = 3;
  string name = 4;
  optional int64 cooperation_id = 5; // 支持通过cooperation_id过滤查询，可选。如果指定了cooperation_id，则name无效
}
message UrlChannel {
  string url_channel = 1;
  int32 state = 2;
  repeated string manager = 3;
  int64 create_time = 4;
  string name = 5;
  int32 game_count = 6;
  int64 id = 7;
}
message Site {
  string site = 1;
  int32 state = 2;
  repeated string manager = 3;
  int64 create_time = 4;
  string name = 5;
  int32 game_count = 6;
  int64 id = 7;
}
message ListCooperationRequest {
  int32 page = 1;
  int32 limit = 2;
  string  filter = 3;
}

message ListCooperationReply {
  repeated Cooperation cooperation = 1;
  int32 total = 2;
}

message Cooperation {
  int64 id = 1;
  string name = 3;
  int32 site_count = 4;
  int32 url_channel_count = 5;
  int32 operational_state = 7;
  int32 state = 6;
  repeated string manager = 8;
  int32 game_count = 9;
  int64 created_at = 10;
}


message UpdateCooperationRequest {
  int64 id = 1;
  string name = 3;
  int32 operational_state = 2;
  repeated string manager = 4;
  int32 state = 5;
}

message DeleteCooperationRequest {
  int64 id = 1;
}