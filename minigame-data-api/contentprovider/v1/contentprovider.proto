syntax = "proto3";

package api.contentProvider.v1;

import "google/api/annotations.proto";
import "google/protobuf/empty.proto";
import "google/protobuf/struct.proto";
option go_package = "git.minigame.vip/service/minigame-data-api/api/contentProvider/v1;v1";


service ContentProvider {
  rpc SyncCpGames(SyncCpGamesRequest) returns (SyncCpGamesReply) {
    option (google.api.http) = {
      post: "/v1/data/cp/game:sync",
      body: "*"
    };
  }
  rpc SyncCp(SyncCpRequest) returns (SyncCpReply) {
    option (google.api.http) = {
      post: "/v1/data/cp:sync",
      body: "*"
    };
  }
  rpc SyncMerchant (SyncMerchantRequest) returns (SyncMerchantReply) {
    option (google.api.http) = {
      post: "/v1/data/merchant:sync",
      body: "*"
    };
  }
}


message SyncMerchantRequest {
  repeated string managers = 1;
  string name = 3;
  int64  data_cp_id = 2;
  int64  data_channel_id = 4;
}

message SyncMerchantReply {
  int64 data_cp_id = 1;
  int64 data_channel_id = 2;
}

message SyncCpRequest {
  repeated string managers = 1;
  string name = 3;
  int64  data_cp_id = 2;
}

message SyncCpReply {
  int64 data_cp_id = 1;
}

message SyncCpGamesRequest {
  repeated string managers = 1;
  string name = 3;
  int64  data_cp_id = 2;
  string app_id = 4;
  string display_name = 5;
  string icon = 6;
}

message SyncCpGamesReply {
  int64 data_cp_id = 1;
}

