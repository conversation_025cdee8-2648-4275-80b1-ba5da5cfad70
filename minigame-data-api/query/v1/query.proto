syntax = "proto3";

package api.query.v1;

import "google/api/annotations.proto";
import "google/protobuf/struct.proto";
import "google/protobuf/empty.proto";
import "openapi/v3/annotations.proto";

option go_package = "git.minigame.vip/service/minigame-data-api/api/query/v1;v1";


// The DataQuery service definition.
service DataQuery {

  // 游戏管理-数据概览
  rpc CpGeneralOverviewData(GeneralOverviewDataRequest) returns (CpGeneralOverviewDataReply) {
    option (google.api.http) = {
      post: "/v1/data/cp/general/overview",
      body: "*"
    };
  }
  // 开发者平台使用的筛选列表
  //  rpc CpFilterList(CpFilterListRequest) returns (CpFilterListReply) {
  //    option (google.api.http) = {
  //      post: "/v1/data/cp/filter",
  //      body: "*"
  //    };
  //  }
  // 开发者平台使用的细分及可用的筛选字段
  //  rpc CpDimensionFilterList(.google.protobuf.Empty) returns (CpDimensionFilterListReply) {
  //    option (google.api.http) = {
  //      post: "/v1/data/cp/dimension",
  //      body: "*"
  //    };
  //  }
  // 开发者平台cp数据查询
  rpc CpChannelDataQuery(CpChannelDataQueryRequest) returns (CpChannelDataQueryReply) {
    option (google.api.http) = {
      post: "/v1/data/adsense/cp/query",
      body: "*"
    };
  }
  // 渠道平台的一些游戏数据 pv啥的
  rpc GetGameData(.google.protobuf.Empty) returns (GameDataReply) {
    option (google.api.http) = {
      post: "/v1/data/game/data",
      body: "*"
    };
  }

  // 渠道平台使用的筛选列表
  //  rpc BusinessFilterList(BusinessFilterListRequest) returns (BusinessFilterListReply) {
  //    option (google.api.http) = {
  //      post: "/v1/data/business/filter",
  //      body: "*"
  //    };
  //  }
  // 渠道平台使用的细分及可用的筛选字段
  //  rpc BusinessDimensionFilterList(.google.protobuf.Empty) returns (BusinessDimensionFilterListReply) {
  //    option (google.api.http) = {
  //      post: "/v1/data/business/dimension",
  //      body: "*"
  //    };
  //  }

  // 概览-运营概览
  rpc GeneralOverviewData(GeneralOverviewDataRequest) returns (GeneralOverviewDataReply) {
    option (google.api.http) = {
      post: "/v1/data/general/overview",
      body: "*"
    };
  }
  // 渠道数据查询
  rpc BusinessDataQuery(BusinessDataQueryRequest) returns (BusinessDataQueryReply) {
    option (google.api.http) = {
      post: "/v1/data/business/query",
      body: "*"
    };
  }
  // 渠道管理 数据概览
  rpc BusinessGeneralOverviewData(GeneralOverviewDataRequest) returns (BusinessGeneralOverviewDataReply) {
    option (google.api.http) = {
      post: "/v1/data/business/general/overview",
      body: "*"
    };
  }
  // 运营管理-收益汇总
  rpc GetBusinessIncomeOverview(BusinessIncomeOverviewRequest) returns (BusinessIncomeOverviewReply) {
    option (google.api.http) = {
      post: "/v1/data/business/income/overview",
      body: "*"
    };
  }
  rpc BusinessSingleChartQuery(BusinessSingleChartQueryRequest) returns (BusinessSingleChartQueryReply) {
    option (google.api.http) = {
      post: "/v1/data/business/chart",
      body: "*"
    };
  }

}

message BusinessIncomeOverviewRequest {
  string name = 1 [deprecated = true];
  int64 data_cp_id = 2;
  int64 data_channel_id = 3;
}

message DayData {
  double value = 1;
  string day = 2;
}

message BusinessIncomeOverviewReply {
  double yesterday_estimated_earnings = 1;
  double today_estimated_earnings = 2;
  double total_estimated_earnings = 3;
  repeated DayData days = 4;
}

message BusinessSingleChartQueryReply {
  repeated .google.protobuf.Struct rows = 1 [
    (.openapi.v3.property) = {description: "图表数据"}
  ];
}

message BusinessSingleChartQueryRequest {
  repeated string  dimensions = 1[
    (.openapi.v3.property) = {description: "细分"}
  ];
  repeated string metrics = 2;
  string filters = 3;
  int32 page_size = 4;
  int32 page = 5;
  Date start_date = 6;
  Date end_date = 7;
  repeated string order = 8;
  bool is_contrast = 9;
  Date contrast_start_date = 10;
  Date contrast_end_date = 11;
  repeated string filter_fields = 12;
  repeated string data_dimensions = 13;
  int64  data_channel_id = 14;
}

message GeneralOverviewDataRequest {
  string name = 1 [deprecated = true];
  Date start_date = 2;
  Date end_date = 3;
  repeated string metrics = 4;
  int64 data_cp_id = 5;
  int64 data_channel_id = 6;
}

message MonthData {
  double value = 1;
  string month = 2;
}


message BusinessGeneralOverviewDataReply {
  int32 site_count = 1;
  double estimated_earnings = 2;
  double impressions_rpm = 3;
  repeated MonthData estimated_earning_month = 4;
  repeated MonthData impressions_rpm_month = 5;
  double estimated_earning_mom = 6;
  double impressions_rpm_mom = 7;
  int32 page_views = 8;
  int32 impressions = 9;
}

message GeneralOverviewDataReply {
  int32 site_count = 1;
  double estimated_earnings = 2;
  double impressions_rpm = 3;
  repeated MonthData estimated_earning_month = 4;
  repeated MonthData impressions_rpm_month = 5;
  double estimated_earning_mom = 6;
  double impressions_rpm_mom = 7;
  double yesterday_estimated_earnings = 8;
  double today_estimated_earnings = 9;
  double total_estimated_earnings = 10;
}

message BusinessDataQueryReply {
  int32     total = 1;
  repeated .google.protobuf.Struct rows = 2;
  repeated .google.protobuf.Struct totals = 3;
  repeated .google.protobuf.Struct charts = 4;
}


message BusinessDataQueryRequest {
  repeated string  dimensions = 1;
  repeated string metrics = 2;
  string filters = 3;
  int32 page_size = 4;
  int32 page = 5;
  Date start_date = 6;
  Date end_date = 7;
  repeated string order = 8;
  bool is_contrast = 9;
  Date contrast_start_date = 10;
  Date contrast_end_date = 11;
  repeated string filter_fields = 12;
  int64 data_channel_id = 13;
}

message GameDataReply {
  message GameData {
    string app_id = 1;
    int64  play_count = 2;
    int64 yesterday_play_count = 3;
    repeated string top_countries = 4;
    int64 past_7d_play_count = 5;
    double total_estimated_earnings = 6;
  }
  repeated GameData data = 1;
}

message CpChannelDataQueryReply {
  int32     total = 1;
  repeated .google.protobuf.Struct rows = 2;
  repeated .google.protobuf.Struct totals = 3;
  repeated .google.protobuf.Struct charts = 4;
}

message CpChannelDataQueryRequest {
  repeated string  dimensions = 1;
  repeated string metrics = 2;
  string filters = 3;
  int32 page_size = 4;
  int32 page = 5;
  Date start_date = 6;
  Date end_date = 7;
  repeated string order = 8;
  bool is_contrast = 9;
  Date contrast_start_date = 10;
  Date contrast_end_date = 11;
  repeated string filter_fields = 12;
  int64 data_cp_id = 13;
}

//message BusinessFilterListRequest {
//  string name = 1;
//  string type = 2;
//}
//
//message BusinessFilterListReply {
//  repeated string  data = 1;
//}

//message CpFilterListRequest{
//  string name = 1;
//  string type = 2;
//}
//
//message CpFilterListReply {
//  repeated string  data = 1;
//}

message Date {
  int32 day = 1;
  int32 year = 2;
  int32 month = 3;
}



message CpGeneralOverviewDataReply {
  double estimated_earnings = 1;
  repeated MonthData estimated_earning_month = 2;
  repeated MonthData impressions_rpm_month = 3;
  double estimated_earning_mom = 4;
  double impressions_rpm_mom = 5;
  double total_estimated_earnings = 6;
  double impressions_rpm = 7;
}


message ExportChannelDataToExcelRequest {
  message Date {
    int32 day = 1;
    int32 year = 2;
    int32 month = 3;
  }
  repeated string  dimensions = 1;
  repeated string metrics = 2;
  string filters = 3;
  Date start_date = 4;
  Date end_date = 5;
  repeated string order = 6;
  bool is_zh = 7;
  int64 data_channel_id = 8;
  string  name = 9;
}

message ExportCpDataToExcelRequest {
  message Date {
    int32 day = 1;
    int32 year = 2;
    int32 month = 3;
  }
  repeated string  dimensions = 1;
  repeated string metrics = 2;
  string filters = 3;
  Date start_date = 4;
  Date end_date = 5;
  repeated string order = 6;
  bool is_zh = 7;
  int64 data_cp_id = 8;
  string  name = 9;
}


