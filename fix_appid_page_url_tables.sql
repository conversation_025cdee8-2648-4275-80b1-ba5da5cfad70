-- 修复page_url系列表中的appID字段
-- 根据FormatURL函数逻辑，从URL中正确提取游戏名称作为appID

-- 开始事务
BEGIN;

-- 1. 修复 page_urls 表（主表，无appID字段，跳过）

-- 2. 修复 page_url_infos 表
UPDATE page_url_infos 
SET app_id = CASE 
    WHEN page_url = 'https://pt.minigame.com/game/two-player-games/play/m' THEN 'two-player-games'
    WHEN page_url = 'https://play.minigame.com/en/m/game/skibydi-toilet/play/recommended2' THEN 'skibydi-toilet'
    WHEN page_url = 'https://play.minigame.com/en/m/game/aqua-funny-park/play/recommended2' THEN 'aqua-funny-park'
    WHEN page_url = 'https://play.minigame.com/en/m/game/ant-war/play/recommended' THEN 'ant-war'
    WHEN page_url = 'https://play.minigame.com/en/m/game/skibydi-toilet/play/recommended2' THEN 'skibydi-toilet'
    WHEN page_url = 'https://play.minigame.com/en/m/game/merge-arena/play/recommended' THEN 'merge-arena'
    WHEN page_url = 'https://play.minigame.com/en/m/game/dino-battle/play/recommended2' THEN 'dino-battle'
    WHEN page_url = 'https://televs.com/twa/game?appId=zombies-are-coming-tg' THEN 'zombies-are-coming-tg'
    ELSE app_id
END
WHERE page_url IN (
    'https://pt.minigame.com/game/two-player-games/play/m',
    'https://play.minigame.com/en/m/game/skibydi-toilet/play/recommended2',
    'https://play.minigame.com/en/m/game/aqua-funny-park/play/recommended2',
    'https://play.minigame.com/en/m/game/ant-war/play/recommended',
    'https://play.minigame.com/en/m/game/merge-arena/play/recommended',
    'https://play.minigame.com/en/m/game/dino-battle/play/recommended2',
    'https://televs.com/twa/game?appId=zombies-are-coming-tg'
);

-- 3. 修复 page_url_info_hkd 表
UPDATE page_url_info_hkd 
SET app_id = CASE 
    WHEN page_url = 'https://pt.minigame.com/game/two-player-games/play/m' THEN 'two-player-games'
    WHEN page_url = 'https://play.minigame.com/en/m/game/skibydi-toilet/play/recommended2' THEN 'skibydi-toilet'
    WHEN page_url = 'https://play.minigame.com/en/m/game/aqua-funny-park/play/recommended2' THEN 'aqua-funny-park'
    WHEN page_url = 'https://play.minigame.com/en/m/game/ant-war/play/recommended' THEN 'ant-war'
    WHEN page_url = 'https://play.minigame.com/en/m/game/skibydi-toilet/play/recommended2' THEN 'skibydi-toilet'
    WHEN page_url = 'https://play.minigame.com/en/m/game/merge-arena/play/recommended' THEN 'merge-arena'
    WHEN page_url = 'https://play.minigame.com/en/m/game/dino-battle/play/recommended2' THEN 'dino-battle'
    WHEN page_url = 'https://televs.com/twa/game?appId=zombies-are-coming-tg' THEN 'zombies-are-coming-tg'
    ELSE app_id
END
WHERE page_url IN (
    'https://pt.minigame.com/game/two-player-games/play/m',
    'https://play.minigame.com/en/m/game/skibydi-toilet/play/recommended2',
    'https://play.minigame.com/en/m/game/aqua-funny-park/play/recommended2',
    'https://play.minigame.com/en/m/game/ant-war/play/recommended',
    'https://play.minigame.com/en/m/game/merge-arena/play/recommended',
    'https://play.minigame.com/en/m/game/dino-battle/play/recommended2',
    'https://televs.com/twa/game?appId=zombies-are-coming-tg'
);

-- 4. 修复 page_url_ad_format_infos 表
UPDATE page_url_ad_format_infos 
SET app_id = CASE 
    WHEN page_url = 'https://pt.minigame.com/game/two-player-games/play/m' THEN 'two-player-games'
    WHEN page_url = 'https://play.minigame.com/en/m/game/skibydi-toilet/play/recommended2' THEN 'skibydi-toilet'
    WHEN page_url = 'https://play.minigame.com/en/m/game/aqua-funny-park/play/recommended2' THEN 'aqua-funny-park'
    WHEN page_url = 'https://play.minigame.com/en/m/game/ant-war/play/recommended' THEN 'ant-war'
    WHEN page_url = 'https://play.minigame.com/en/m/game/skibydi-toilet/play/recommended2' THEN 'skibydi-toilet'
    WHEN page_url = 'https://play.minigame.com/en/m/game/merge-arena/play/recommended' THEN 'merge-arena'
    WHEN page_url = 'https://play.minigame.com/en/m/game/dino-battle/play/recommended2' THEN 'dino-battle'
    WHEN page_url = 'https://televs.com/twa/game?appId=zombies-are-coming-tg' THEN 'zombies-are-coming-tg'
    ELSE app_id
END
WHERE page_url IN (
    'https://pt.minigame.com/game/two-player-games/play/m',
    'https://play.minigame.com/en/m/game/skibydi-toilet/play/recommended2',
    'https://play.minigame.com/en/m/game/aqua-funny-park/play/recommended2',
    'https://play.minigame.com/en/m/game/ant-war/play/recommended',
    'https://play.minigame.com/en/m/game/merge-arena/play/recommended',
    'https://play.minigame.com/en/m/game/dino-battle/play/recommended2',
    'https://televs.com/twa/game?appId=zombies-are-coming-tg'
);

-- 5. 修复 page_url_ad_format_info_hkd 表
UPDATE page_url_ad_format_info_hkd 
SET app_id = CASE 
    WHEN page_url = 'https://pt.minigame.com/game/two-player-games/play/m' THEN 'two-player-games'
    WHEN page_url = 'https://play.minigame.com/en/m/game/skibydi-toilet/play/recommended2' THEN 'skibydi-toilet'
    WHEN page_url = 'https://play.minigame.com/en/m/game/aqua-funny-park/play/recommended2' THEN 'aqua-funny-park'
    WHEN page_url = 'https://play.minigame.com/en/m/game/ant-war/play/recommended' THEN 'ant-war'
    WHEN page_url = 'https://play.minigame.com/en/m/game/skibydi-toilet/play/recommended2' THEN 'skibydi-toilet'
    WHEN page_url = 'https://play.minigame.com/en/m/game/merge-arena/play/recommended' THEN 'merge-arena'
    WHEN page_url = 'https://play.minigame.com/en/m/game/dino-battle/play/recommended2' THEN 'dino-battle'
    WHEN page_url = 'https://televs.com/twa/game?appId=zombies-are-coming-tg' THEN 'zombies-are-coming-tg'
    ELSE app_id
END
WHERE page_url IN (
    'https://pt.minigame.com/game/two-player-games/play/m',
    'https://play.minigame.com/en/m/game/skibydi-toilet/play/recommended2',
    'https://play.minigame.com/en/m/game/aqua-funny-park/play/recommended2',
    'https://play.minigame.com/en/m/game/ant-war/play/recommended',
    'https://play.minigame.com/en/m/game/merge-arena/play/recommended',
    'https://play.minigame.com/en/m/game/dino-battle/play/recommended2',
    'https://televs.com/twa/game?appId=zombies-are-coming-tg'
);

-- 显示修复结果统计
SELECT 'page_url_infos' as table_name, COUNT(*) as updated_rows 
FROM page_url_infos 
WHERE page_url IN (
    'https://pt.minigame.com/game/two-player-games/play/m',
    'https://play.minigame.com/en/m/game/skibydi-toilet/play/recommended2',
    'https://play.minigame.com/en/m/game/aqua-funny-park/play/recommended2',
    'https://play.minigame.com/en/m/game/ant-war/play/recommended',
    'https://play.minigame.com/en/m/game/merge-arena/play/recommended',
    'https://play.minigame.com/en/m/game/dino-battle/play/recommended2',
    'https://televs.com/twa/game?appId=zombies-are-coming-tg'
)

UNION ALL

SELECT 'page_url_info_hkd' as table_name, COUNT(*) as updated_rows 
FROM page_url_info_hkd 
WHERE page_url IN (
    'https://pt.minigame.com/game/two-player-games/play/m',
    'https://play.minigame.com/en/m/game/skibydi-toilet/play/recommended2',
    'https://play.minigame.com/en/m/game/aqua-funny-park/play/recommended2',
    'https://play.minigame.com/en/m/game/ant-war/play/recommended',
    'https://play.minigame.com/en/m/game/merge-arena/play/recommended',
    'https://play.minigame.com/en/m/game/dino-battle/play/recommended2',
    'https://televs.com/twa/game?appId=zombies-are-coming-tg'
)

UNION ALL

SELECT 'page_url_ad_format_infos' as table_name, COUNT(*) as updated_rows 
FROM page_url_ad_format_infos 
WHERE page_url IN (
    'https://pt.minigame.com/game/two-player-games/play/m',
    'https://play.minigame.com/en/m/game/skibydi-toilet/play/recommended2',
    'https://play.minigame.com/en/m/game/aqua-funny-park/play/recommended2',
    'https://play.minigame.com/en/m/game/ant-war/play/recommended',
    'https://play.minigame.com/en/m/game/merge-arena/play/recommended',
    'https://play.minigame.com/en/m/game/dino-battle/play/recommended2',
    'https://televs.com/twa/game?appId=zombies-are-coming-tg'
)

UNION ALL

SELECT 'page_url_ad_format_info_hkd' as table_name, COUNT(*) as updated_rows 
FROM page_url_ad_format_info_hkd 
WHERE page_url IN (
    'https://pt.minigame.com/game/two-player-games/play/m',
    'https://play.minigame.com/en/m/game/skibydi-toilet/play/recommended2',
    'https://play.minigame.com/en/m/game/aqua-funny-park/play/recommended2',
    'https://play.minigame.com/en/m/game/ant-war/play/recommended',
    'https://play.minigame.com/en/m/game/merge-arena/play/recommended',
    'https://play.minigame.com/en/m/game/dino-battle/play/recommended2',
    'https://televs.com/twa/game?appId=zombies-are-coming-tg'
);

-- 提交事务
COMMIT;

-- 验证修复结果的查询语句
-- 可以单独运行以检查修复是否成功
/*
SELECT page_url, app_id, sub_channel, page_type 
FROM page_url_infos 
WHERE page_url IN (
    'https://pt.minigame.com/game/two-player-games/play/m',
    'https://play.minigame.com/en/m/game/skibydi-toilet/play/recommended2',
    'https://play.minigame.com/en/m/game/aqua-funny-park/play/recommended2',
    'https://play.minigame.com/en/m/game/ant-war/play/recommended',
    'https://play.minigame.com/en/m/game/merge-arena/play/recommended',
    'https://play.minigame.com/en/m/game/dino-battle/play/recommended2',
    'https://televs.com/twa/game?appId=zombies-are-coming-tg'
)
ORDER BY page_url;
*/
